{"skeleton": {"hash": "CghXPcVmFPY", "spine": "4.2.40", "x": -139.66, "y": -272.97, "width": 281, "height": 395, "images": "./Images/Monster_Ufo/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "Cntr_control", "parent": "root"}, {"name": "Cntr", "parent": "Cntr_control", "y": -3.55, "icon": "arrows"}, {"name": "Body", "parent": "Cntr", "length": 39.67, "rotation": 90.2, "x": -0.28, "y": 23.63}, {"name": "Body2", "parent": "Body", "length": 39.67, "x": 39.67}, {"name": "Ear_R", "parent": "Body2", "length": 16.88, "rotation": -46.9, "x": 25.72, "y": -33.55}, {"name": "Ear_L", "parent": "Body2", "length": 15.69, "rotation": 47.35, "x": 27.93, "y": 33.36}, {"name": "Ear_R2", "parent": "Ear_R", "length": 16.88, "x": 16.88}, {"name": "Ear_L2", "parent": "Ear_L", "length": 15.69, "x": 15.69}, {"name": "Light", "parent": "Cntr", "rotation": 90, "y": -51.86, "icon": "triangle"}, {"name": "Eye_R", "parent": "Body", "rotation": -90.2, "x": 27.36, "y": -25.22, "icon": "eye"}, {"name": "Eye_L", "parent": "Body", "rotation": -90.2, "x": 27.54, "y": 23.78, "icon": "eye"}, {"name": "Pupil_L", "parent": "Eye_L", "length": 10, "x": -2, "y": -6.5}, {"name": "Pupil_R", "parent": "Eye_R", "length": 10, "x": 2.5, "y": -6.5}, {"name": "Mouth", "parent": "Body", "rotation": -90.2, "x": -2.06, "y": -0.61, "icon": "mouth"}, {"name": "Tongue", "parent": "Mouth", "rotation": 11.54, "x": 25.51, "y": 0.28}, {"name": "Tongue_tip", "parent": "Tongue", "rotation": -4.66, "x": -0.01, "y": -12.67}, {"name": "Tooth_01", "parent": "Mouth", "rotation": -11.42, "x": -44.99, "y": 7.45, "inherit": "noScaleOrReflection"}, {"name": "Tooth_02", "parent": "Mouth", "rotation": 21.44, "x": 43.76, "y": 6.96, "inherit": "noScaleOrReflection"}, {"name": "Tooth_03", "parent": "Mouth", "rotation": -4.57, "x": -25.37, "y": 2.86, "inherit": "noScaleOrReflection"}, {"name": "blot", "parent": "root", "rotation": -0.04, "icon": "flower"}, {"name": "blot_drops_control", "parent": "blot", "y": -10.68, "icon": "arrowDown"}, {"name": "Drops", "parent": "root", "scaleX": -1}, {"name": "blot_drop2", "parent": "Drops"}, {"name": "blot_drop_s1", "parent": "Drops"}, {"name": "blot_drop3", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop4", "parent": "Drops", "scaleX": 1.2553, "scaleY": 1.2553}, {"name": "blot_drop_s2", "parent": "Drops"}, {"name": "blot_drop5", "parent": "Drops"}, {"name": "blot_drop_s3", "parent": "Drops"}, {"name": "blot_drop6", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop_s4", "parent": "Drops"}, {"name": "Eyelid_R", "parent": "Eye_R", "x": 1.01, "y": -0.94, "icon": "arrowUpDown"}, {"name": "Eyelid_L", "parent": "Eye_L", "x": -0.88, "y": -1.01, "icon": "arrowUpDown"}, {"name": "Eyelid_L2", "parent": "Eye_L", "x": -0.88, "y": -14.89, "icon": "arrowUpDown"}, {"name": "Eyelid_R2", "parent": "Eye_R", "x": 1.01, "y": -15.41, "icon": "arrowUpDown"}], "slots": [{"name": "Light_Ray", "bone": "Light", "attachment": "Light_Ray"}, {"name": "Light_Ray2", "bone": "Light", "attachment": "Light_Ray"}, {"name": "Light_Glow", "bone": "Light", "attachment": "Light_Glow"}, {"name": "Ufo_Body_Outline", "bone": "Cntr", "attachment": "Ufo_Body_Outline"}, {"name": "Body_outline", "bone": "Cntr", "attachment": "Body_outline"}, {"name": "Ear_L_outline", "bone": "Ear_L", "attachment": "Ear_L_outline"}, {"name": "Ear_R_outline", "bone": "Ear_R", "attachment": "Ear_R_outline"}, {"name": "Ufo_Body", "bone": "Cntr", "attachment": "Ufo_Body"}, {"name": "Light_from_Ray", "bone": "Light", "attachment": "Light_from_Ray"}, {"name": "Ear_L", "bone": "Ear_L", "attachment": "Ear_L"}, {"name": "Ear_R", "bone": "Ear_R", "attachment": "Ear_R"}, {"name": "Body", "bone": "Cntr", "attachment": "Body"}, {"name": "Lips", "bone": "Mouth", "attachment": "Lips"}, {"name": "Eye_L", "bone": "Eye_L", "attachment": "Eye_L"}, {"name": "Eye_R", "bone": "Eye_R", "attachment": "Eye_R"}, {"name": "Pupil_L", "bone": "Pupil_L", "attachment": "Pupil_L"}, {"name": "Pupil_R", "bone": "Pupil_R", "attachment": "Pupil_R"}, {"name": "Eye_Eyelid_R", "bone": "Eye_R", "attachment": "Eyelid_L_cosed"}, {"name": "Eye_Eyelid_R2", "bone": "Eye_R", "attachment": "Eyelid_L_cosed"}, {"name": "Eye_Eyelid_L", "bone": "Eye_L", "attachment": "Eyelid_L_cosed"}, {"name": "Eye_Eyelid_L2", "bone": "Eye_L", "attachment": "Eyelid_L_cosed"}, {"name": "Tongue", "bone": "Tongue", "attachment": "Tongue"}, {"name": "Mouth", "bone": "Mouth", "attachment": "Mouth"}, {"name": "Tooth_01", "bone": "Tooth_01", "attachment": "Tooth_01"}, {"name": "Tooth_02", "bone": "Tooth_02", "attachment": "Tooth_02"}, {"name": "Tooth_03", "bone": "Tooth_03", "attachment": "Tooth_03"}, {"name": "blot", "bone": "blot"}, {"name": "blot_drop2", "bone": "blot_drop2"}, {"name": "blot_drop_s1", "bone": "blot_drop_s1"}, {"name": "blot_drop3", "bone": "blot_drop3"}, {"name": "blot_drop4", "bone": "blot_drop4"}, {"name": "blot_drop5", "bone": "blot_drop_s2"}, {"name": "blot_drop6", "bone": "blot_drop5"}, {"name": "blot_drop_s2", "bone": "blot_drop_s3"}, {"name": "blot_drop7", "bone": "blot_drop6"}, {"name": "blot_drop8", "bone": "blot_drop_s4"}], "skins": [{"name": "default", "attachments": {"blot": {"blot": {"type": "mesh", "color": "61e82fff", "uvs": [0.16853, 0.04132, 0.20427, 0.04133, 0.235, 0.05776, 0.2567, 0.08503, 0.27019, 0.11893, 0.28083, 0.15284, 0.29008, 0.16859, 0.40306, 0.14175, 0.52511, 0.14243, 0.63958, 0.17341, 0.65099, 0.15069, 0.66785, 0.11583, 0.69938, 0.10078, 0.74656, 0.0982, 0.78663, 0.11447, 0.81478, 0.14412, 0.82679, 0.17844, 0.8274, 0.22268, 0.80551, 0.24979, 0.78146, 0.26764, 0.77045, 0.28539, 0.79007, 0.31899, 0.82203, 0.31695, 0.86186, 0.3347, 0.88752, 0.35974, 0.90407, 0.39632, 0.9038, 0.43154, 0.89703, 0.47361, 0.87282, 0.49811, 0.84367, 0.51769, 0.83976, 0.57695, 0.82721, 0.62819, 0.85091, 0.63399, 0.88661, 0.64029, 0.9085, 0.64943, 0.92589, 0.66799, 0.92962, 0.68768, 0.92352, 0.71185, 0.90345, 0.72913, 0.88078, 0.7345, 0.85869, 0.73204, 0.84233, 0.71683, 0.82827, 0.69353, 0.80387, 0.68283, 0.78923, 0.70054, 0.77272, 0.72456, 0.75456, 0.74543, 0.74252, 0.76337, 0.75174, 0.78241, 0.7707, 0.79541, 0.78966, 0.80098, 0.80915, 0.81654, 0.82604, 0.83018, 0.83576, 0.85319, 0.83587, 0.87721, 0.83318, 0.90725, 0.81778, 0.92725, 0.79106, 0.94395, 0.74837, 0.94704, 0.71572, 0.94051, 0.69219, 0.91664, 0.67776, 0.88946, 0.6729, 0.86403, 0.66254, 0.84197, 0.64012, 0.83666, 0.62051, 0.84647, 0.61585, 0.88202, 0.60876, 0.90761, 0.58596, 0.93801, 0.55205, 0.95614, 0.51472, 0.9587, 0.47446, 0.95391, 0.44301, 0.93005, 0.42458, 0.90092, 0.4098, 0.87756, 0.3531, 0.86126, 0.31071, 0.8462, 0.27287, 0.82849, 0.23944, 0.82863, 0.21336, 0.82462, 0.19205, 0.80713, 0.1763, 0.78103, 0.17544, 0.7546, 0.15225, 0.73275, 0.12704, 0.75153, 0.09896, 0.78407, 0.07158, 0.81002, 0.02474, 0.81038, 0.00898, 0.79541, 0.00014, 0.76864, 0.0126, 0.74387, 0.02727, 0.723, 0.06373, 0.71112, 0.1024, 0.70314, 0.11577, 0.6871, 0.11344, 0.66789, 0.10013, 0.64319, 0.07288, 0.63049, 0.0481, 0.60493, 0.0395, 0.57932, 0.04017, 0.53824, 0.05265, 0.51498, 0.04012, 0.49584, 0.02776, 0.45571, 0.02776, 0.39855, 0.042, 0.36049, 0.07071, 0.32161, 0.10651, 0.29848, 0.14993, 0.28593, 0.17204, 0.25511, 0.157, 0.23649, 0.12839, 0.21904, 0.09915, 0.19856, 0.07437, 0.1592, 0.07434, 0.10411, 0.09293, 0.06764, 0.12711, 0.04525, 0.7637, 0.87399, 0.51839, 0.89337, 0.06159, 0.75434, 0.88018, 0.68481, 0.82945, 0.40232, 0.45472, 0.47982, 0.74434, 0.1825], "triangles": [123, 12, 13, 0, 112, 113, 111, 0, 4, 3, 4, 0, 119, 89, 90, 119, 88, 89, 85, 119, 84, 116, 113, 114, 123, 13, 14, 117, 53, 54, 55, 117, 54, 40, 41, 120, 56, 117, 55, 59, 60, 117, 56, 57, 117, 57, 58, 117, 69, 70, 118, 86, 87, 119, 86, 119, 85, 70, 71, 118, 71, 72, 118, 69, 118, 68, 117, 58, 59, 120, 39, 40, 38, 39, 120, 38, 120, 37, 37, 120, 36, 120, 35, 36, 120, 34, 35, 117, 51, 52, 117, 52, 53, 117, 50, 51, 2, 0, 1, 3, 0, 2, 115, 116, 114, 116, 0, 113, 87, 88, 119, 97, 98, 99, 96, 97, 99, 122, 79, 80, 79, 122, 78, 77, 78, 122, 122, 96, 101, 111, 112, 0, 123, 14, 15, 11, 12, 123, 95, 96, 122, 93, 94, 83, 84, 93, 83, 119, 91, 92, 90, 91, 119, 119, 93, 84, 93, 119, 92, 63, 64, 47, 48, 63, 47, 62, 63, 48, 117, 48, 49, 117, 49, 50, 62, 48, 117, 65, 118, 74, 61, 62, 117, 66, 118, 65, 73, 74, 118, 67, 118, 66, 60, 61, 117, 72, 73, 118, 68, 118, 67, 42, 31, 32, 122, 83, 95, 46, 122, 45, 82, 83, 122, 47, 122, 46, 81, 82, 122, 122, 80, 81, 77, 122, 76, 47, 64, 122, 76, 122, 75, 64, 65, 122, 122, 74, 75, 65, 74, 122, 101, 99, 100, 31, 44, 122, 123, 15, 16, 123, 16, 17, 4, 110, 111, 110, 4, 5, 110, 5, 6, 18, 123, 17, 109, 110, 6, 19, 123, 18, 20, 123, 19, 121, 22, 23, 121, 23, 24, 121, 24, 25, 26, 121, 25, 27, 121, 26, 122, 7, 8, 122, 8, 9, 6, 7, 122, 109, 6, 122, 108, 109, 122, 10, 11, 123, 9, 10, 123, 20, 122, 9, 20, 9, 123, 105, 103, 104, 101, 102, 103, 28, 121, 27, 105, 107, 103, 101, 103, 108, 106, 107, 105, 108, 103, 107, 101, 108, 122, 29, 121, 28, 121, 122, 21, 21, 22, 121, 122, 121, 29, 122, 20, 21, 29, 30, 122, 31, 122, 30, 101, 96, 99, 45, 122, 44, 120, 32, 33, 120, 33, 34, 83, 94, 95, 42, 32, 120, 43, 31, 42, 43, 44, 31, 41, 42, 120], "vertices": [2, 20, -90.99, 143.28, 0.02155, 21, -90.99, 153.95, 0.97845, 2, 20, -79.49, 143.07, 0.00946, 21, -79.49, 153.74, 0.99054, 2, 20, -69.52, 139.98, 0.153, 21, -69.52, 150.66, 0.847, 2, 20, -62.4, 134.98, 0.39795, 21, -62.4, 145.65, 0.60205, 2, 20, -57.89, 128.81, 0.70584, 21, -57.89, 139.49, 0.29416, 2, 20, -54.34, 121.24, 0.93146, 21, -54.34, 131.92, 0.06854, 2, 20, -51.35, 116.31, 0.95362, 21, -51.35, 126.99, 0.04638, 2, 20, -15.08, 121.98, 0.7559, 21, -15.08, 132.66, 0.2441, 2, 20, 24.22, 121.85, 0.76179, 21, 24.22, 132.53, 0.23821, 2, 20, 61.07, 111.05, 0.74063, 21, 61.07, 121.73, 0.25937, 2, 20, 64.69, 117.07, 0.64513, 21, 64.69, 127.75, 0.35487, 2, 20, 69.97, 124.08, 0.36729, 21, 69.97, 134.75, 0.63271, 2, 20, 80.03, 126.27, 0.19886, 21, 80.03, 136.94, 0.80114, 2, 20, 95.17, 125.47, 0.10171, 21, 95.17, 136.15, 0.89829, 2, 20, 108.13, 121.61, 0.19647, 21, 108.13, 132.29, 0.80353, 2, 20, 117.28, 114.46, 0.36288, 21, 117.28, 125.14, 0.63712, 2, 20, 121.2, 104.59, 0.46208, 21, 121.2, 115.27, 0.53792, 2, 20, 121.47, 91.9, 0.59174, 21, 121.47, 102.58, 0.40826, 2, 20, 114.5, 85.31, 0.74078, 21, 114.5, 95.99, 0.25922, 2, 20, 106.82, 81.47, 0.86799, 21, 106.82, 92.15, 0.13201, 1, 20, 103.35, 77.75, 1, 1, 20, 109.67, 66.42, 1, 2, 20, 119.95, 66.83, 0.98349, 21, 119.95, 77.5, 0.01651, 2, 20, 132.69, 58.02, 0.81787, 21, 132.69, 68.69, 0.18213, 2, 20, 140.89, 47.62, 0.70315, 21, 140.89, 58.29, 0.29685, 2, 20, 146.18, 34.02, 0.62876, 21, 146.18, 44.69, 0.37124, 2, 20, 146.08, 21.81, 0.60882, 21, 146.08, 32.49, 0.39118, 2, 20, 143.91, 8, 0.63067, 21, 143.91, 18.68, 0.36933, 2, 20, 136.18, 1.68, 0.74414, 21, 136.18, 12.36, 0.25586, 2, 20, 126.87, -2.46, 0.88743, 21, 126.87, 8.21, 0.11257, 1, 20, 125.67, -20.51, 1, 2, 20, 121.59, -39.07, 0.92415, 21, 121.59, -28.4, 0.07585, 2, 20, 129.18, -42.19, 0.85626, 21, 129.18, -31.51, 0.14374, 2, 20, 140.57, -47.81, 0.65141, 21, 140.57, -37.14, 0.34859, 2, 20, 147.51, -54.28, 0.45337, 21, 147.51, -43.6, 0.54663, 2, 20, 152.98, -64.45, 0.22414, 21, 152.98, -53.77, 0.77586, 2, 20, 154.13, -72.72, 0.12863, 21, 154.13, -62.04, 0.87137, 2, 20, 152.13, -81.96, 0.06435, 21, 152.13, -71.29, 0.93565, 2, 20, 145.68, -87.59, 0.0759, 21, 145.68, -76.91, 0.9241, 2, 20, 138.4, -88.76, 0.11319, 21, 138.4, -78.09, 0.88681, 2, 20, 131.35, -86.06, 0.22246, 21, 131.35, -75.39, 0.77754, 2, 20, 126.18, -77.59, 0.41853, 21, 126.18, -66.91, 0.58147, 2, 20, 121.79, -65.57, 0.66246, 21, 121.79, -54.89, 0.33754, 2, 20, 114.11, -56.29, 0.99426, 21, 114.11, -45.61, 0.00574, 2, 20, 109.4, -62.28, 0.99314, 21, 109.4, -51.6, 0.00686, 2, 20, 104.08, -70.39, 0.99213, 21, 104.08, -59.71, 0.00787, 2, 20, 98.23, -77.33, 0.99754, 21, 98.23, -66.65, 0.00246, 2, 20, 94.15, -90, 0.60983, 21, 94.15, -79.33, 0.39017, 2, 20, 97.15, -95.23, 0.67939, 21, 97.15, -84.55, 0.32061, 2, 20, 103.21, -101.28, 0.58167, 21, 103.21, -90.6, 0.41833, 2, 20, 109.23, -105.58, 0.43988, 21, 109.23, -94.91, 0.56012, 2, 20, 115.45, -112.77, 0.3264, 21, 115.45, -102.09, 0.6736, 2, 20, 120.82, -119.39, 0.20757, 21, 120.82, -108.72, 0.79243, 2, 20, 123.96, -126.84, 0.2256, 21, 123.96, -116.16, 0.7744, 2, 20, 124.06, -133.1, 0.33257, 21, 124.06, -122.43, 0.66743, 2, 20, 123.16, -144.25, 0.27292, 21, 123.16, -133.57, 0.72708, 2, 20, 118.12, -153.42, 0.13068, 21, 118.12, -142.74, 0.86932, 1, 21, 109.45, -150.61, 1, 1, 21, 95.7, -151.64, 1, 2, 20, 85.24, -158.48, 0.09602, 21, 85.24, -147.8, 0.90398, 2, 20, 77.82, -145.35, 0.39368, 21, 77.82, -134.67, 0.60632, 2, 20, 73.34, -130.93, 0.70124, 21, 73.34, -120.26, 0.29876, 2, 20, 71.87, -119.48, 0.86966, 21, 71.87, -108.81, 0.13034, 2, 20, 68.58, -110.66, 0.95079, 21, 68.58, -99.99, 0.04921, 2, 20, 61.37, -108.46, 0.97473, 21, 61.37, -97.79, 0.02527, 2, 20, 55.04, -112.19, 0.95012, 21, 55.04, -101.51, 0.04988, 2, 20, 53.48, -126.06, 0.83978, 21, 53.48, -115.38, 0.16022, 2, 20, 51.15, -136.24, 0.7483, 21, 51.15, -125.57, 0.2517, 2, 20, 43.68, -150.59, 0.50798, 21, 43.68, -139.92, 0.49202, 2, 20, 32.6, -161.92, 0.20256, 21, 32.6, -151.25, 0.79744, 2, 20, 20.47, -166.16, 0.00552, 21, 20.47, -155.48, 0.99448, 1, 21, 7.5, -153.96, 1, 2, 20, -2.46, -151.47, 0.29991, 21, -2.46, -140.79, 0.70009, 2, 20, -8.22, -136.19, 0.61942, 21, -8.22, -125.52, 0.38058, 2, 20, -12.92, -126.15, 0.74609, 21, -12.92, -115.48, 0.25391, 2, 20, -31.13, -119.46, 0.81636, 21, -31.13, -108.79, 0.18364, 2, 20, -44.88, -117.49, 0.63451, 21, -44.88, -106.82, 0.36549, 2, 20, -57.09, -112.24, 0.59263, 21, -57.09, -101.56, 0.40737, 2, 20, -67.91, -113.88, 0.49938, 21, -67.91, -103.21, 0.50062, 2, 20, -76.33, -113.38, 0.44983, 21, -76.33, -102.7, 0.55017, 2, 20, -83.13, -105.64, 0.5575, 21, -83.13, -94.97, 0.4425, 2, 20, -88.06, -92.49, 0.81245, 21, -88.06, -81.82, 0.18755, 2, 20, -88.25, -80.62, 0.9861, 21, -88.25, -69.94, 0.0139, 2, 20, -95.71, -73.18, 0.99035, 21, -95.71, -62.51, 0.00965, 2, 20, -104.08, -87.42, 0.52724, 21, -104.08, -76.75, 0.47276, 1, 21, -113.41, -96.72, 1, 1, 21, -122.23, -105.47, 1, 1, 21, -137.31, -105.59, 1, 2, 20, -142.34, -109.76, 0.08531, 21, -142.34, -99.09, 0.91469, 2, 20, -145.05, -96.47, 0.33535, 21, -145.05, -85.79, 0.66465, 2, 20, -140.97, -85.96, 0.46148, 21, -140.97, -75.29, 0.53852, 2, 20, -136.19, -77.3, 0.55661, 21, -136.19, -66.63, 0.44339, 2, 20, -124.38, -71.14, 0.68332, 21, -124.38, -60.46, 0.31668, 2, 20, -111.88, -66.88, 0.77501, 21, -111.88, -56.2, 0.22499, 2, 20, -107.54, -60.37, 0.83963, 21, -107.54, -49.7, 0.16037, 2, 20, -108.21, -51.16, 0.37143, 21, -108.21, -40.48, 0.62857, 2, 20, -112.82, -53.09, 0.39981, 21, -112.82, -42.41, 0.60019, 2, 20, -121.62, -49.9, 0.33627, 21, -121.62, -39.22, 0.66373, 2, 20, -129.52, -38.47, 0.50107, 21, -129.52, -27.79, 0.49893, 2, 20, -132.22, -27.86, 0.61634, 21, -132.22, -17.19, 0.38366, 2, 20, -131.98, -13.28, 0.65964, 21, -131.98, -2.61, 0.34036, 2, 20, -127.97, -5.53, 0.65438, 21, -127.97, 5.14, 0.34562, 2, 20, -131.99, 1.26, 0.6746, 21, -131.99, 11.94, 0.3254, 2, 20, -135.85, 18.78, 0.90838, 21, -135.85, 29.46, 0.09162, 2, 20, -135.9, 36.34, 0.80863, 21, -135.9, 47.01, 0.19137, 2, 20, -131.37, 47.53, 0.71293, 21, -131.37, 58.21, 0.28707, 2, 20, -122.14, 60.19, 0.68699, 21, -122.14, 70.87, 0.31301, 2, 20, -110.55, 69.59, 0.78083, 21, -110.55, 80.26, 0.21917, 2, 20, -96.49, 76.61, 0.94418, 21, -96.49, 87.28, 0.05582, 2, 20, -89.51, 82.38, 0.67426, 21, -89.51, 93.06, 0.32574, 2, 20, -94.35, 88.74, 0.67915, 21, -94.35, 99.42, 0.32085, 2, 20, -103.64, 92.11, 0.53234, 21, -103.64, 102.79, 0.46766, 2, 20, -113.05, 99.34, 0.55156, 21, -113.05, 110.02, 0.44844, 2, 20, -121.17, 108.12, 0.28889, 21, -121.17, 118.79, 0.71111, 2, 20, -121.32, 122.36, 0.03582, 21, -121.32, 133.04, 0.96418, 1, 21, -115.35, 144.71, 1, 1, 21, -104.35, 152.26, 1, 2, 20, 101.02, -125.51, 0.71328, 21, 101.02, -114.84, 0.28672, 2, 20, 22.04, -131.9, 0.72168, 21, 22.04, -121.23, 0.27832, 2, 20, -125.18, -89.07, 0.48636, 21, -125.18, -78.39, 0.51364, 2, 20, 138.41, -65.71, 0.48188, 21, 138.41, -55.04, 0.51812, 2, 20, 122.3, 36.78, 0.90859, 21, 122.3, 47.45, 0.09141, 2, 20, 1.31, 0.5, 0.31422, 21, 1.31, 11.18, 0.68578, 2, 20, 94.84, 109.27, 0.81578, 21, 94.84, 119.95, 0.18422], "hull": 117, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 34, 36, 58, 60, 60, 62, 72, 74, 152, 154, 172, 174, 174, 176, 176, 178, 210, 212, 218, 220, 224, 226, 226, 228, 228, 230, 230, 232, 166, 168, 168, 170, 170, 172, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 200, 202, 202, 204, 196, 198, 198, 200, 158, 160, 160, 162, 154, 156, 156, 158, 162, 164, 164, 166, 146, 148, 142, 144, 144, 146, 140, 142, 134, 136, 132, 134, 136, 138, 138, 140, 128, 130, 130, 132, 126, 128, 122, 124, 124, 126, 118, 120, 120, 122, 114, 116, 116, 118, 110, 112, 112, 114, 108, 110, 178, 180, 180, 182, 102, 104, 100, 102, 94, 96, 104, 106, 106, 108, 96, 98, 98, 100, 90, 92, 92, 94, 86, 88, 88, 90, 82, 84, 84, 86, 74, 76, 76, 78, 78, 80, 80, 82, 68, 70, 70, 72, 62, 64, 64, 66, 66, 68, 54, 56, 56, 58, 50, 52, 52, 54, 46, 48, 48, 50, 44, 46, 244, 60, 40, 42, 42, 44, 36, 38, 38, 40, 30, 32, 32, 34, 26, 28, 28, 30, 18, 20, 20, 22, 40, 244, 148, 150, 150, 152, 156, 244, 160, 244, 162, 244, 158, 244, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 232, 220, 222, 222, 224, 216, 218, 212, 214, 214, 216, 208, 210, 206, 208, 204, 206, 244, 202], "width": 322, "height": 337}}, "blot_drop2": {"blot_drop2": {"color": "61e82fff", "width": 63, "height": 52}}, "blot_drop3": {"blot_drop2": {"color": "61e82fff", "width": 63, "height": 52}}, "blot_drop4": {"blot_drop2": {"color": "61e82fff", "width": 63, "height": 52}}, "blot_drop5": {"blot_drop1": {"color": "61e82fff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop6": {"blot_drop2": {"color": "61e82fff", "width": 63, "height": 52}}, "blot_drop7": {"blot_drop2": {"color": "61e82fff", "width": 63, "height": 52}}, "blot_drop8": {"blot_drop1": {"color": "61e82fff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s1": {"blot_drop1": {"color": "61e82fff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s2": {"blot_drop1": {"color": "61e82fff", "rotation": -0.04, "width": 30, "height": 29}}, "Body": {"Body": {"type": "mesh", "uvs": [0.49818, 0.00489, 0.65486, 0.02245, 0.74829, 0.10605, 0.8041, 0.18625, 0.84637, 0.28275, 0.875, 0.38586, 0.90362, 0.48897, 0.93191, 0.60177, 0.9602, 0.71458, 0.9801, 0.82492, 1, 0.93526, 0.97283, 0.96558, 0.52306, 0.99741, 0.06371, 0.97498, 0, 0.92271, 0.02199, 0.82671, 0.04398, 0.73072, 0.06597, 0.63473, 0.08796, 0.53874, 0.11634, 0.43351, 0.14473, 0.32828, 0.1815, 0.23113, 0.22946, 0.12937, 0.35509, 0.0127], "triangles": [11, 12, 9, 14, 15, 13, 12, 13, 16, 11, 9, 10, 15, 16, 13, 9, 12, 8, 12, 21, 0, 0, 21, 22, 22, 23, 0, 4, 0, 3, 3, 0, 2, 2, 0, 1, 12, 0, 4, 20, 21, 12, 12, 4, 5, 12, 16, 17, 17, 18, 12, 18, 19, 12, 19, 20, 12, 12, 7, 8, 12, 6, 7, 12, 5, 6], "vertices": [1, 4, 39.84, -0.66, 1, 2, 3, 77.86, -21.03, 0.03671, 4, 38.18, -21.03, 0.96329, 2, 3, 70.29, -33.15, 0.14244, 4, 30.62, -33.15, 0.85756, 2, 3, 63.05, -40.38, 0.26115, 4, 23.37, -40.38, 0.73885, 2, 3, 54.34, -45.84, 0.40572, 4, 14.67, -45.84, 0.59428, 2, 3, 45.05, -49.53, 0.56617, 4, 5.38, -49.53, 0.43383, 2, 3, 35.75, -53.22, 0.72009, 4, -3.92, -53.22, 0.27991, 3, 3, 25.59, -56.86, 0.81711, 4, -14.08, -56.86, 0.15146, 2, 56.49, 49.42, 0.03143, 3, 3, 15.42, -60.5, 0.79396, 4, -24.25, -60.5, 0.06797, 2, 60.17, 39.26, 0.13808, 3, 3, 5.48, -63.05, 0.61476, 4, -34.19, -63.05, 0.02361, 2, 62.76, 29.33, 0.36162, 1, 2, 65.34, 19.4, 1, 1, 2, 61.81, 16.67, 1, 1, 2, 3.34, 13.81, 1, 1, 2, -56.38, 15.83, 1, 1, 2, -64.66, 20.53, 1, 3, 3, 5.77, 61.5, 0.61945, 4, -33.91, 61.5, 0.01617, 2, -61.8, 29.17, 0.36438, 3, 3, 14.39, 58.61, 0.80634, 4, -25.28, 58.61, 0.05343, 2, -58.94, 37.81, 0.14023, 3, 3, 23.02, 55.72, 0.84851, 4, -16.65, 55.72, 0.11886, 2, -56.08, 46.45, 0.03263, 2, 3, 31.65, 52.84, 0.78421, 4, -8.02, 52.84, 0.21579, 2, 3, 41.11, 49.11, 0.63724, 4, 1.44, 49.11, 0.36276, 2, 3, 50.57, 45.39, 0.46483, 4, 10.9, 45.39, 0.53517, 2, 3, 59.29, 40.58, 0.29917, 4, 19.62, 40.58, 0.70083, 2, 3, 68.43, 34.31, 0.15381, 4, 28.76, 34.31, 0.84619, 2, 3, 78.87, 17.94, 0.01545, 4, 39.2, 17.94, 0.98455], "hull": 24, "edges": [20, 22, 26, 28, 44, 46, 0, 46, 0, 2, 2, 4, 22, 24, 24, 26, 12, 14, 14, 16, 8, 10, 10, 12, 16, 18, 18, 20, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 28, 30, 30, 32, 4, 6, 6, 8], "width": 130, "height": 90}}, "Body_outline": {"Body_outline": {"type": "mesh", "uvs": [0.57115, 0, 0.75437, 0.08053, 0.85921, 0.28457, 0.90614, 0.4599, 0.95307, 0.63524, 0.97482, 0.73555, 0.99656, 0.83586, 0.9946, 0.90751, 0.95261, 0.97931, 0.40877, 1, 0, 0.95239, 0, 0.82965, 0.02331, 0.7391, 0.04662, 0.64854, 0.09324, 0.46743, 0.14623, 0.27676, 0.25764, 0.07173, 0.43759, 0], "triangles": [5, 8, 9, 9, 11, 12, 12, 13, 9, 7, 8, 6, 5, 9, 4, 9, 10, 11, 6, 8, 5, 2, 0, 1, 0, 2, 9, 9, 17, 0, 9, 15, 17, 17, 15, 16, 9, 2, 3, 13, 14, 9, 14, 15, 9, 4, 9, 3], "vertices": [2, 3, 85.91, -11.03, 0.0013, 4, 46.24, -11.03, 0.9987, 2, 3, 77.61, -37.01, 0.09965, 4, 37.93, -37.01, 0.90035, 2, 3, 56.74, -51.83, 0.37634, 4, 17.07, -51.83, 0.62366, 2, 3, 38.83, -58.43, 0.65148, 4, -0.84, -58.43, 0.34852, 2, 3, 20.93, -65.03, 0.86408, 4, -18.75, -65.03, 0.13592, 3, 3, 10.68, -68.08, 0.43204, 4, -28.99, -68.08, 0.06796, 2, 67.77, 34.55, 0.5, 1, 2, 70.85, 24.32, 1, 1, 2, 70.58, 17.01, 1, 1, 2, 64.61, 9.69, 1, 1, 2, -12.61, 7.58, 1, 1, 2, -70.66, 12.43, 1, 1, 2, -70.66, 24.95, 1, 3, 3, 10.8, 67.03, 0.45283, 4, -28.87, 67.03, 0.04717, 2, -67.35, 34.19, 0.5, 2, 3, 20.03, 63.69, 0.90565, 4, -19.64, 63.69, 0.09435, 2, 3, 38.48, 57.01, 0.70737, 4, -1.2, 57.01, 0.29263, 2, 3, 57.9, 49.41, 0.40938, 4, 18.23, 49.41, 0.59062, 2, 3, 78.75, 33.52, 0.10844, 4, 39.08, 33.52, 0.89156, 2, 3, 85.98, 7.94, 0.00247, 4, 46.31, 7.94, 0.99753], "hull": 18, "edges": [0, 34, 0, 2, 12, 14, 14, 16, 16, 18, 20, 22, 32, 34, 26, 28, 18, 20, 28, 30, 30, 32, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 22, 24, 24, 26], "width": 142, "height": 102}}, "Ear_L": {"Ear_L": {"type": "mesh", "uvs": [0.41264, 0, 0.52944, 0.11118, 0.53503, 0.27684, 0.59101, 0.41066, 0.73718, 0.46491, 0.89396, 0.45815, 1, 0.55909, 1, 0.71502, 0.74586, 0.98121, 0.52452, 0.98419, 0.44296, 0.89894, 0.49663, 0.69351, 0.43093, 0.56235, 0.30025, 0.51607, 0.10855, 0.54938, 0, 0.43591, 0, 0.26724, 0.24803, 0], "triangles": [4, 7, 11, 5, 6, 7, 8, 9, 11, 9, 10, 11, 8, 11, 7, 7, 4, 5, 11, 3, 4, 11, 12, 3, 3, 12, 2, 12, 13, 2, 14, 15, 13, 13, 16, 17, 1, 2, 0, 0, 2, 17, 17, 2, 13, 13, 15, 16], "vertices": [2, 8, 12.23, -13.04, 0.99478, 6, 27.92, -13.04, 0.00522, 2, 8, 4.82, -12.89, 0.87893, 6, 20.5, -12.89, 0.12107, 2, 8, -0.52, -7.44, 0.55235, 6, 15.17, -7.44, 0.44765, 2, 8, -6.57, -4.64, 0.22423, 6, 9.12, -4.64, 0.77577, 3, 8, -13.22, -7.34, 0.00675, 6, 2.47, -7.34, 0.65992, 4, 35, 30.21, 0.33333, 1, 4, 35.29, 22.99, 1, 1, 4, 30.63, 18.13, 1, 1, 4, 23.45, 18.16, 1, 1, 4, 11.25, 29.89, 1, 1, 4, 11.15, 40.07, 1, 1, 4, 15.08, 43.81, 1, 3, 8, -12.15, 7.89, 0.01716, 6, 3.54, 7.89, 0.6495, 4, 24.53, 41.31, 0.33333, 2, 8, -5.85, 5.48, 0.24613, 6, 9.84, 5.48, 0.75387, 2, 8, 0.02, 7.96, 0.57676, 6, 15.71, 7.96, 0.42324, 2, 8, 5.5, 15.05, 0.89293, 6, 21.19, 15.05, 0.10707, 2, 8, 12.71, 14.56, 0.99729, 6, 28.39, 14.56, 0.00271, 1, 8, 17.94, 8.84, 1, 1, 8, 17.82, -7.93, 1], "hull": 18, "edges": [0, 34, 12, 14, 14, 16, 16, 18, 30, 32, 32, 34, 0, 2, 10, 12, 18, 20, 28, 30, 6, 8, 8, 10, 2, 4, 4, 6, 24, 26, 26, 28, 20, 22, 22, 24], "width": 46, "height": 46}}, "Ear_L_outline": {"Ear_L_outline": {"type": "mesh", "uvs": [0.46481, 0, 0.60277, 0.12653, 0.64722, 0.35246, 0.85366, 0.3659, 1, 0.51056, 0.95097, 0.81886, 0.63081, 1, 0.50013, 1, 0.35932, 0.86132, 0.37141, 0.6336, 0.1321, 0.63098, 0, 0.50102, 0.02487, 0.21252, 0.32612, 0], "triangles": [6, 8, 9, 3, 5, 2, 5, 6, 9, 5, 3, 4, 7, 8, 6, 2, 5, 9, 13, 2, 9, 9, 10, 12, 11, 12, 10, 1, 13, 0, 2, 13, 1, 9, 12, 13], "vertices": [2, 8, 14.82, -18.8, 0.90145, 6, 30.51, -18.8, 0.09855, 2, 8, 3.96, -18.79, 0.60502, 6, 19.65, -18.79, 0.39498, 3, 8, -6.78, -10.86, 0.28785, 6, 8.91, -10.86, 0.37882, 4, 41.95, 32.55, 0.33333, 1, 4, 41.13, 20.58, 1, 1, 4, 32.71, 12.12, 1, 1, 4, 14.84, 15.03, 1, 1, 4, 4.4, 33.64, 1, 1, 4, 4.43, 41.22, 1, 1, 4, 12.5, 49.36, 1, 3, 8, -5.98, 11.97, 0.43448, 6, 9.71, 11.97, 0.23219, 4, 25.7, 48.61, 0.33333, 2, 8, 4.36, 21.22, 0.76781, 6, 20.05, 21.22, 0.23219, 2, 8, 15.1, 20.83, 0.98996, 6, 30.79, 20.83, 0.01004, 2, 8, 25.33, 7.51, 0.99785, 6, 41.02, 7.51, 0.00215, 2, 8, 20.76, -13.38, 0.98169, 6, 36.45, -13.38, 0.01831], "hull": 14, "edges": [0, 26, 10, 12, 12, 14, 24, 26, 0, 2, 6, 8, 14, 16, 20, 22, 16, 18, 18, 20, 2, 4, 4, 6, 8, 10, 22, 24], "width": 58, "height": 58}}, "Ear_R": {"Ear_R": {"type": "mesh", "uvs": [0.72027, 0, 1, 0.28276, 0.99585, 0.46507, 0.85414, 0.53139, 0.68798, 0.51581, 0.5426, 0.57362, 0.4929, 0.72831, 0.52907, 0.90239, 0.47583, 1, 0.26375, 1, 0, 0.74469, 0, 0.53812, 0.08255, 0.46098, 0.26895, 0.46358, 0.39719, 0.39693, 0.45895, 0.26519, 0.47087, 0.09607, 0.56591, 0], "triangles": [6, 10, 13, 6, 9, 10, 9, 6, 7, 12, 13, 11, 13, 10, 11, 8, 9, 7, 6, 13, 5, 5, 13, 14, 5, 14, 4, 2, 3, 1, 14, 15, 4, 4, 15, 0, 0, 15, 16, 1, 3, 4, 0, 16, 17, 1, 4, 0], "vertices": [1, 7, 18.87, 8.14, 1, 1, 7, 19.31, -10.15, 1, 2, 7, 13.42, -16.12, 0.99929, 5, 30.3, -16.12, 0.00071, 2, 7, 6.59, -13.87, 0.93877, 5, 23.47, -13.87, 0.06123, 2, 7, 1.52, -8.11, 0.63055, 5, 18.39, -8.11, 0.36945, 2, 7, -5.17, -5.46, 0.29793, 5, 11.7, -5.46, 0.70207, 3, 7, -11.72, -9.07, 0.02512, 5, 5.16, -9.07, 0.64155, 4, 22.62, -43.51, 0.33333, 1, 4, 14.61, -45.15, 1, 1, 4, 10.13, -42.68, 1, 1, 4, 10.16, -32.93, 1, 1, 4, 21.95, -20.84, 1, 1, 4, 31.45, -20.87, 1, 1, 4, 34.99, -24.68, 1, 3, 7, -10.86, 6.86, 0.0247, 5, 6.02, 6.86, 0.64197, 4, 34.84, -33.25, 0.33333, 2, 7, -4.47, 5.05, 0.30962, 5, 12.41, 5.05, 0.69038, 2, 7, 1.76, 7.51, 0.64295, 5, 18.64, 7.51, 0.35705, 2, 7, 7.49, 12.79, 0.95159, 5, 24.37, 12.79, 0.04841, 1, 7, 13.7, 13.01, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 16, 18, 18, 20, 20, 22, 14, 16, 22, 24, 32, 34, 4, 6, 28, 30, 30, 32, 24, 26, 26, 28, 6, 8, 8, 10, 10, 12, 12, 14], "width": 46, "height": 46}}, "Ear_R_outline": {"Ear_R_outline": {"type": "mesh", "uvs": [0.63459, 0, 0.90381, 0.12729, 1, 0.40996, 1, 0.48076, 0.86087, 0.62465, 0.60811, 0.63908, 0.63322, 0.86547, 0.50496, 1, 0.35141, 1, 0.01933, 0.82162, 0.01024, 0.48438, 0.14343, 0.35707, 0.34846, 0.34836, 0.37996, 0.13098, 0.51699, 0], "triangles": [7, 8, 6, 12, 9, 10, 8, 5, 6, 12, 10, 11, 5, 8, 9, 5, 9, 12, 2, 5, 1, 0, 1, 12, 3, 4, 2, 0, 13, 14, 2, 4, 5, 0, 12, 13, 1, 5, 12], "vertices": [2, 5, 38.17, 14.1, 0.03007, 7, 21.29, 14.1, 0.96993, 2, 5, 44.47, -1.98, 0.0074, 7, 27.59, -1.98, 0.9926, 1, 7, 20.41, -17.74, 1, 2, 5, 34.47, -20.73, 0.00308, 7, 17.59, -20.73, 0.99692, 2, 5, 22.87, -21.26, 0.22171, 7, 6, -21.26, 0.77829, 3, 5, 11.63, -11.82, 0.22171, 7, -5.25, -11.82, 0.44495, 4, 25.04, -50.12, 0.33333, 1, 4, 11.9, -51.53, 1, 1, 4, 4.12, -44.06, 1, 1, 4, 4.15, -35.16, 1, 1, 4, 14.57, -15.93, 1, 1, 4, 34.13, -15.47, 1, 1, 4, 41.49, -23.23, 1, 3, 5, 12.24, 10.78, 0.38416, 7, -4.64, 10.78, 0.2825, 4, 41.95, -35.12, 0.33333, 2, 5, 22.21, 18.7, 0.40684, 7, 5.34, 18.7, 0.59316, 2, 5, 33.21, 18.78, 0.11738, 7, 16.33, 18.78, 0.88262], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 12, 14, 6, 8, 8, 10, 10, 12, 26, 28, 20, 22, 22, 24, 24, 26], "width": 58, "height": 58}}, "Eye_Eyelid_L": {"Eyelid_L_cosed": {"type": "mesh", "uvs": [0.17465, 0.18271, 0.28731, 0.05752, 0.71388, 0.06211, 0.827, 0.18806, 0.94011, 0.31402, 0.9382, 0.45452, 0.93628, 0.59501, 0.82682, 0.59491, 0.71736, 0.59482, 0.6079, 0.59472, 0.49843, 0.59462, 0.38897, 0.59452, 0.27951, 0.59443, 0.17005, 0.59433, 0.06058, 0.59423, 0.06129, 0.45107, 0.062, 0.30791], "triangles": [3, 8, 9, 10, 11, 1, 3, 9, 10, 11, 12, 0, 12, 13, 15, 7, 8, 5, 3, 10, 2, 11, 0, 1, 0, 12, 16, 14, 15, 13, 16, 12, 15, 2, 10, 1, 6, 7, 5, 5, 8, 3, 5, 3, 4], "vertices": [1, 11, -10.78, 11.14, 1, 1, 11, -7.18, 15.15, 1, 1, 11, 6.47, 15, 1, 1, 11, 10.09, 10.97, 1, 1, 11, 13.71, 6.94, 1, 2, 11, 13.65, 2.44, 0.85256, 33, 14.53, 3.46, 0.14744, 2, 11, 13.59, -2.05, 0.61387, 33, 14.47, -1.04, 0.38613, 2, 11, 10.09, -2.05, 0.32341, 33, 10.97, -1.04, 0.67659, 2, 11, 6.59, -2.05, 0.14164, 33, 7.46, -1.03, 0.85836, 2, 11, 3.08, -2.04, 0.03948, 33, 3.96, -1.03, 0.96052, 2, 11, -0.42, -2.04, 0.00418, 33, 0.46, -1.03, 0.99582, 2, 11, -3.92, -2.04, 0.02849, 33, -3.05, -1.02, 0.97151, 2, 11, -7.43, -2.03, 0.11458, 33, -6.55, -1.02, 0.88542, 2, 11, -10.93, -2.03, 0.26288, 33, -10.05, -1.02, 0.73712, 2, 11, -14.43, -2.03, 0.51422, 33, -13.55, -1.01, 0.48578, 2, 11, -14.41, 2.55, 0.76161, 33, -13.53, 3.57, 0.23839, 1, 11, -14.39, 7.14, 1], "hull": 17, "edges": [2, 4, 28, 30, 30, 32, 8, 10, 10, 12, 4, 6, 6, 8, 2, 0, 0, 32, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 32, "height": 32}}, "Eye_Eyelid_L2": {"Eyelid_L_cosed": {"type": "mesh", "uvs": [0.17465, 0.18271, 0.28731, 0.05752, 0.71388, 0.06211, 0.827, 0.18806, 0.94011, 0.31402, 0.9382, 0.45452, 0.93628, 0.59501, 0.82682, 0.59491, 0.71736, 0.59482, 0.6079, 0.59472, 0.49843, 0.59462, 0.38897, 0.59452, 0.27951, 0.59443, 0.17005, 0.59433, 0.06058, 0.59423, 0.06129, 0.45107, 0.062, 0.30791], "triangles": [7, 8, 5, 3, 8, 9, 2, 9, 10, 10, 11, 1, 11, 12, 0, 14, 15, 13, 12, 13, 15, 6, 7, 5, 5, 8, 3, 2, 3, 9, 11, 0, 1, 2, 10, 1, 0, 12, 16, 16, 12, 15, 5, 3, 4], "vertices": [2, 11, 9.99, -10.93, 0.94857, 34, 10.87, -9.91, 0.05143, 1, 11, 6.38, -14.93, 1, 1, 11, -7.27, -14.78, 1, 2, 11, -10.89, -10.75, 0.93714, 34, -10.01, -9.74, 0.06286, 2, 11, -14.51, -6.72, 0.91714, 34, -13.63, -5.71, 0.08286, 2, 11, -14.45, -2.23, 0.76971, 34, -13.57, -1.22, 0.23029, 2, 11, -14.38, 2.27, 0.53101, 34, -13.51, 3.28, 0.46899, 2, 11, -10.88, 2.27, 0.28627, 34, -10, 3.28, 0.71373, 2, 11, -7.38, 2.26, 0.12164, 34, -6.5, 3.27, 0.87836, 2, 11, -3.88, 2.26, 0.03948, 34, -3, 3.27, 0.96052, 2, 11, -0.37, 2.26, 0.00418, 34, 0.5, 3.27, 0.99582, 2, 11, 3.13, 2.25, 0.02849, 34, 4.01, 3.26, 0.97151, 2, 11, 6.63, 2.25, 0.09458, 34, 7.51, 3.26, 0.90542, 2, 11, 10.14, 2.25, 0.22574, 34, 11.01, 3.26, 0.77426, 2, 11, 13.64, 2.24, 0.43136, 34, 14.52, 3.26, 0.56864, 2, 11, 13.62, -2.34, 0.67876, 34, 14.49, -1.33, 0.32124, 2, 11, 13.59, -6.92, 0.91714, 34, 14.47, -5.91, 0.08286], "hull": 17, "edges": [2, 4, 28, 30, 30, 32, 8, 10, 10, 12, 4, 6, 6, 8, 2, 0, 0, 32, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 18, 4], "width": 32, "height": 32}}, "Eye_Eyelid_R": {"Eyelid_L_cosed": {"type": "mesh", "uvs": [0.17465, 0.18271, 0.28731, 0.05752, 0.71388, 0.06211, 0.827, 0.18806, 0.94011, 0.31402, 0.9382, 0.45452, 0.93628, 0.59501, 0.82682, 0.59491, 0.71736, 0.59482, 0.6079, 0.59472, 0.49843, 0.59462, 0.38897, 0.59452, 0.27951, 0.59443, 0.17005, 0.59433, 0.06058, 0.59423, 0.06129, 0.45107, 0.062, 0.30791], "triangles": [3, 8, 9, 10, 11, 1, 3, 9, 10, 11, 12, 0, 12, 13, 15, 7, 8, 5, 6, 7, 5, 3, 10, 2, 11, 0, 1, 2, 10, 1, 0, 12, 16, 14, 15, 13, 16, 12, 15, 5, 8, 3, 5, 3, 4], "vertices": [1, 10, -9.7, 11.14, 1, 1, 10, -6.1, 15.15, 1, 1, 10, 7.55, 15, 1, 1, 10, 11.17, 10.97, 1, 1, 10, 14.79, 6.94, 1, 2, 10, 14.73, 2.44, 0.8582, 32, 13.72, 3.39, 0.1418, 2, 10, 14.67, -2.05, 0.58197, 32, 13.66, -1.11, 0.41803, 2, 10, 11.16, -2.05, 0.33886, 32, 10.15, -1.1, 0.66114, 2, 10, 7.66, -2.05, 0.17803, 32, 6.65, -1.1, 0.82197, 2, 10, 4.16, -2.04, 0.08467, 32, 3.15, -1.1, 0.91533, 2, 10, 0.66, -2.04, 0.0513, 32, -0.36, -1.09, 0.9487, 2, 10, -2.85, -2.04, 0.07524, 32, -3.86, -1.09, 0.92476, 2, 10, -6.35, -2.03, 0.15815, 32, -7.36, -1.09, 0.84185, 2, 10, -9.85, -2.03, 0.30657, 32, -10.86, -1.09, 0.69343, 2, 10, -13.36, -2.03, 0.53365, 32, -14.37, -1.08, 0.46635, 2, 10, -13.33, 2.55, 0.81577, 32, -14.34, 3.5, 0.18423, 1, 10, -13.31, 7.14, 1], "hull": 17, "edges": [2, 4, 28, 30, 30, 32, 8, 10, 10, 12, 4, 6, 6, 8, 2, 0, 0, 32, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 32, "height": 32}}, "Eye_Eyelid_R2": {"Eyelid_L_cosed": {"type": "mesh", "uvs": [0.17465, 0.18271, 0.28731, 0.05752, 0.71388, 0.06211, 0.827, 0.18806, 0.94011, 0.31402, 0.9382, 0.45452, 0.93628, 0.59501, 0.82682, 0.59491, 0.71736, 0.59482, 0.6079, 0.59472, 0.49843, 0.59462, 0.38897, 0.59452, 0.27951, 0.59443, 0.17005, 0.59433, 0.06058, 0.59423, 0.06129, 0.45107, 0.062, 0.30791], "triangles": [7, 8, 5, 3, 8, 9, 2, 9, 10, 10, 11, 1, 11, 12, 0, 14, 15, 13, 12, 13, 15, 6, 7, 5, 5, 8, 3, 2, 3, 9, 11, 0, 1, 2, 10, 1, 0, 12, 16, 16, 12, 15, 5, 3, 4], "vertices": [2, 10, 11.06, -11.04, 0.96286, 35, 10.05, -10.1, 0.03714, 1, 10, 7.46, -15.05, 1, 1, 10, -6.19, -14.9, 1, 2, 10, -9.81, -10.87, 0.94857, 35, -10.82, -9.93, 0.05143, 2, 10, -13.43, -6.84, 0.89714, 35, -14.44, -5.9, 0.10286, 2, 10, -13.37, -2.35, 0.75534, 35, -14.38, -1.4, 0.24466, 2, 10, -13.31, 2.15, 0.47911, 35, -14.32, 3.09, 0.52089, 2, 10, -9.81, 2.15, 0.30458, 35, -10.82, 3.09, 0.69542, 2, 10, -6.3, 2.14, 0.1666, 35, -7.31, 3.09, 0.8334, 2, 10, -2.8, 2.14, 0.07324, 35, -3.81, 3.08, 0.92676, 2, 10, 0.7, 2.14, 0.0513, 35, -0.31, 3.08, 0.9487, 2, 10, 4.21, 2.13, 0.07524, 35, 3.19, 3.08, 0.92476, 2, 10, 7.71, 2.13, 0.14386, 35, 6.7, 3.07, 0.85614, 2, 10, 11.21, 2.13, 0.27228, 35, 10.2, 3.07, 0.72772, 2, 10, 14.71, 2.12, 0.43079, 35, 13.7, 3.07, 0.56921, 2, 10, 14.69, -2.46, 0.71291, 35, 13.68, -1.51, 0.28709, 2, 10, 14.67, -7.04, 0.89714, 35, 13.66, -6.09, 0.10286], "hull": 17, "edges": [2, 4, 28, 30, 30, 32, 8, 10, 10, 12, 4, 6, 6, 8, 2, 0, 0, 32, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 18, 4], "width": 32, "height": 32}}, "Eye_L": {"Eye_L": {"width": 37, "height": 37}}, "Eye_R": {"Eye_R": {"width": 37, "height": 37}}, "Light_from_Ray": {"Light_from_Ray": {"x": 9.93, "y": -2.34, "rotation": -90, "width": 216, "height": 43}}, "Light_Glow": {"Light_Glow": {"x": 1.43, "y": -0.84, "rotation": -90, "width": 281, "height": 258}}, "Light_Ray": {"Light_Ray": {"x": -96.07, "y": -1.34, "rotation": -90, "width": 258, "height": 243}}, "Light_Ray2": {"Light_Ray": {"x": -96.07, "y": -1.34, "rotation": -90, "width": 258, "height": 243}}, "Lips": {"Lips": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [56, -7.99, -56, -7.99, -56, 22.01, 56, 22.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 112, "height": 30}}, "Mouth": {"Mouth": {"x": -1.5, "y": 7.01, "width": 103, "height": 20}}, "Pupil_L": {"Eye_cross_R": {"scaleX": 0.7, "scaleY": 0.7, "width": 25, "height": 28}, "Pupil_L": {"width": 9, "height": 10}}, "Pupil_R": {"Eye_cross_L": {"scaleX": 0.7, "scaleY": 0.7, "width": 29, "height": 28}, "Pupil_R": {"width": 10, "height": 10}}, "Tongue": {"Tongue": {"type": "mesh", "uvs": [0.96195, 0, 0.96197, 0.39946, 0.962, 0.79892, 0.56992, 0.99999, 0.16154, 0.82416, 0.08077, 0.51164, 0, 0.19912, 0.80196, 0], "triangles": [3, 1, 2, 5, 1, 3, 3, 4, 5, 1, 5, 7, 7, 0, 1, 5, 6, 7], "vertices": [2, 15, 12.41, 3.31, 0.93576, 16, 11.09, 16.94, 0.06424, 2, 15, 10.74, -4.91, 0.64486, 16, 10.08, 8.61, 0.35514, 2, 15, 9.06, -13.13, 0.19645, 16, 9.08, 0.28, 0.80355, 2, 15, -1.77, -15.22, 0.00787, 16, -1.55, -2.69, 0.99213, 2, 15, -11.44, -9.48, 0.49366, 16, -11.65, 2.25, 0.50634, 2, 15, -12.18, -2.63, 0.7865, 16, -12.94, 9.02, 0.2135, 2, 15, -12.93, 4.22, 0.94097, 16, -14.24, 15.78, 0.05903, 2, 15, 8.34, 4.14, 0.95699, 16, 6.96, 17.44, 0.04301], "hull": 8, "edges": [0, 14, 4, 6, 12, 14, 6, 8, 0, 2, 2, 4, 8, 10, 10, 12], "width": 26, "height": 21}}, "Tooth_01": {"Tooth_01": {"x": -1.4, "y": 4.36, "rotation": 11.42, "width": 17, "height": 16}}, "Tooth_02": {"Tooth_02": {"x": 0.77, "y": 4.05, "rotation": -21.44, "width": 16, "height": 16}}, "Tooth_03": {"Tooth_03": {"x": -0.26, "y": 1.63, "rotation": 4.57, "width": 17, "height": 17}}, "Ufo_Body": {"Ufo_Body": {"x": 0.34, "y": -17.92, "width": 266, "height": 91}}, "Ufo_Body_Outline": {"Ufo_Body_Outline": {"x": 0.34, "y": -17.42, "width": 278, "height": 104}}}}], "animations": {"t0_000000": {"slots": {"Body_outline": {"rgba": [{"color": "00000080"}]}, "Ear_L_outline": {"rgba": [{"color": "00000080"}]}, "Ear_R_outline": {"rgba": [{"color": "00000080"}]}, "Ufo_Body_Outline": {"rgba": [{"color": "00000080"}]}}}, "t0_405c80": {"slots": {"Body_outline": {"rgba": [{"color": "405c80ff"}]}, "Ear_L_outline": {"rgba": [{"color": "405c80ff"}]}, "Ear_R_outline": {"rgba": [{"color": "405c80ff"}]}, "Ufo_Body_Outline": {"rgba": [{"color": "405c80ff"}]}}}, "t1_Death": {"slots": {"blot": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 1.0333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot"}]}, "blot_drop2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop2"}, {"time": 0.5333}]}, "blot_drop3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop4": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop2"}, {"time": 0.6}]}, "blot_drop5": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop1"}, {"time": 0.5333}]}, "blot_drop6": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop2"}, {"time": 0.6}]}, "blot_drop7": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop2"}, {"time": 0.6667}]}, "blot_drop8": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop1"}, {"time": 0.7667}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop1"}, {"time": 0.5}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop1"}, {"time": 0.4667}]}, "Body": {"attachment": [{"name": "Body"}, {"time": 0.5}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}, {"time": 0.5}]}, "Ear_L": {"attachment": [{"name": "Ear_L"}, {"time": 0.5}]}, "Ear_L_outline": {"attachment": [{"name": "Ear_L_outline"}, {"time": 0.5}]}, "Ear_R": {"attachment": [{"name": "Ear_R"}, {"time": 0.5}]}, "Ear_R_outline": {"attachment": [{"name": "Ear_R_outline"}, {"time": 0.5}]}, "Eye_Eyelid_L": {"attachment": [{}]}, "Eye_Eyelid_L2": {"attachment": [{"name": "Eyelid_L_cosed"}, {"time": 0.5}]}, "Eye_Eyelid_R": {"attachment": [{}]}, "Eye_Eyelid_R2": {"attachment": [{"name": "Eyelid_L_cosed"}, {"time": 0.5}]}, "Eye_L": {"attachment": [{"name": "Eye_L"}, {"time": 0.5}]}, "Eye_R": {"attachment": [{"name": "Eye_R"}, {"time": 0.5}]}, "Light_from_Ray": {"rgba": [{"color": "ffffffff"}, {"time": 0.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff89"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": "Light_from_Ray"}, {"time": 0.5}]}, "Light_Glow": {"rgba": [{"color": "ffffffff"}, {"time": 0.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff89"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": "Light_Glow"}, {"time": 0.5}]}, "Light_Ray": {"rgba": [{"color": "ffffffff"}, {"time": 0.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff89"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"name": "Light_Ray"}, {"time": 0.5}]}, "Light_Ray2": {"rgba": [{"color": "ffffff00"}]}, "Lips": {"attachment": [{"name": "Lips"}, {"time": 0.5}]}, "Mouth": {"attachment": [{"name": "Mouth"}, {"time": 0.5}]}, "Pupil_L": {"attachment": [{"name": "Pupil_L"}, {"time": 0.0667, "name": "Eye_cross_R"}, {"time": 0.5}]}, "Pupil_R": {"attachment": [{"name": "Pupil_R"}, {"time": 0.0667, "name": "Eye_cross_L"}, {"time": 0.5}]}, "Tongue": {"attachment": [{"name": "Tongue"}, {"time": 0.0333}, {"time": 0.5}]}, "Tooth_01": {"attachment": [{"name": "Tooth_01"}, {"time": 0.5}]}, "Tooth_02": {"attachment": [{"name": "Tooth_02"}, {"time": 0.5}]}, "Tooth_03": {"attachment": [{"name": "Tooth_03"}, {"time": 0.5}]}, "Ufo_Body": {"attachment": [{"name": "Ufo_Body"}, {"time": 0.5}]}, "Ufo_Body_Outline": {"attachment": [{"name": "Ufo_Body_Outline"}, {"time": 0.5}]}}, "bones": {"blot": {"translate": [{"curve": "stepped"}, {"time": 0.3667, "x": -12.21, "y": 15.4, "curve": [0.478, -12.21, 0.589, -12.22, 0.435, 8.18, 0.589, 1.44]}, {"time": 0.7, "x": -12.22, "y": 1.44}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "x": 0.85, "y": 0.85, "curve": [0.36, 1.26, 0.511, 1.2, 0.36, 1.26, 0.511, 1.2]}, {"time": 0.6, "x": 1.2, "y": 1.2}]}, "blot_drops_control": {"translate": [{"curve": "stepped"}, {"time": 0.3667, "x": -0.54, "y": -0.54, "curve": [0.589, -0.54, 0.811, 0, 0.512, -0.67, 0.811, -16.59]}, {"time": 1.0333, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.3, "curve": [0.367, 0, 0.433, -67.84]}, {"time": 0.5, "value": -67.84}], "translate": [{"curve": "stepped"}, {"time": 0.3, "x": 41.44, "y": 7.91, "curve": [0.365, 77.65, 0.456, 273.93, 0.36, -38.99, 0.468, -211.23]}, {"time": 0.5333, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "curve": [0.511, 1, 0.556, 0.4, 0.511, 1, 0.556, 0.4]}, {"time": 0.6, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{"curve": "stepped"}, {"time": 0.3, "value": -35.08, "curve": [0.362, 32.76, 0.522, 77.81]}, {"time": 0.6333, "value": 77.81}], "translate": [{"curve": "stepped"}, {"time": 0.3, "x": -74.68, "y": 24.95, "curve": [0.386, -115.07, 0.522, -322.02, 0.397, 261.05, 0.541, -337.68]}, {"time": 0.6333, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{"curve": "stepped"}, {"time": 0.3, "value": 16.41, "curve": [0.397, 68.37, 0.5, 77.81]}, {"time": 0.6, "value": 77.81}], "translate": [{"curve": "stepped"}, {"time": 0.3, "x": -48.78, "y": -9.58, "curve": [0.354, -164.38, 0.5, -211.51, 0.378, -53.5, 0.529, -476.89]}, {"time": 0.6, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "curve": [0.511, 1, 0.556, 0.4, 0.511, 1, 0.556, 0.4]}, {"time": 0.6, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.3}], "translate": [{"curve": "stepped"}, {"time": 0.3, "x": 54.1, "y": 29.08, "curve": [0.368, 209.62, 0.433, 276.96, 0.368, 5.57, 0.442, -88.86]}, {"time": 0.5, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.3}], "translate": [{"curve": "stepped"}, {"time": 0.3, "x": -53.64, "y": 36.2, "curve": [0.348, -164.57, 0.443, -313.12, 0.377, 187.62, 0.459, -267.19]}, {"time": 0.5333, "x": -368.12, "y": -387.78}]}, "blot_drop5": {"rotate": [{"curve": "stepped"}, {"time": 0.3, "value": 103.14, "curve": [0.389, 103.14, 0.552, 97.56]}, {"time": 0.5667, "value": 97.56}], "translate": [{"curve": "stepped"}, {"time": 0.3, "x": 35.48, "y": -17.82, "curve": [0.388, 58.13, 0.5, 37.99, 0.377, -78.13, 0.517, -373.65]}, {"time": 0.6, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.4667, "curve": [0.511, 1, 0.556, 0.4, 0.511, 1, 0.556, 0.4]}, {"time": 0.6, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.3}], "translate": [{"curve": "stepped"}, {"time": 0.3, "x": 10.55, "y": -19.73, "curve": [0.356, -6.01, 0.404, -21.01, 0.347, -79.55, 0.394, -201.42]}, {"time": 0.4667, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{"curve": "stepped"}, {"time": 0.3, "value": -75.4, "curve": [0.342, -120.98, 0.427, -263.98]}, {"time": 0.5333, "value": -261.68}], "translate": [{"curve": "stepped"}, {"time": 0.3, "x": 9.31, "y": 29.92, "curve": [0.392, 118.46, 0.544, 297.6, 0.356, 351.23, 0.514, 364.92]}, {"time": 0.6667, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5333, "curve": [0.578, 1, 0.622, 0.4, 0.578, 1, 0.622, 0.4]}, {"time": 0.6667, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "x": -53.64, "y": 83.69, "curve": [0.433, -154.19, 0.617, -338.78, 0.504, 242.44, 0.659, -264.59]}, {"time": 0.7667, "x": -378.34, "y": -598.88}]}, "Cntr": {"rotate": [{"curve": [0.033, 0, 0.067, 15.91]}, {"time": 0.1, "value": 15.91, "curve": [0.144, 15.91, 0.189, -10.15]}, {"time": 0.2333, "value": -10.15, "curve": [0.3, -10.15, 0.367, 8.63]}, {"time": 0.4333, "value": 8.63}], "translate": [{"y": -2.92, "curve": [0.044, 0, 0.089, -2.16, 0.044, -2.92, 0.089, 12.53]}, {"time": 0.1333, "x": -2.16, "y": 12.53, "curve": [0.178, -2.16, 0.222, 10.83, 0.178, 12.53, 0.222, -1.37]}, {"time": 0.2667, "x": 10.83, "y": -1.37, "curve": [0.333, 10.83, 0.4, -9.63, 0.333, -1.37, 0.4, -7.19]}, {"time": 0.4667, "x": -9.63, "y": -7.19}], "scale": [{"x": 1.015, "y": 1.015, "curve": [0.033, 1.015, 0.067, 0.9, 0.033, 1.015, 0.067, 1.1]}, {"time": 0.1, "x": 0.9, "y": 1.1, "curve": [0.156, 0.9, 0.211, 1.015, 0.156, 1.1, 0.211, 1.015]}, {"time": 0.2667, "x": 1.015, "y": 1.015}]}, "Ear_L": {"rotate": [{"value": -14.3, "curve": [0.033, -14.3, 0.067, -35.77]}, {"time": 0.1, "value": -35.77, "curve": [0.144, -35.77, 0.189, 22.11]}, {"time": 0.2333, "value": 22.11}]}, "Ear_R": {"rotate": [{"value": 8.06, "curve": [0.033, 8.06, 0.067, -18.53]}, {"time": 0.1, "value": -18.53, "curve": [0.144, -18.53, 0.189, 44.47]}, {"time": 0.2333, "value": 44.47}]}, "Eye_L": {"translate": [{}], "scale": [{}]}, "Eye_R": {"translate": [{}], "scale": [{}]}, "Mouth": {"rotate": [{"curve": [0.022, 0, 0.044, 13.23]}, {"time": 0.0667, "value": 13.23, "curve": [0.111, 13.23, 0.156, -9.42]}, {"time": 0.2, "value": -9.42, "curve": [0.278, -9.42, 0.356, 16.69]}, {"time": 0.4333, "value": 16.69}], "translate": [{"x": 1.03, "curve": [0.033, 1.03, 0.067, 9.81, 0.033, 0, 0.067, 1.41]}, {"time": 0.1, "x": 9.81, "y": 1.41, "curve": [0.133, 9.81, 0.167, 8.87, 0.133, 1.41, 0.167, -1.09]}, {"time": 0.2, "x": 8.87, "y": -1.09, "curve": [0.278, 8.87, 0.356, 4.27, 0.278, -1.09, 0.356, 1.46]}, {"time": 0.4333, "x": 4.27, "y": 1.46}], "scale": [{"curve": [0.033, 1, 0.067, 1, 0.033, 1, 0.067, -1.014]}, {"time": 0.1, "y": -1.014}]}, "Pupil_L": {"translate": [{"x": 2.02, "y": -0.64}, {"time": 0.1333, "x": 0.78, "y": 6.74}]}, "Pupil_R": {"translate": [{"x": -1.51, "y": -1.57}, {"time": 0.1333, "x": -1.51, "y": 6.02}]}, "Tongue_tip": {"rotate": [{"value": 35.39}], "translate": [{"x": 6.82, "y": 1.51}]}, "Body": {"rotate": [{"value": -0.2, "curve": [0.026, -0.2, 0.041, -16.37]}, {"time": 0.0667, "value": -16.37, "curve": [0.111, -16.37, 0.156, 18.41]}, {"time": 0.2, "value": 18.41, "curve": [0.267, 18.41, 0.374, -16.37]}, {"time": 0.4, "value": -16.37}], "translate": [{"y": 17.01, "curve": [0.025, 0, 0.042, -3.48, 0.025, 17.01, 0.042, 5.02]}, {"time": 0.0667, "x": -3.48, "y": 5.02, "curve": [0.133, -3.48, 0.2, 2.04, 0.133, 5.02, 0.2, 6.78]}, {"time": 0.2667, "x": 2.04, "y": 6.78, "curve": [0.311, 2.04, 0.375, -3.48, 0.311, 6.78, 0.375, 5.02]}, {"time": 0.4, "x": -3.48, "y": 5.02}], "scale": [{"x": 1.05, "y": 0.987}, {"time": 0.1, "x": 1.6, "y": 0.987}, {"time": 0.2, "x": 1.414, "y": 0.987}, {"time": 0.3333, "x": 0.822, "y": 1.065}]}, "Body2": {"rotate": [{"curve": [0.033, 0, 0.067, -18.15]}, {"time": 0.1, "value": -18.15, "curve": [0.144, -18.15, 0.189, 18.64]}, {"time": 0.2333, "value": 18.64, "curve": [0.3, 18.64, 0.4, -18.15]}, {"time": 0.4333, "value": -18.15}], "translate": [{"x": -5.32}]}, "Ear_R2": {"rotate": [{"value": 12.06, "curve": [0.044, 12.06, 0.089, -44.57]}, {"time": 0.1333, "value": -44.57, "curve": [0.178, -44.57, 0.222, 48.47]}, {"time": 0.2667, "value": 48.47}]}, "Ear_L2": {"rotate": [{"value": -19.43, "curve": [0.044, -19.43, 0.089, -38.72]}, {"time": 0.1333, "value": -38.72, "curve": [0.178, -38.72, 0.222, 16.98]}, {"time": 0.2667, "value": 16.98}]}, "Eyelid_R": {"translate": [{"y": 15.21}]}, "Eyelid_L": {"translate": [{"y": 15.21}]}, "Cntr_control": {"rotate": [{}]}}}, "t1_IDLE": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body": {"attachment": [{"name": "Body"}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Ear_L": {"attachment": [{"name": "Ear_L"}]}, "Ear_L_outline": {"attachment": [{"name": "Ear_L_outline"}]}, "Ear_R": {"attachment": [{"name": "Ear_R"}]}, "Ear_R_outline": {"attachment": [{"name": "Ear_R_outline"}]}, "Eye_Eyelid_L": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_L2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_R": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_R2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_L": {"attachment": [{"name": "Eye_L"}]}, "Eye_R": {"attachment": [{"name": "Eye_R"}]}, "Light_from_Ray": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Light_from_Ray"}]}, "Light_Glow": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Light_Glow"}]}, "Light_Ray": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Light_Ray"}]}, "Light_Ray2": {"rgba": [{"color": "ffffff00"}]}, "Lips": {"attachment": [{"name": "Lips"}]}, "Mouth": {"attachment": [{"name": "Mouth"}]}, "Pupil_L": {"attachment": [{"name": "Pupil_L"}]}, "Pupil_R": {"attachment": [{"name": "Pupil_R"}]}, "Tongue": {"attachment": [{}]}, "Tooth_01": {"attachment": [{"name": "Tooth_01"}]}, "Tooth_02": {"attachment": [{"name": "Tooth_02"}]}, "Tooth_03": {"attachment": [{"name": "Tooth_03"}]}, "Ufo_Body": {"attachment": [{"name": "Ufo_Body"}]}, "Ufo_Body_Outline": {"attachment": [{"name": "Ufo_Body_Outline"}]}}, "bones": {"Cntr": {"rotate": [{}], "translate": [{"y": -2.92, "curve": [0.222, 0, 0.444, 0, 0.222, -2.92, 0.444, 4.92]}, {"time": 0.6667, "y": 4.92, "curve": [0.889, 0, 1.111, 0, 0.889, 4.92, 1.111, -2.92]}, {"time": 1.3333, "y": -2.92, "curve": [1.556, 0, 1.778, 0, 1.556, -2.92, 1.778, 4.92]}, {"time": 2, "y": 4.92, "curve": [2.222, 0, 2.444, 0, 2.222, 4.92, 2.444, -2.92]}, {"time": 2.6667, "y": -2.92}], "scale": [{"x": 1.015, "y": 1.015, "curve": [0.113, 1.007, 0.223, 1, 0.113, 1.007, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1.029, 0.556, 1, 0.778, 1.029]}, {"time": 1, "x": 1.029, "y": 1.029, "curve": [1.112, 1.029, 1.222, 1.022, 1.112, 1.029, 1.222, 1.022]}, {"time": 1.3333, "x": 1.015, "y": 1.015, "curve": [1.446, 1.007, 1.556, 1, 1.446, 1.007, 1.556, 1]}, {"time": 1.6667, "curve": [1.889, 1, 2.111, 1.029, 1.889, 1, 2.111, 1.029]}, {"time": 2.3333, "x": 1.029, "y": 1.029, "curve": [2.445, 1.029, 2.557, 1.022, 2.445, 1.029, 2.557, 1.022]}, {"time": 2.6667, "x": 1.015, "y": 1.015}]}, "Tongue_tip": {"rotate": [{"value": 9.35}], "translate": [{"x": 3.07, "y": 9.11}]}, "Pupil_R": {"translate": [{"x": -10.77, "y": 4.03, "curve": "stepped"}, {"time": 0.8667, "x": -10.77, "y": 4.03, "curve": [0.911, -10.77, 0.956, 7.47, 0.911, 4.03, 0.956, 3.91]}, {"time": 1, "x": 7.47, "y": 3.91, "curve": "stepped"}, {"time": 2.3333, "x": 7.47, "y": 3.91, "curve": [2.378, 7.47, 2.422, -10.77, 2.378, 3.91, 2.422, 4.03]}, {"time": 2.4667, "x": -10.77, "y": 4.03}], "scale": [{"curve": "stepped"}, {"time": 0.8667, "curve": [0.889, 1.416, 0.911, 2.247, 0.889, 1, 0.911, 1]}, {"time": 0.9333, "x": 2.247, "curve": [0.956, 2.247, 0.978, 1.416, 0.956, 1, 0.978, 1]}, {"time": 1, "curve": "stepped"}, {"time": 2.3333, "curve": [2.356, 1.416, 2.378, 2.247, 2.356, 1, 2.378, 1]}, {"time": 2.4, "x": 2.247, "curve": [2.422, 2.247, 2.444, 1.416, 2.422, 1, 2.444, 1]}, {"time": 2.4667}]}, "Pupil_L": {"translate": [{"x": -7.23, "y": 4.96, "curve": "stepped"}, {"time": 0.8667, "x": -7.23, "y": 4.96, "curve": [0.911, -7.23, 0.956, 11, 0.911, 4.96, 0.956, 4.84]}, {"time": 1, "x": 11, "y": 4.84, "curve": "stepped"}, {"time": 2.3333, "x": 11, "y": 4.84, "curve": [2.378, 11, 2.422, -7.23, 2.378, 4.84, 2.422, 4.96]}, {"time": 2.4667, "x": -7.23, "y": 4.96}], "scale": [{"curve": "stepped"}, {"time": 0.8667, "curve": [0.889, 1.416, 0.911, 2.247, 0.889, 1, 0.911, 1]}, {"time": 0.9333, "x": 2.247, "curve": [0.956, 2.247, 0.978, 1.416, 0.956, 1, 0.978, 1]}, {"time": 1, "curve": "stepped"}, {"time": 2.3333, "curve": [2.356, 1.416, 2.378, 2.247, 2.356, 1, 2.378, 1]}, {"time": 2.4, "x": 2.247, "curve": [2.422, 2.247, 2.444, 1.416, 2.422, 1, 2.444, 1]}, {"time": 2.4667}]}, "Body": {"rotate": [{"value": 3.04, "curve": [0.222, 3.96, 0.444, 5.84]}, {"time": 0.6667, "value": 5.84}, {"time": 0.9, "value": 3.94, "curve": [0.944, 3.94, 0.989, -2.75]}, {"time": 1.0333, "value": -3.1, "curve": [1.297, -5.13, 1.611, -7.27]}, {"time": 1.9, "value": -7.27, "curve": [2.056, -7.27, 2.225, -5.58]}, {"time": 2.3667, "value": -3.1, "curve": [2.411, -2.32, 2.456, 0.96]}, {"time": 2.5, "value": 1.68, "curve": [2.556, 2.58, 2.611, 2.8]}, {"time": 2.6667, "value": 3.04}], "translate": [{"y": 25, "curve": "stepped"}, {"time": 0.8667, "y": 25, "curve": [0.9, 0, 0.933, 0.06, 0.9, 25, 0.933, 19.42]}, {"time": 0.9667, "x": 0.06, "y": 19.42, "curve": [1, 0.06, 1.033, 0, 1, 19.42, 1.033, 25]}, {"time": 1.0667, "y": 25, "curve": "stepped"}, {"time": 2.3333, "y": 25, "curve": [2.367, 0, 2.4, 0.06, 2.367, 25, 2.4, 19.42]}, {"time": 2.4333, "x": 0.06, "y": 19.42, "curve": [2.467, 0.06, 2.5, 0, 2.467, 19.42, 2.5, 25]}, {"time": 2.5333, "y": 25}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": [0.878, 1, 0.922, 0.95, 0.878, 1, 0.922, 1.05]}, {"time": 0.9667, "x": 0.95, "y": 1.05, "curve": [1.011, 0.95, 1.022, 1, 1.011, 1.05, 1.022, 1]}, {"time": 1.1, "curve": "stepped"}, {"time": 2.3, "curve": [2.344, 1, 2.389, 0.95, 2.344, 1, 2.389, 1.05]}, {"time": 2.4333, "x": 0.95, "y": 1.05, "curve": [2.478, 0.95, 2.522, 1, 2.478, 1.05, 2.522, 1]}, {"time": 2.5667}]}, "Body2": {"rotate": [{"value": 7.54, "curve": "stepped"}, {"time": 0.9, "value": 7.54, "curve": [0.944, 7.54, 0.989, -6.13]}, {"time": 1.0333, "value": -6.13, "curve": "stepped"}, {"time": 2.3667, "value": -6.13, "curve": [2.411, -6.13, 2.456, 7.54]}, {"time": 2.5, "value": 7.54}], "translate": [{"x": -5.32}]}, "Ear_R": {"rotate": [{"value": 17.15, "curve": [0.045, 10.91, 0.089, -0.02]}, {"time": 0.1333, "value": -0.02, "curve": [0.233, -0.02, 0.333, 8.06]}, {"time": 0.4333, "value": 8.06, "curve": "stepped"}, {"time": 0.9, "value": 8.06, "curve": [0.922, 8.06, 0.944, 32.48]}, {"time": 0.9667, "value": 32.48, "curve": [1.011, 32.48, 1.056, -14.96]}, {"time": 1.1, "value": -14.96, "curve": [1.167, -14.96, 1.233, 17.89]}, {"time": 1.3, "value": 17.89, "curve": [1.389, 17.89, 1.478, 8.06]}, {"time": 1.5667, "value": 8.06, "curve": "stepped"}, {"time": 2.3667, "value": 8.06, "curve": [2.389, 8.06, 2.411, -9.37]}, {"time": 2.4333, "value": -9.37, "curve": [2.478, -9.37, 2.522, 25.97]}, {"time": 2.5667, "value": 25.97, "curve": [2.6, 25.97, 2.634, 21.87]}, {"time": 2.6667, "value": 17.15}]}, "Ear_R2": {"rotate": [{"value": 47.74, "curve": [0.067, 36.33, 0.133, -6.81]}, {"time": 0.2, "value": -6.81, "curve": [0.3, -6.81, 0.4, 12.06]}, {"time": 0.5, "value": 12.06, "curve": "stepped"}, {"time": 0.9667, "value": 12.06, "curve": [0.989, 12.06, 1.011, 37.73]}, {"time": 1.0333, "value": 37.73, "curve": [1.078, 37.73, 1.122, -28.26]}, {"time": 1.1667, "value": -28.26, "curve": [1.233, -28.26, 1.3, 21.89]}, {"time": 1.3667, "value": 21.89, "curve": [1.456, 21.89, 1.544, 12.06]}, {"time": 1.6333, "value": 12.06, "curve": "stepped"}, {"time": 2.4333, "value": 12.06, "curve": [2.456, 12.06, 2.478, -22.39]}, {"time": 2.5, "value": -22.39, "curve": [2.544, -22.39, 2.589, 51.02]}, {"time": 2.6333, "value": 51.02, "curve": [2.644, 51.02, 2.656, 49.64]}, {"time": 2.6667, "value": 47.74}]}, "Ear_L": {"rotate": [{"value": -0.83, "curve": [0.045, -9.09, 0.089, -22.38]}, {"time": 0.1333, "value": -22.38, "curve": [0.233, -22.38, 0.333, -14.3]}, {"time": 0.4333, "value": -14.3, "curve": "stepped"}, {"time": 0.9, "value": -14.3, "curve": [0.922, -14.3, 0.944, 9.18]}, {"time": 0.9667, "value": 9.18, "curve": [1.011, 9.18, 1.056, -39.6]}, {"time": 1.1, "value": -39.6, "curve": [1.167, -39.6, 1.233, -4.47]}, {"time": 1.3, "value": -4.47, "curve": [1.389, -4.47, 1.478, -14.3]}, {"time": 1.5667, "value": -14.3, "curve": "stepped"}, {"time": 2.3667, "value": -14.3, "curve": [2.389, -14.3, 2.411, -35.62]}, {"time": 2.4333, "value": -35.62, "curve": [2.478, -35.62, 2.522, 10.83]}, {"time": 2.5667, "value": 10.83, "curve": [2.6, 10.83, 2.634, 5.4]}, {"time": 2.6667, "value": -0.83}]}, "Ear_L2": {"rotate": [{"value": 33.5, "curve": [0.067, 19.48, 0.133, -32.72]}, {"time": 0.2, "value": -32.72, "curve": [0.3, -32.72, 0.4, -19.43]}, {"time": 0.5, "value": -19.43, "curve": "stepped"}, {"time": 0.9667, "value": -19.43, "curve": [0.989, -19.43, 1.011, 11.1]}, {"time": 1.0333, "value": 11.1, "curve": [1.078, 11.1, 1.122, -51.49]}, {"time": 1.1667, "value": -51.49, "curve": [1.233, -51.49, 1.3, -9.6]}, {"time": 1.3667, "value": -9.6, "curve": [1.456, -9.6, 1.544, -19.43]}, {"time": 1.6333, "value": -19.43, "curve": "stepped"}, {"time": 2.4333, "value": -19.43, "curve": [2.456, -19.43, 2.478, -46.65]}, {"time": 2.5, "value": -46.65, "curve": [2.544, -46.65, 2.589, 37.53]}, {"time": 2.6333, "value": 37.53, "curve": [2.644, 37.53, 2.656, 35.84]}, {"time": 2.6667, "value": 33.5}]}, "Mouth": {"rotate": [{}], "translate": [{"x": 1.03}], "scale": [{"y": -0.445}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot": {"translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "blot_drops_control": {"translate": [{}]}, "Eye_L": {"translate": [{"x": 0.02, "y": 5.97, "curve": "stepped"}, {"time": 0.9, "x": 0.02, "y": 5.97, "curve": [0.944, 0.02, 0.989, -0.02, 0.944, 5.97, 0.989, -6.99]}, {"time": 1.0333, "x": -0.02, "y": -6.99, "curve": "stepped"}, {"time": 2.3667, "x": -0.02, "y": -6.99, "curve": [2.411, -0.02, 2.456, 0.02, 2.411, -6.99, 2.456, 5.97]}, {"time": 2.5, "x": 0.02, "y": 5.97}], "scale": [{}]}, "Eye_R": {"translate": [{"x": 0.02, "y": 5.97, "curve": "stepped"}, {"time": 0.9, "x": 0.02, "y": 5.97, "curve": [0.944, 0.02, 0.989, -0.02, 0.944, 5.97, 0.989, -6.99]}, {"time": 1.0333, "x": -0.02, "y": -6.99, "curve": "stepped"}, {"time": 2.3667, "x": -0.02, "y": -6.99, "curve": [2.411, -0.02, 2.456, 0.02, 2.411, -6.99, 2.456, 5.97]}, {"time": 2.5, "x": 0.02, "y": 5.97}], "scale": [{}]}, "Eyelid_R": {"translate": [{"y": 2.34}]}, "Eyelid_L": {"translate": [{"y": 2.34}]}, "Cntr_control": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.089, 0, 0.111, 1.82]}, {"time": 0.1333, "value": 1.82, "curve": [0.172, 1.82, 0.228, -0.79]}, {"time": 0.2667, "value": -0.79, "curve": [0.311, -0.79, 0.356, 1.13]}, {"time": 0.4, "value": 1.13, "curve": [0.444, 1.13, 0.489, 0]}, {"time": 0.5333, "curve": [0.565, 0, 0.635, 1.59]}, {"time": 0.6667, "value": 1.59, "curve": [0.7, 1.59, 0.733, -0.79]}, {"time": 0.7667, "value": -0.79, "curve": [0.811, -0.79, 0.856, 0]}, {"time": 0.9, "curve": [1.09, 0, 1.396, -1.13]}, {"time": 1.4333, "value": -1.13, "curve": [1.478, -1.13, 1.522, 0]}, {"time": 1.5667, "curve": [1.589, 0, 1.611, -2.7]}, {"time": 1.6333, "value": -2.7, "curve": [1.667, -2.7, 1.7, 0.56]}, {"time": 1.7333, "value": 0.56, "curve": [1.778, 0.56, 1.822, -1.13]}, {"time": 1.8667, "value": -1.13, "curve": [1.941, -1.13, 2.225, 0]}, {"time": 2.3, "curve": [2.322, 0, 2.344, 1.59]}, {"time": 2.3667, "value": 1.59, "curve": [2.4, 1.59, 2.433, -0.79]}, {"time": 2.4667, "value": -0.79, "curve": [2.511, -0.79, 2.556, 0]}, {"time": 2.6}]}, "Eyelid_L2": {"translate": [{"y": 7.36}]}, "Eyelid_R2": {"translate": [{"y": 7.36}]}}}, "t1_IDLE2": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body": {"attachment": [{"name": "Body"}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Ear_L": {"attachment": [{"name": "Ear_L"}]}, "Ear_L_outline": {"attachment": [{"name": "Ear_L_outline"}]}, "Ear_R": {"attachment": [{"name": "Ear_R"}]}, "Ear_R_outline": {"attachment": [{"name": "Ear_R_outline"}]}, "Eye_Eyelid_L": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_L2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_R": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_R2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_L": {"attachment": [{"name": "Eye_L"}]}, "Eye_R": {"attachment": [{"name": "Eye_R"}]}, "Light_from_Ray": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Light_from_Ray"}]}, "Light_Glow": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Light_Glow"}]}, "Light_Ray": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Light_Ray"}]}, "Light_Ray2": {"rgba": [{"color": "ffffff00"}]}, "Lips": {"attachment": [{"name": "Lips"}]}, "Mouth": {"attachment": [{"name": "Mouth"}]}, "Pupil_L": {"attachment": [{"name": "Pupil_L"}]}, "Pupil_R": {"attachment": [{"name": "Pupil_R"}]}, "Tongue": {"attachment": [{"name": "Tongue"}]}, "Tooth_01": {"attachment": [{"name": "Tooth_01"}]}, "Tooth_02": {"attachment": [{"name": "Tooth_02"}]}, "Tooth_03": {"attachment": [{"name": "Tooth_03"}]}, "Ufo_Body": {"attachment": [{"name": "Ufo_Body"}]}, "Ufo_Body_Outline": {"attachment": [{"name": "Ufo_Body_Outline"}]}}, "bones": {"Cntr": {"rotate": [{}], "translate": [{"y": -2.92, "curve": [0.222, 0, 0.444, 0, 0.222, -2.92, 0.444, 4.92]}, {"time": 0.6667, "y": 4.92, "curve": [0.889, 0, 1.111, 0, 0.889, 4.92, 1.111, -2.92]}, {"time": 1.3333, "y": -2.92, "curve": [1.556, 0, 1.778, 0, 1.556, -2.92, 1.778, 4.92]}, {"time": 2, "y": 4.92, "curve": [2.222, 0, 2.444, 0, 2.222, 4.92, 2.444, -2.92]}, {"time": 2.6667, "y": -2.92}], "scale": [{"x": 1.015, "y": 1.015, "curve": [0.113, 1.007, 0.223, 1, 0.113, 1.007, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1.029, 0.556, 1, 0.778, 1.029]}, {"time": 1, "x": 1.029, "y": 1.029, "curve": [1.112, 1.029, 1.222, 1.022, 1.112, 1.029, 1.222, 1.022]}, {"time": 1.3333, "x": 1.015, "y": 1.015, "curve": [1.446, 1.007, 1.556, 1, 1.446, 1.007, 1.556, 1]}, {"time": 1.6667, "curve": [1.889, 1, 2.111, 1.029, 1.889, 1, 2.111, 1.029]}, {"time": 2.3333, "x": 1.029, "y": 1.029, "curve": [2.445, 1.029, 2.557, 1.022, 2.445, 1.029, 2.557, 1.022]}, {"time": 2.6667, "x": 1.015, "y": 1.015}]}, "Tongue_tip": {"rotate": [{"value": 35.39, "curve": [0.056, 19.55, 0.111, -1.51]}, {"time": 0.1667, "value": -1.51, "curve": [0.222, -1.51, 0.278, 52.59]}, {"time": 0.3333, "value": 52.59, "curve": [0.411, 52.59, 0.489, -1.51]}, {"time": 0.5667, "value": -1.51, "curve": [0.644, -1.51, 0.722, 52.59]}, {"time": 0.8, "value": 52.59, "curve": [0.878, 52.59, 0.956, -1.51]}, {"time": 1.0333, "value": -1.51, "curve": [1.1, -1.51, 1.167, 52.59]}, {"time": 1.2333, "value": 52.59, "curve": [1.267, 52.59, 1.3, 44.84]}, {"time": 1.3333, "value": 35.39, "curve": [1.389, 19.55, 1.445, -1.51]}, {"time": 1.5, "value": -1.51, "curve": [1.556, -1.51, 1.611, 52.59]}, {"time": 1.6667, "value": 52.59, "curve": [1.744, 52.59, 1.822, -1.51]}, {"time": 1.9, "value": -1.51, "curve": [1.978, -1.51, 2.056, 52.59]}, {"time": 2.1333, "value": 52.59, "curve": [2.211, 52.59, 2.289, -1.51]}, {"time": 2.3667, "value": -1.51, "curve": [2.433, -1.51, 2.5, 52.59]}, {"time": 2.5667, "value": 52.59, "curve": [2.6, 52.59, 2.634, 44.96]}, {"time": 2.6667, "value": 35.39}], "translate": [{"x": 6.82, "y": 1.51, "curve": [0.056, 2.5, 0.111, -3.26, 0.056, -2.54, 0.111, -7.91]}, {"time": 0.1667, "x": -3.26, "y": -7.91, "curve": [0.222, -3.26, 0.278, 11.53, 0.222, -7.91, 0.278, 5.9]}, {"time": 0.3333, "x": 11.53, "y": 5.9, "curve": [0.411, 11.53, 0.489, -3.26, 0.411, 5.9, 0.489, -7.91]}, {"time": 0.5667, "x": -3.26, "y": -7.91, "curve": [0.644, -3.26, 0.722, 11.53, 0.644, -7.91, 0.722, 5.9]}, {"time": 0.8, "x": 11.53, "y": 5.9, "curve": [0.878, 11.53, 0.956, -3.26, 0.878, 5.9, 0.956, -7.91]}, {"time": 1.0333, "x": -3.26, "y": -7.91, "curve": [1.1, -3.26, 1.167, 11.53, 1.1, -7.91, 1.167, 5.9]}, {"time": 1.2333, "x": 11.53, "y": 5.9, "curve": [1.267, 11.53, 1.3, 9.41, 1.267, 5.9, 1.3, 3.92]}, {"time": 1.3333, "x": 6.82, "y": 1.51, "curve": [1.389, 2.5, 1.445, -3.26, 1.389, -2.54, 1.445, -7.91]}, {"time": 1.5, "x": -3.26, "y": -7.91, "curve": [1.556, -3.26, 1.611, 11.53, 1.556, -7.91, 1.611, 5.9]}, {"time": 1.6667, "x": 11.53, "y": 5.9, "curve": [1.744, 11.53, 1.822, -3.26, 1.744, 5.9, 1.822, -7.91]}, {"time": 1.9, "x": -3.26, "y": -7.91, "curve": [1.978, -3.26, 2.056, 11.53, 1.978, -7.91, 2.056, 5.9]}, {"time": 2.1333, "x": 11.53, "y": 5.9, "curve": [2.211, 11.53, 2.289, -3.26, 2.211, 5.9, 2.289, -7.91]}, {"time": 2.3667, "x": -3.26, "y": -7.91, "curve": [2.433, -3.26, 2.5, 11.53, 2.433, -7.91, 2.5, 5.9]}, {"time": 2.5667, "x": 11.53, "y": 5.9, "curve": [2.6, 11.53, 2.634, 9.44, 2.6, 5.9, 2.634, 3.95]}, {"time": 2.6667, "x": 6.82, "y": 1.51}]}, "Pupil_R": {"translate": [{"x": -1.01, "y": -3.93, "curve": "stepped"}, {"time": 0.3667, "x": -1.01, "y": -3.93}, {"time": 0.4333, "x": 1.64, "y": -3.76, "curve": "stepped"}, {"time": 0.7, "x": 1.64, "y": -3.76}, {"time": 0.7667, "x": -1.9, "y": -3.93, "curve": "stepped"}, {"time": 1.4333, "x": -1.9, "y": -3.93}, {"time": 1.5, "x": 0.14, "y": -4.23, "curve": "stepped"}, {"time": 1.7, "x": 0.14, "y": -4.23}, {"time": 1.7667, "x": -4.46, "y": -3.47, "curve": "stepped"}, {"time": 2.4333, "x": -4.46, "y": -3.47}, {"time": 2.5, "x": -1.01, "y": -3.93}], "scale": [{}]}, "Pupil_L": {"translate": [{"x": 2.53, "y": -2.99, "curve": "stepped"}, {"time": 0.3667, "x": 2.53, "y": -2.99}, {"time": 0.4333, "x": 5.18, "y": -2.83, "curve": "stepped"}, {"time": 0.7, "x": 5.18, "y": -2.83}, {"time": 0.7667, "x": 1.64, "y": -2.99, "curve": "stepped"}, {"time": 1.4333, "x": 1.64, "y": -2.99}, {"time": 1.5, "x": 3.67, "y": -3.3, "curve": "stepped"}, {"time": 1.7, "x": 3.67, "y": -3.3}, {"time": 1.7667, "x": -0.93, "y": -2.53, "curve": "stepped"}, {"time": 2.4333, "x": -0.93, "y": -2.53}, {"time": 2.5, "x": 2.53, "y": -2.99}], "scale": [{}]}, "Body": {"rotate": [{"value": -0.2}], "translate": [{"y": 3.68, "curve": [0.075, 0, 0.125, 0, 0.075, 3.68, 0.125, 8.75]}, {"time": 0.2, "y": 8.75, "curve": "stepped"}, {"time": 0.2333, "y": 8.75, "curve": [0.308, 0, 0.325, 0, 0.308, 8.75, 0.325, 3.68]}, {"time": 0.4, "y": 3.68, "curve": "stepped"}, {"time": 0.4333, "y": 3.68, "curve": [0.508, 0, 0.559, 0, 0.508, 3.68, 0.559, 8.75]}, {"time": 0.6333, "y": 8.75, "curve": "stepped"}, {"time": 0.6667, "y": 8.75, "curve": [0.741, 0, 0.792, 0, 0.741, 8.75, 0.792, 3.68]}, {"time": 0.8667, "y": 3.68, "curve": "stepped"}, {"time": 0.9, "y": 3.68, "curve": [0.975, 0, 1.025, 0, 0.975, 3.68, 1.025, 8.75]}, {"time": 1.1, "y": 8.75, "curve": "stepped"}, {"time": 1.1333, "y": 8.75, "curve": [1.208, 0, 1.225, 0, 1.208, 8.75, 1.225, 3.68]}, {"time": 1.3, "y": 3.68, "curve": "stepped"}, {"time": 1.3333, "y": 3.68, "curve": [1.408, 0, 1.459, 0, 1.408, 3.68, 1.459, 8.75]}, {"time": 1.5333, "y": 8.75, "curve": "stepped"}, {"time": 1.5667, "y": 8.75, "curve": [1.641, 0, 1.659, 0, 1.641, 8.75, 1.659, 3.68]}, {"time": 1.7333, "y": 3.68, "curve": "stepped"}, {"time": 1.7667, "y": 3.68, "curve": [1.841, 0, 1.892, 0, 1.841, 3.68, 1.892, 8.75]}, {"time": 1.9667, "y": 8.75, "curve": "stepped"}, {"time": 2, "y": 8.75, "curve": [2.075, 0, 2.125, 0, 2.075, 8.75, 2.125, 3.68]}, {"time": 2.2, "y": 3.68, "curve": "stepped"}, {"time": 2.2333, "y": 3.68, "curve": [2.308, 0, 2.359, 0, 2.308, 3.68, 2.359, 8.75]}, {"time": 2.4333, "y": 8.75, "curve": "stepped"}, {"time": 2.4667, "y": 8.75, "curve": [2.541, 0, 2.559, 0, 2.541, 8.75, 2.559, 3.68]}, {"time": 2.6333, "y": 3.68, "curve": "stepped"}, {"time": 2.6667, "y": 3.68}], "scale": [{"x": 1.05, "y": 0.987, "curve": [0.044, 1.05, 0.089, 1.05, 0.044, 0.972, 0.089, 0.95]}, {"time": 0.1333, "x": 1.05, "y": 0.95, "curve": [0.211, 1.05, 0.289, 1.05, 0.211, 0.95, 0.289, 1]}, {"time": 0.3667, "x": 1.05, "curve": [0.433, 1.05, 0.5, 1.05, 0.433, 1, 0.5, 0.95]}, {"time": 0.5667, "x": 1.05, "y": 0.95, "curve": [0.644, 1.05, 0.722, 1.05, 0.644, 0.95, 0.722, 1]}, {"time": 0.8, "x": 1.05, "curve": [0.878, 1.05, 0.956, 1.05, 0.878, 1, 0.956, 0.95]}, {"time": 1.0333, "x": 1.05, "y": 0.95, "curve": [1.111, 1.05, 1.189, 1.05, 1.111, 0.95, 1.189, 1]}, {"time": 1.2667, "x": 1.05, "curve": [1.289, 1.05, 1.311, 1.05, 1.289, 1, 1.311, 0.994]}, {"time": 1.3333, "x": 1.05, "y": 0.987, "curve": [1.378, 1.05, 1.422, 1.05, 1.378, 0.972, 1.422, 0.95]}, {"time": 1.4667, "x": 1.05, "y": 0.95, "curve": [1.544, 1.05, 1.622, 1.05, 1.544, 0.95, 1.622, 1]}, {"time": 1.7, "x": 1.05, "curve": [1.767, 1.05, 1.833, 1.05, 1.767, 1, 1.833, 0.95]}, {"time": 1.9, "x": 1.05, "y": 0.95, "curve": [1.978, 1.05, 2.056, 1.05, 1.978, 0.95, 2.056, 1]}, {"time": 2.1333, "x": 1.05, "curve": [2.211, 1.05, 2.289, 1.05, 2.211, 1, 2.289, 0.95]}, {"time": 2.3667, "x": 1.05, "y": 0.95, "curve": [2.444, 1.05, 2.522, 1.05, 2.444, 0.95, 2.522, 1]}, {"time": 2.6, "x": 1.05, "curve": [2.622, 1.05, 2.644, 1.05, 2.622, 1, 2.644, 0.994]}, {"time": 2.6667, "x": 1.05, "y": 0.987}]}, "Body2": {"rotate": [{}], "translate": [{"x": -5.32}]}, "Ear_R": {"rotate": [{"value": 3.37, "curve": [0.034, -6.57, 0.067, -13.06]}, {"time": 0.1, "value": -13.06, "curve": [0.167, -13.06, 0.267, 9.01]}, {"time": 0.3333, "value": 9.01, "curve": [0.405, 9.01, 0.495, -13.06]}, {"time": 0.5667, "value": -13.06, "curve": [0.638, -13.06, 0.728, 9.01]}, {"time": 0.8, "value": 9.01, "curve": [0.867, 9.01, 0.966, -13.06]}, {"time": 1.0333, "value": -13.06, "curve": [1.128, -13.06, 1.205, 9.01]}, {"time": 1.3, "value": 9.01, "curve": [1.311, 9.01, 1.322, 6.66]}, {"time": 1.3333, "value": 3.37, "curve": [1.367, -6.57, 1.4, -13.06]}, {"time": 1.4333, "value": -13.06, "curve": [1.5, -13.06, 1.6, 9.01]}, {"time": 1.6667, "value": 9.01, "curve": [1.738, 9.01, 1.828, -13.06]}, {"time": 1.9, "value": -13.06, "curve": [1.972, -13.06, 2.062, 9.01]}, {"time": 2.1333, "value": 9.01, "curve": [2.2, 9.01, 2.3, -13.06]}, {"time": 2.3667, "value": -13.06, "curve": [2.461, -13.06, 2.539, 9.01]}, {"time": 2.6333, "value": 9.01, "curve": [2.645, 9.01, 2.656, 6.75]}, {"time": 2.6667, "value": 3.37}]}, "Ear_R2": {"rotate": [{"value": 12.06, "curve": [0.011, 12.97, 0.022, 13.69]}, {"time": 0.0333, "value": 13.69, "curve": [0.078, 13.69, 0.122, -13.9]}, {"time": 0.1667, "value": -13.9, "curve": [0.233, -13.9, 0.333, 13.69]}, {"time": 0.4, "value": 13.69, "curve": [0.472, 13.69, 0.562, -13.9]}, {"time": 0.6333, "value": -13.9, "curve": [0.705, -13.9, 0.795, 13.69]}, {"time": 0.8667, "value": 13.69, "curve": [0.934, 13.69, 1.033, -13.9]}, {"time": 1.1, "value": -13.9, "curve": [1.184, -13.9, 1.256, 5.86]}, {"time": 1.3333, "value": 12.06, "curve": [1.345, 12.97, 1.356, 13.69]}, {"time": 1.3667, "value": 13.69, "curve": [1.411, 13.69, 1.456, -13.9]}, {"time": 1.5, "value": -13.9, "curve": [1.567, -13.9, 1.667, 13.69]}, {"time": 1.7333, "value": 13.69, "curve": [1.805, 13.69, 1.895, -13.9]}, {"time": 1.9667, "value": -13.9, "curve": [2.038, -13.9, 2.128, 13.69]}, {"time": 2.2, "value": 13.69, "curve": [2.267, 13.69, 2.366, -13.9]}, {"time": 2.4333, "value": -13.9, "curve": [2.517, -13.9, 2.587, 5.15]}, {"time": 2.6667, "value": 12.06}]}, "Ear_L": {"rotate": [{"value": -6.66, "curve": [0.034, 5.92, 0.067, 14.7]}, {"time": 0.1, "value": 14.7, "curve": [0.167, 14.7, 0.267, -13.78]}, {"time": 0.3333, "value": -13.78, "curve": [0.405, -13.78, 0.495, 14.7]}, {"time": 0.5667, "value": 14.7, "curve": [0.638, 14.7, 0.728, -13.78]}, {"time": 0.8, "value": -13.78, "curve": [0.867, -13.78, 0.966, 14.7]}, {"time": 1.0333, "value": 14.7, "curve": [1.128, 14.7, 1.205, -13.78]}, {"time": 1.3, "value": -13.78, "curve": [1.311, -13.78, 1.322, -10.81]}, {"time": 1.3333, "value": -6.66, "curve": [1.367, 5.92, 1.4, 14.7]}, {"time": 1.4333, "value": 14.7, "curve": [1.5, 14.7, 1.6, -13.78]}, {"time": 1.6667, "value": -13.78, "curve": [1.738, -13.78, 1.828, 14.7]}, {"time": 1.9, "value": 14.7, "curve": [1.972, 14.7, 2.062, -13.78]}, {"time": 2.1333, "value": -13.78, "curve": [2.2, -13.78, 2.3, 14.7]}, {"time": 2.3667, "value": 14.7, "curve": [2.461, 14.7, 2.539, -13.78]}, {"time": 2.6333, "value": -13.78, "curve": [2.645, -13.78, 2.656, -10.93]}, {"time": 2.6667, "value": -6.66}]}, "Ear_L2": {"rotate": [{"value": -10.87, "curve": [0.011, -11.99, 0.022, -12.87]}, {"time": 0.0333, "value": -12.87, "curve": [0.078, -12.87, 0.122, 17.18]}, {"time": 0.1667, "value": 17.18, "curve": [0.233, 17.18, 0.333, -12.87]}, {"time": 0.4, "value": -12.87, "curve": [0.472, -12.87, 0.562, 17.18]}, {"time": 0.6333, "value": 17.18, "curve": [0.705, 17.18, 0.795, -12.87]}, {"time": 0.8667, "value": -12.87, "curve": [0.934, -12.87, 1.033, 17.18]}, {"time": 1.1, "value": 17.18, "curve": [1.184, 17.18, 1.256, -3.3]}, {"time": 1.3333, "value": -10.87, "curve": [1.345, -11.99, 1.356, -12.87]}, {"time": 1.3667, "value": -12.87, "curve": [1.411, -12.87, 1.456, 17.18]}, {"time": 1.5, "value": 17.18, "curve": [1.567, 17.18, 1.667, -12.87]}, {"time": 1.7333, "value": -12.87, "curve": [1.805, -12.87, 1.895, 17.18]}, {"time": 1.9667, "value": 17.18, "curve": [2.038, 17.18, 2.128, -12.87]}, {"time": 2.2, "value": -12.87, "curve": [2.267, -12.87, 2.366, 17.18]}, {"time": 2.4333, "value": 17.18, "curve": [2.517, 17.18, 2.587, -2.43]}, {"time": 2.6667, "value": -10.87}]}, "Mouth": {"rotate": [{}], "translate": [{"x": 1.03, "curve": [0.023, 0.44, 0.045, 0, 0.023, 0, 0.045, 0]}, {"time": 0.0667, "curve": [0.133, 0, 0.2, 5.13, 0.133, 0, 0.2, -0.02]}, {"time": 0.2667, "x": 5.13, "y": -0.02, "curve": [0.344, 5.13, 0.422, 0, 0.344, -0.02, 0.422, 0]}, {"time": 0.5, "curve": [0.578, 0, 0.656, 5.13, 0.578, 0, 0.656, -0.02]}, {"time": 0.7333, "x": 5.13, "y": -0.02, "curve": [0.811, 5.13, 0.889, 0, 0.811, -0.02, 0.889, 0]}, {"time": 0.9667, "curve": [1.033, 0, 1.1, 5.13, 1.033, 0, 1.1, -0.02]}, {"time": 1.1667, "x": 5.13, "y": -0.02, "curve": [1.222, 5.13, 1.278, 2.48, 1.222, -0.02, 1.278, -0.01]}, {"time": 1.3333, "x": 1.03, "curve": [1.356, 0.44, 1.378, 0, 1.356, 0, 1.378, 0]}, {"time": 1.4, "curve": [1.467, 0, 1.533, 5.13, 1.467, 0, 1.533, -0.02]}, {"time": 1.6, "x": 5.13, "y": -0.02, "curve": [1.678, 5.13, 1.756, 0, 1.678, -0.02, 1.756, 0]}, {"time": 1.8333, "curve": [1.911, 0, 1.989, 5.13, 1.911, 0, 1.989, -0.02]}, {"time": 2.0667, "x": 5.13, "y": -0.02, "curve": [2.144, 5.13, 2.222, 0, 2.144, -0.02, 2.222, 0]}, {"time": 2.3, "curve": [2.367, 0, 2.433, 5.13, 2.367, 0, 2.433, -0.02]}, {"time": 2.5, "x": 5.13, "y": -0.02, "curve": [2.556, 5.13, 2.611, 2.52, 2.556, -0.02, 2.611, -0.01]}, {"time": 2.6667, "x": 1.03}], "scale": [{"curve": [0.067, 1, 0.133, 1.044, 0.067, 1, 0.133, 0.698]}, {"time": 0.2, "x": 1.044, "y": 0.698, "curve": [0.278, 1.044, 0.356, 1, 0.278, 0.698, 0.356, 1]}, {"time": 0.4333, "curve": [0.511, 1, 0.589, 1.044, 0.511, 1, 0.589, 0.698]}, {"time": 0.6667, "x": 1.044, "y": 0.698, "curve": [0.744, 1.044, 0.822, 1, 0.744, 0.698, 0.822, 1]}, {"time": 0.9, "curve": [0.967, 1, 1.033, 1.044, 0.967, 1, 1.033, 0.698]}, {"time": 1.1, "x": 1.044, "y": 0.698, "curve": [1.178, 1.044, 1.256, 1, 1.178, 0.698, 1.256, 1]}, {"time": 1.3333, "curve": [1.4, 1, 1.467, 1.044, 1.4, 1, 1.467, 0.698]}, {"time": 1.5333, "x": 1.044, "y": 0.698, "curve": [1.611, 1.044, 1.689, 1, 1.611, 0.698, 1.689, 1]}, {"time": 1.7667, "curve": [1.844, 1, 1.922, 1.044, 1.844, 1, 1.922, 0.698]}, {"time": 2, "x": 1.044, "y": 0.698, "curve": [2.078, 1.044, 2.156, 1, 2.078, 0.698, 2.156, 1]}, {"time": 2.2333, "curve": [2.3, 1, 2.367, 1.044, 2.3, 1, 2.367, 0.698]}, {"time": 2.4333, "x": 1.044, "y": 0.698, "curve": [2.511, 1.044, 2.589, 1, 2.511, 0.698, 2.589, 1]}, {"time": 2.6667}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot": {"translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "blot_drops_control": {"translate": [{}]}, "Eye_L": {"translate": [{"x": 0.02, "y": 5.97}], "scale": [{}]}, "Eye_R": {"translate": [{"x": 0.02, "y": 5.97}], "scale": [{}]}, "Eyelid_R": {"translate": [{"y": 15.44}]}, "Eyelid_L": {"translate": [{"y": 15.44}]}, "Cntr_control": {"rotate": [{}]}, "Eyelid_L2": {"translate": [{"y": -1.5}]}, "Eyelid_R2": {"translate": [{"y": -1.5}]}}}, "t1_IDLE3": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body": {"attachment": [{"name": "Body"}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Ear_L": {"attachment": [{"name": "Ear_L"}]}, "Ear_L_outline": {"attachment": [{"name": "Ear_L_outline"}]}, "Ear_R": {"attachment": [{"name": "Ear_R"}]}, "Ear_R_outline": {"attachment": [{"name": "Ear_R_outline"}]}, "Eye_Eyelid_L": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_L2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_R": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_R2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_L": {"attachment": [{"name": "Eye_L"}]}, "Eye_R": {"attachment": [{"name": "Eye_R"}]}, "Light_from_Ray": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Light_from_Ray"}]}, "Light_Glow": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Light_Glow"}]}, "Light_Ray": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Light_Ray"}]}, "Light_Ray2": {"rgba": [{"color": "ffffffff", "curve": [0.022, 1, 0.044, 1, 0.022, 1, 0.044, 1, 0.022, 1, 0.044, 1, 0.022, 1, 0.044, 0]}, {"time": 0.0667, "color": "ffffff00", "curve": [0.111, 1, 0.156, 1, 0.111, 1, 0.156, 1, 0.111, 1, 0.156, 1, 0.111, 0, 0.156, 1]}, {"time": 0.2, "color": "ffffffff", "curve": [0.222, 1, 0.244, 1, 0.222, 1, 0.244, 1, 0.222, 1, 0.244, 1, 0.222, 1, 0.244, 0]}, {"time": 0.2667, "color": "ffffff00", "curve": [0.311, 1, 0.356, 1, 0.311, 1, 0.356, 1, 0.311, 1, 0.356, 1, 0.311, 0, 0.356, 1]}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff", "curve": [0.489, 1, 0.511, 1, 0.489, 1, 0.511, 1, 0.489, 1, 0.511, 1, 0.489, 1, 0.511, 0]}, {"time": 0.5333, "color": "ffffff00", "curve": [0.578, 1, 0.622, 1, 0.578, 1, 0.622, 1, 0.578, 1, 0.622, 1, 0.578, 0, 0.622, 1]}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff", "curve": [0.789, 1, 0.811, 1, 0.789, 1, 0.811, 1, 0.789, 1, 0.811, 1, 0.789, 1, 0.811, 0]}, {"time": 0.8333, "color": "ffffff00", "curve": [0.878, 1, 0.922, 1, 0.878, 1, 0.922, 1, 0.878, 1, 0.922, 1, 0.878, 0, 0.922, 1]}, {"time": 0.9667, "color": "ffffffff", "curve": [0.989, 1, 1.011, 1, 0.989, 1, 1.011, 1, 0.989, 1, 1.011, 1, 0.989, 1, 1.011, 0]}, {"time": 1.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1, "color": "ffffff00", "curve": [1.144, 1, 1.189, 1, 1.144, 1, 1.189, 1, 1.144, 1, 1.189, 1, 1.144, 0, 1.189, 1]}, {"time": 1.2333, "color": "ffffffff", "curve": [1.267, 1, 1.3, 1, 1.267, 1, 1.3, 1, 1.267, 1, 1.3, 1, 1.267, 1, 1.3, 0]}, {"time": 1.3333, "color": "ffffff00", "curve": [1.389, 1, 1.444, 1, 1.389, 1, 1.444, 1, 1.389, 1, 1.444, 1, 1.389, 0, 1.444, 1]}, {"time": 1.5, "color": "ffffffff", "curve": [1.522, 1, 1.544, 1, 1.522, 1, 1.544, 1, 1.522, 1, 1.544, 1, 1.522, 1, 1.544, 0]}, {"time": 1.5667, "color": "ffffff00", "curve": [1.611, 1, 1.656, 1, 1.611, 1, 1.656, 1, 1.611, 1, 1.656, 1, 1.611, 0, 1.656, 1]}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8667, "color": "ffffffff", "curve": [1.889, 1, 1.911, 1, 1.889, 1, 1.911, 1, 1.889, 1, 1.911, 1, 1.889, 1, 1.911, 0]}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9667, "color": "ffffff00", "curve": [2.011, 1, 2.056, 1, 2.011, 1, 2.056, 1, 2.011, 1, 2.056, 1, 2.011, 0, 2.056, 1]}, {"time": 2.1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff", "curve": [2.222, 1, 2.244, 1, 2.222, 1, 2.244, 1, 2.222, 1, 2.244, 1, 2.222, 1, 2.244, 0]}, {"time": 2.2667, "color": "ffffff00", "curve": [2.311, 1, 2.356, 1, 2.311, 1, 2.356, 1, 2.311, 1, 2.356, 1, 2.311, 0, 2.356, 1]}, {"time": 2.4, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff", "curve": [2.456, 1, 2.478, 1, 2.456, 1, 2.478, 1, 2.456, 1, 2.478, 1, 2.456, 1, 2.478, 0]}, {"time": 2.5, "color": "ffffff00", "curve": [2.544, 1, 2.589, 1, 2.544, 1, 2.589, 1, 2.544, 1, 2.589, 1, 2.544, 0, 2.589, 1]}, {"time": 2.6333, "color": "ffffffff"}]}, "Lips": {"attachment": [{"name": "Lips"}]}, "Mouth": {"attachment": [{"name": "Mouth"}]}, "Pupil_L": {"attachment": [{"name": "Pupil_L"}]}, "Pupil_R": {"attachment": [{"name": "Pupil_R"}]}, "Tongue": {"attachment": [{"name": "Tongue"}]}, "Tooth_01": {"attachment": [{"name": "Tooth_01"}]}, "Tooth_02": {"attachment": [{"name": "Tooth_02"}]}, "Tooth_03": {"attachment": [{"name": "Tooth_03"}]}, "Ufo_Body": {"attachment": [{"name": "Ufo_Body"}]}, "Ufo_Body_Outline": {"attachment": [{"name": "Ufo_Body_Outline"}]}}, "bones": {"Cntr": {"rotate": [{}], "translate": [{"y": -2.92, "curve": [0.222, 0, 0.444, 0, 0.222, -2.92, 0.444, 4.92]}, {"time": 0.6667, "y": 4.92, "curve": [0.889, 0, 1.111, 0, 0.889, 4.92, 1.111, -2.92]}, {"time": 1.3333, "y": -2.92, "curve": [1.556, 0, 1.778, 0, 1.556, -2.92, 1.778, 4.92]}, {"time": 2, "y": 4.92, "curve": [2.222, 0, 2.444, 0, 2.222, 4.92, 2.444, -2.92]}, {"time": 2.6667, "y": -2.92}], "scale": [{"x": 1.015, "y": 1.015, "curve": [0.113, 1.007, 0.223, 1, 0.113, 1.007, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1.029, 0.556, 1, 0.778, 1.029]}, {"time": 1, "x": 1.029, "y": 1.029, "curve": [1.112, 1.029, 1.222, 1.022, 1.112, 1.029, 1.222, 1.022]}, {"time": 1.3333, "x": 1.015, "y": 1.015, "curve": [1.446, 1.007, 1.556, 1, 1.446, 1.007, 1.556, 1]}, {"time": 1.6667, "curve": [1.889, 1, 2.111, 1.029, 1.889, 1, 2.111, 1.029]}, {"time": 2.3333, "x": 1.029, "y": 1.029, "curve": [2.445, 1.029, 2.557, 1.022, 2.445, 1.029, 2.557, 1.022]}, {"time": 2.6667, "x": 1.015, "y": 1.015}]}, "Tongue_tip": {"rotate": [{"value": 35.39, "curve": [0.056, 19.55, 0.111, -1.51]}, {"time": 0.1667, "value": -1.51, "curve": [0.222, -1.51, 0.278, 52.59]}, {"time": 0.3333, "value": 52.59, "curve": [0.411, 52.59, 0.489, -1.51]}, {"time": 0.5667, "value": -1.51, "curve": [0.644, -1.51, 0.722, 52.59]}, {"time": 0.8, "value": 52.59, "curve": [0.878, 52.59, 0.956, -1.51]}, {"time": 1.0333, "value": -1.51, "curve": [1.1, -1.51, 1.167, 52.59]}, {"time": 1.2333, "value": 52.59, "curve": [1.267, 52.59, 1.3, 44.84]}, {"time": 1.3333, "value": 35.39, "curve": [1.389, 19.55, 1.445, -1.51]}, {"time": 1.5, "value": -1.51, "curve": [1.556, -1.51, 1.611, 52.59]}, {"time": 1.6667, "value": 52.59, "curve": [1.744, 52.59, 1.822, -1.51]}, {"time": 1.9, "value": -1.51, "curve": [1.978, -1.51, 2.056, 52.59]}, {"time": 2.1333, "value": 52.59, "curve": [2.211, 52.59, 2.289, -1.51]}, {"time": 2.3667, "value": -1.51, "curve": [2.433, -1.51, 2.5, 52.59]}, {"time": 2.5667, "value": 52.59, "curve": [2.6, 52.59, 2.634, 44.96]}, {"time": 2.6667, "value": 35.39}], "translate": [{"x": 6.82, "y": 1.51, "curve": [0.056, 2.5, 0.111, -3.26, 0.056, -2.54, 0.111, -7.91]}, {"time": 0.1667, "x": -3.26, "y": -7.91, "curve": [0.222, -3.26, 0.278, 11.53, 0.222, -7.91, 0.278, 5.9]}, {"time": 0.3333, "x": 11.53, "y": 5.9, "curve": [0.411, 11.53, 0.489, -3.26, 0.411, 5.9, 0.489, -7.91]}, {"time": 0.5667, "x": -3.26, "y": -7.91, "curve": [0.644, -3.26, 0.722, 11.53, 0.644, -7.91, 0.722, 5.9]}, {"time": 0.8, "x": 11.53, "y": 5.9, "curve": [0.878, 11.53, 0.956, -3.26, 0.878, 5.9, 0.956, -7.91]}, {"time": 1.0333, "x": -3.26, "y": -7.91, "curve": [1.1, -3.26, 1.167, 11.53, 1.1, -7.91, 1.167, 5.9]}, {"time": 1.2333, "x": 11.53, "y": 5.9, "curve": [1.267, 11.53, 1.3, 9.41, 1.267, 5.9, 1.3, 3.92]}, {"time": 1.3333, "x": 6.82, "y": 1.51, "curve": [1.389, 2.5, 1.445, -3.26, 1.389, -2.54, 1.445, -7.91]}, {"time": 1.5, "x": -3.26, "y": -7.91, "curve": [1.556, -3.26, 1.611, 11.53, 1.556, -7.91, 1.611, 5.9]}, {"time": 1.6667, "x": 11.53, "y": 5.9, "curve": [1.744, 11.53, 1.822, -3.26, 1.744, 5.9, 1.822, -7.91]}, {"time": 1.9, "x": -3.26, "y": -7.91, "curve": [1.978, -3.26, 2.056, 11.53, 1.978, -7.91, 2.056, 5.9]}, {"time": 2.1333, "x": 11.53, "y": 5.9, "curve": [2.211, 11.53, 2.289, -3.26, 2.211, 5.9, 2.289, -7.91]}, {"time": 2.3667, "x": -3.26, "y": -7.91, "curve": [2.433, -3.26, 2.5, 11.53, 2.433, -7.91, 2.5, 5.9]}, {"time": 2.5667, "x": 11.53, "y": 5.9, "curve": [2.6, 11.53, 2.634, 9.44, 2.6, 5.9, 2.634, 3.95]}, {"time": 2.6667, "x": 6.82, "y": 1.51}]}, "Pupil_R": {"translate": [{"x": -1.01, "y": -3.93, "curve": "stepped"}, {"time": 0.3667, "x": -1.01, "y": -3.93}, {"time": 0.4333, "x": 1.64, "y": -3.76, "curve": "stepped"}, {"time": 0.7, "x": 1.64, "y": -3.76}, {"time": 0.7667, "x": -1.9, "y": -3.93, "curve": "stepped"}, {"time": 1.4333, "x": -1.9, "y": -3.93}, {"time": 1.5, "x": 0.14, "y": -4.23, "curve": "stepped"}, {"time": 1.7, "x": 0.14, "y": -4.23}, {"time": 1.7667, "x": -4.46, "y": -3.47, "curve": "stepped"}, {"time": 2.4333, "x": -4.46, "y": -3.47}, {"time": 2.5, "x": -1.01, "y": -3.93}], "scale": [{}]}, "Pupil_L": {"translate": [{"x": 2.53, "y": -2.99, "curve": "stepped"}, {"time": 0.3667, "x": 2.53, "y": -2.99}, {"time": 0.4333, "x": 5.18, "y": -2.83, "curve": "stepped"}, {"time": 0.7, "x": 5.18, "y": -2.83}, {"time": 0.7667, "x": 1.64, "y": -2.99, "curve": "stepped"}, {"time": 1.4333, "x": 1.64, "y": -2.99}, {"time": 1.5, "x": 3.67, "y": -3.3, "curve": "stepped"}, {"time": 1.7, "x": 3.67, "y": -3.3}, {"time": 1.7667, "x": -0.93, "y": -2.53, "curve": "stepped"}, {"time": 2.4333, "x": -0.93, "y": -2.53}, {"time": 2.5, "x": 2.53, "y": -2.99}], "scale": [{}]}, "Body": {"rotate": [{"value": 4.8, "curve": [0.111, 4.8, 0.222, -5.2]}, {"time": 0.3333, "value": -5.2, "curve": [0.444, -5.2, 0.556, 4.8]}, {"time": 0.6667, "value": 4.8, "curve": [0.778, 4.8, 0.889, -5.2]}, {"time": 1, "value": -5.2, "curve": [1.111, -5.2, 1.222, 4.8]}, {"time": 1.3333, "value": 4.8, "curve": [1.444, 4.8, 1.556, -5.2]}, {"time": 1.6667, "value": -5.2, "curve": [1.778, -5.2, 1.889, 4.8]}, {"time": 2, "value": 4.8, "curve": [2.111, 4.8, 2.222, -5.2]}, {"time": 2.3333, "value": -5.2, "curve": [2.444, -5.2, 2.556, 4.8]}, {"time": 2.6667, "value": 4.8}], "translate": [{"y": 3.68, "curve": [0.075, 0, 0.125, 0, 0.075, 3.68, 0.125, 8.75]}, {"time": 0.2, "y": 8.75, "curve": "stepped"}, {"time": 0.2333, "y": 8.75, "curve": [0.308, 0, 0.325, 0, 0.308, 8.75, 0.325, 3.68]}, {"time": 0.4, "y": 3.68, "curve": "stepped"}, {"time": 0.4333, "y": 3.68, "curve": [0.508, 0, 0.559, 0, 0.508, 3.68, 0.559, 8.75]}, {"time": 0.6333, "y": 8.75, "curve": "stepped"}, {"time": 0.6667, "y": 8.75, "curve": [0.741, 0, 0.792, 0, 0.741, 8.75, 0.792, 3.68]}, {"time": 0.8667, "y": 3.68, "curve": "stepped"}, {"time": 0.9, "y": 3.68, "curve": [0.975, 0, 1.025, 0, 0.975, 3.68, 1.025, 8.75]}, {"time": 1.1, "y": 8.75, "curve": "stepped"}, {"time": 1.1333, "y": 8.75, "curve": [1.208, 0, 1.225, 0, 1.208, 8.75, 1.225, 3.68]}, {"time": 1.3, "y": 3.68, "curve": "stepped"}, {"time": 1.3333, "y": 3.68, "curve": [1.408, 0, 1.459, 0, 1.408, 3.68, 1.459, 8.75]}, {"time": 1.5333, "y": 8.75, "curve": "stepped"}, {"time": 1.5667, "y": 8.75, "curve": [1.641, 0, 1.659, 0, 1.641, 8.75, 1.659, 3.68]}, {"time": 1.7333, "y": 3.68, "curve": "stepped"}, {"time": 1.7667, "y": 3.68, "curve": [1.841, 0, 1.892, 0, 1.841, 3.68, 1.892, 8.75]}, {"time": 1.9667, "y": 8.75, "curve": "stepped"}, {"time": 2, "y": 8.75, "curve": [2.075, 0, 2.125, 0, 2.075, 8.75, 2.125, 3.68]}, {"time": 2.2, "y": 3.68, "curve": "stepped"}, {"time": 2.2333, "y": 3.68, "curve": [2.308, 0, 2.359, 0, 2.308, 3.68, 2.359, 8.75]}, {"time": 2.4333, "y": 8.75, "curve": "stepped"}, {"time": 2.4667, "y": 8.75, "curve": [2.541, 0, 2.559, 0, 2.541, 8.75, 2.559, 3.68]}, {"time": 2.6333, "y": 3.68, "curve": "stepped"}, {"time": 2.6667, "y": 3.68}], "scale": [{"x": 1.05, "y": 0.987, "curve": [0.044, 1.05, 0.089, 1.05, 0.044, 0.972, 0.089, 0.95]}, {"time": 0.1333, "x": 1.05, "y": 0.95, "curve": [0.211, 1.05, 0.289, 1.05, 0.211, 0.95, 0.289, 1]}, {"time": 0.3667, "x": 1.05, "curve": [0.433, 1.05, 0.5, 1.05, 0.433, 1, 0.5, 0.95]}, {"time": 0.5667, "x": 1.05, "y": 0.95, "curve": [0.644, 1.05, 0.722, 1.05, 0.644, 0.95, 0.722, 1]}, {"time": 0.8, "x": 1.05, "curve": [0.878, 1.05, 0.956, 1.05, 0.878, 1, 0.956, 0.95]}, {"time": 1.0333, "x": 1.05, "y": 0.95, "curve": [1.111, 1.05, 1.189, 1.05, 1.111, 0.95, 1.189, 1]}, {"time": 1.2667, "x": 1.05, "curve": [1.289, 1.05, 1.311, 1.05, 1.289, 1, 1.311, 0.994]}, {"time": 1.3333, "x": 1.05, "y": 0.987, "curve": [1.378, 1.05, 1.422, 1.05, 1.378, 0.972, 1.422, 0.95]}, {"time": 1.4667, "x": 1.05, "y": 0.95, "curve": [1.544, 1.05, 1.622, 1.05, 1.544, 0.95, 1.622, 1]}, {"time": 1.7, "x": 1.05, "curve": [1.767, 1.05, 1.833, 1.05, 1.767, 1, 1.833, 0.95]}, {"time": 1.9, "x": 1.05, "y": 0.95, "curve": [1.978, 1.05, 2.056, 1.05, 1.978, 0.95, 2.056, 1]}, {"time": 2.1333, "x": 1.05, "curve": [2.211, 1.05, 2.289, 1.05, 2.211, 1, 2.289, 0.95]}, {"time": 2.3667, "x": 1.05, "y": 0.95, "curve": [2.444, 1.05, 2.522, 1.05, 2.444, 0.95, 2.522, 1]}, {"time": 2.6, "x": 1.05, "curve": [2.622, 1.05, 2.644, 1.05, 2.622, 1, 2.644, 0.994]}, {"time": 2.6667, "x": 1.05, "y": 0.987}]}, "Body2": {"rotate": [{"curve": [0.056, 4.97, 0.111, 10]}, {"time": 0.1667, "value": 10, "curve": [0.278, 10, 0.389, -10]}, {"time": 0.5, "value": -10, "curve": [0.611, -10, 0.722, 10]}, {"time": 0.8333, "value": 10, "curve": [0.944, 10, 1.056, -10]}, {"time": 1.1667, "value": -10, "curve": [1.278, -10, 1.389, 10]}, {"time": 1.5, "value": 10, "curve": [1.611, 10, 1.722, -10]}, {"time": 1.8333, "value": -10, "curve": [1.944, -10, 2.056, 10]}, {"time": 2.1667, "value": 10, "curve": [2.278, 10, 2.389, -10]}, {"time": 2.5, "value": -10, "curve": [2.556, -10, 2.612, -5.03]}, {"time": 2.6667}], "translate": [{"x": -5.32}]}, "Ear_R": {"rotate": [{"value": 3.37, "curve": [0.034, -6.57, 0.067, -13.06]}, {"time": 0.1, "value": -13.06, "curve": [0.167, -13.06, 0.267, 9.01]}, {"time": 0.3333, "value": 9.01, "curve": [0.405, 9.01, 0.495, -13.06]}, {"time": 0.5667, "value": -13.06, "curve": [0.638, -13.06, 0.728, 9.01]}, {"time": 0.8, "value": 9.01, "curve": [0.867, 9.01, 0.966, -13.06]}, {"time": 1.0333, "value": -13.06, "curve": [1.128, -13.06, 1.205, 9.01]}, {"time": 1.3, "value": 9.01, "curve": [1.311, 9.01, 1.322, 6.66]}, {"time": 1.3333, "value": 3.37, "curve": [1.367, -6.57, 1.4, -13.06]}, {"time": 1.4333, "value": -13.06, "curve": [1.5, -13.06, 1.6, 9.01]}, {"time": 1.6667, "value": 9.01, "curve": [1.738, 9.01, 1.828, -13.06]}, {"time": 1.9, "value": -13.06, "curve": [1.972, -13.06, 2.062, 9.01]}, {"time": 2.1333, "value": 9.01, "curve": [2.2, 9.01, 2.3, -13.06]}, {"time": 2.3667, "value": -13.06, "curve": [2.461, -13.06, 2.539, 9.01]}, {"time": 2.6333, "value": 9.01, "curve": [2.645, 9.01, 2.656, 6.75]}, {"time": 2.6667, "value": 3.37}]}, "Ear_R2": {"rotate": [{"value": 12.06, "curve": [0.011, 12.97, 0.022, 13.69]}, {"time": 0.0333, "value": 13.69, "curve": [0.078, 13.69, 0.122, -13.9]}, {"time": 0.1667, "value": -13.9, "curve": [0.233, -13.9, 0.333, 13.69]}, {"time": 0.4, "value": 13.69, "curve": [0.472, 13.69, 0.562, -13.9]}, {"time": 0.6333, "value": -13.9, "curve": [0.705, -13.9, 0.795, 13.69]}, {"time": 0.8667, "value": 13.69, "curve": [0.934, 13.69, 1.033, -13.9]}, {"time": 1.1, "value": -13.9, "curve": [1.184, -13.9, 1.256, 5.86]}, {"time": 1.3333, "value": 12.06, "curve": [1.345, 12.97, 1.356, 13.69]}, {"time": 1.3667, "value": 13.69, "curve": [1.411, 13.69, 1.456, -13.9]}, {"time": 1.5, "value": -13.9, "curve": [1.567, -13.9, 1.667, 13.69]}, {"time": 1.7333, "value": 13.69, "curve": [1.805, 13.69, 1.895, -13.9]}, {"time": 1.9667, "value": -13.9, "curve": [2.038, -13.9, 2.128, 13.69]}, {"time": 2.2, "value": 13.69, "curve": [2.267, 13.69, 2.366, -13.9]}, {"time": 2.4333, "value": -13.9, "curve": [2.517, -13.9, 2.587, 5.15]}, {"time": 2.6667, "value": 12.06}]}, "Ear_L": {"rotate": [{"value": -6.66, "curve": [0.034, 5.92, 0.067, 14.7]}, {"time": 0.1, "value": 14.7, "curve": [0.167, 14.7, 0.267, -13.78]}, {"time": 0.3333, "value": -13.78, "curve": [0.405, -13.78, 0.495, 14.7]}, {"time": 0.5667, "value": 14.7, "curve": [0.638, 14.7, 0.728, -13.78]}, {"time": 0.8, "value": -13.78, "curve": [0.867, -13.78, 0.966, 14.7]}, {"time": 1.0333, "value": 14.7, "curve": [1.128, 14.7, 1.205, -13.78]}, {"time": 1.3, "value": -13.78, "curve": [1.311, -13.78, 1.322, -10.81]}, {"time": 1.3333, "value": -6.66, "curve": [1.367, 5.92, 1.4, 14.7]}, {"time": 1.4333, "value": 14.7, "curve": [1.5, 14.7, 1.6, -13.78]}, {"time": 1.6667, "value": -13.78, "curve": [1.738, -13.78, 1.828, 14.7]}, {"time": 1.9, "value": 14.7, "curve": [1.972, 14.7, 2.062, -13.78]}, {"time": 2.1333, "value": -13.78, "curve": [2.2, -13.78, 2.3, 14.7]}, {"time": 2.3667, "value": 14.7, "curve": [2.461, 14.7, 2.539, -13.78]}, {"time": 2.6333, "value": -13.78, "curve": [2.645, -13.78, 2.656, -10.93]}, {"time": 2.6667, "value": -6.66}]}, "Ear_L2": {"rotate": [{"value": -10.87, "curve": [0.011, -11.99, 0.022, -12.87]}, {"time": 0.0333, "value": -12.87, "curve": [0.078, -12.87, 0.122, 17.18]}, {"time": 0.1667, "value": 17.18, "curve": [0.233, 17.18, 0.333, -12.87]}, {"time": 0.4, "value": -12.87, "curve": [0.472, -12.87, 0.562, 17.18]}, {"time": 0.6333, "value": 17.18, "curve": [0.705, 17.18, 0.795, -12.87]}, {"time": 0.8667, "value": -12.87, "curve": [0.934, -12.87, 1.033, 17.18]}, {"time": 1.1, "value": 17.18, "curve": [1.184, 17.18, 1.256, -3.3]}, {"time": 1.3333, "value": -10.87, "curve": [1.345, -11.99, 1.356, -12.87]}, {"time": 1.3667, "value": -12.87, "curve": [1.411, -12.87, 1.456, 17.18]}, {"time": 1.5, "value": 17.18, "curve": [1.567, 17.18, 1.667, -12.87]}, {"time": 1.7333, "value": -12.87, "curve": [1.805, -12.87, 1.895, 17.18]}, {"time": 1.9667, "value": 17.18, "curve": [2.038, 17.18, 2.128, -12.87]}, {"time": 2.2, "value": -12.87, "curve": [2.267, -12.87, 2.366, 17.18]}, {"time": 2.4333, "value": 17.18, "curve": [2.517, 17.18, 2.587, -2.43]}, {"time": 2.6667, "value": -10.87}]}, "Mouth": {"rotate": [{}], "translate": [{"x": 1.03, "curve": [0.023, 0.44, 0.045, 0, 0.023, 0, 0.045, 0]}, {"time": 0.0667, "curve": [0.133, 0, 0.2, 5.13, 0.133, 0, 0.2, -0.02]}, {"time": 0.2667, "x": 5.13, "y": -0.02, "curve": [0.344, 5.13, 0.422, 0, 0.344, -0.02, 0.422, 0]}, {"time": 0.5, "curve": [0.578, 0, 0.656, 5.13, 0.578, 0, 0.656, -0.02]}, {"time": 0.7333, "x": 5.13, "y": -0.02, "curve": [0.811, 5.13, 0.889, 0, 0.811, -0.02, 0.889, 0]}, {"time": 0.9667, "curve": [1.033, 0, 1.1, 5.13, 1.033, 0, 1.1, -0.02]}, {"time": 1.1667, "x": 5.13, "y": -0.02, "curve": [1.222, 5.13, 1.278, 2.48, 1.222, -0.02, 1.278, -0.01]}, {"time": 1.3333, "x": 1.03, "curve": [1.356, 0.44, 1.378, 0, 1.356, 0, 1.378, 0]}, {"time": 1.4, "curve": [1.467, 0, 1.533, 5.13, 1.467, 0, 1.533, -0.02]}, {"time": 1.6, "x": 5.13, "y": -0.02, "curve": [1.678, 5.13, 1.756, 0, 1.678, -0.02, 1.756, 0]}, {"time": 1.8333, "curve": [1.911, 0, 1.989, 5.13, 1.911, 0, 1.989, -0.02]}, {"time": 2.0667, "x": 5.13, "y": -0.02, "curve": [2.144, 5.13, 2.222, 0, 2.144, -0.02, 2.222, 0]}, {"time": 2.3, "curve": [2.367, 0, 2.433, 5.13, 2.367, 0, 2.433, -0.02]}, {"time": 2.5, "x": 5.13, "y": -0.02, "curve": [2.556, 5.13, 2.611, 2.52, 2.556, -0.02, 2.611, -0.01]}, {"time": 2.6667, "x": 1.03}], "scale": [{"curve": [0.067, 1, 0.133, 1.044, 0.067, 1, 0.133, 0.698]}, {"time": 0.2, "x": 1.044, "y": 0.698, "curve": [0.278, 1.044, 0.356, 1, 0.278, 0.698, 0.356, 1]}, {"time": 0.4333, "curve": [0.511, 1, 0.589, 1.044, 0.511, 1, 0.589, 0.698]}, {"time": 0.6667, "x": 1.044, "y": 0.698, "curve": [0.744, 1.044, 0.822, 1, 0.744, 0.698, 0.822, 1]}, {"time": 0.9, "curve": [0.967, 1, 1.033, 1.044, 0.967, 1, 1.033, 0.698]}, {"time": 1.1, "x": 1.044, "y": 0.698, "curve": [1.178, 1.044, 1.256, 1, 1.178, 0.698, 1.256, 1]}, {"time": 1.3333, "curve": [1.4, 1, 1.467, 1.044, 1.4, 1, 1.467, 0.698]}, {"time": 1.5333, "x": 1.044, "y": 0.698, "curve": [1.611, 1.044, 1.689, 1, 1.611, 0.698, 1.689, 1]}, {"time": 1.7667, "curve": [1.844, 1, 1.922, 1.044, 1.844, 1, 1.922, 0.698]}, {"time": 2, "x": 1.044, "y": 0.698, "curve": [2.078, 1.044, 2.156, 1, 2.078, 0.698, 2.156, 1]}, {"time": 2.2333, "curve": [2.3, 1, 2.367, 1.044, 2.3, 1, 2.367, 0.698]}, {"time": 2.4333, "x": 1.044, "y": 0.698, "curve": [2.511, 1.044, 2.589, 1, 2.511, 0.698, 2.589, 1]}, {"time": 2.6667}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot": {"translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "blot_drops_control": {"translate": [{}]}, "Eye_L": {"translate": [{"x": 0.02, "y": 5.97}], "scale": [{}]}, "Eye_R": {"translate": [{"x": 0.02, "y": 5.97}], "scale": [{}]}, "Eyelid_R": {"translate": [{"y": 15.44}]}, "Eyelid_L": {"translate": [{"y": 15.44}]}, "Cntr_control": {"rotate": [{}]}, "Eyelid_L2": {"translate": [{"y": -1.5}]}, "Eyelid_R2": {"translate": [{"y": -1.5}]}}}, "t1_IDLE_old": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body": {"attachment": [{"name": "Body"}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Ear_L": {"attachment": [{"name": "Ear_L"}]}, "Ear_L_outline": {"attachment": [{"name": "Ear_L_outline"}]}, "Ear_R": {"attachment": [{"name": "Ear_R"}]}, "Ear_R_outline": {"attachment": [{"name": "Ear_R_outline"}]}, "Eye_Eyelid_L": {"attachment": [{}]}, "Eye_Eyelid_L2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_R": {"attachment": [{}]}, "Eye_Eyelid_R2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_L": {"attachment": [{"name": "Eye_L"}]}, "Eye_R": {"attachment": [{"name": "Eye_R"}]}, "Light_from_Ray": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Light_from_Ray"}]}, "Light_Glow": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Light_Glow"}]}, "Light_Ray": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Light_Ray"}]}, "Light_Ray2": {"rgba": [{"color": "ffffff00"}]}, "Lips": {"attachment": [{"name": "Lips"}]}, "Mouth": {"attachment": [{"name": "Mouth"}]}, "Pupil_L": {"attachment": [{"name": "Pupil_L"}]}, "Pupil_R": {"attachment": [{"name": "Pupil_R"}]}, "Tongue": {"attachment": [{}]}, "Tooth_01": {"attachment": [{"name": "Tooth_01"}]}, "Tooth_02": {"attachment": [{"name": "Tooth_02"}]}, "Tooth_03": {"attachment": [{"name": "Tooth_03"}]}, "Ufo_Body": {"attachment": [{"name": "Ufo_Body"}]}, "Ufo_Body_Outline": {"attachment": [{"name": "Ufo_Body_Outline"}]}}, "bones": {"Cntr": {"rotate": [{}], "translate": [{"y": -2.92, "curve": [0.222, 0, 0.444, 0, 0.222, -2.92, 0.444, 4.92]}, {"time": 0.6667, "y": 4.92, "curve": [0.889, 0, 1.111, 0, 0.889, 4.92, 1.111, -2.92]}, {"time": 1.3333, "y": -2.92}], "scale": [{"x": 1.015, "y": 1.015, "curve": [0.113, 1.007, 0.223, 1, 0.113, 1.007, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1.029, 0.556, 1, 0.778, 1.029]}, {"time": 1, "x": 1.029, "y": 1.029, "curve": [1.112, 1.029, 1.224, 1.022, 1.112, 1.029, 1.224, 1.022]}, {"time": 1.3333, "x": 1.015, "y": 1.015}]}, "Tongue_tip": {"rotate": [{"value": 6.33}], "translate": [{"x": 1.5, "y": 10.63}]}, "Pupil_R": {"translate": [{"x": 5.74, "y": 7.29}]}, "Pupil_L": {"translate": [{"x": -4.86, "y": 8.61}]}, "Body": {"rotate": [{"value": -0.4, "curve": [0.157, 1.71, 0.312, 5.23]}, {"time": 0.4667, "value": 5.23, "curve": [0.689, 5.23, 0.911, -1.95]}, {"time": 1.1333, "value": -1.95, "curve": [1.201, -1.95, 1.268, -1.32]}, {"time": 1.3333, "value": -0.4}], "translate": [{}], "scale": [{}]}, "Body2": {"rotate": [{"value": -6.14, "curve": [0.19, -2.88, 0.379, 7.72]}, {"time": 0.5667, "value": 7.72, "curve": [0.756, 7.72, 0.946, -2.97]}, {"time": 1.1333, "value": -6.14, "curve": [1.168, -6.69, 1.201, -7.12]}, {"time": 1.2333, "value": -7.12, "curve": [1.267, -7.12, 1.301, -6.73]}, {"time": 1.3333, "value": -6.14}], "translate": [{}]}, "Ear_R": {"rotate": [{"value": -15.36, "curve": [0.213, -12.5, 0.423, 14.06]}, {"time": 0.6333, "value": 14.06, "curve": [0.856, 14.06, 1.078, -15.77]}, {"time": 1.3, "value": -15.77, "curve": [1.312, -15.77, 1.324, -15.52]}, {"time": 1.3333, "value": -15.36}]}, "Ear_R2": {"rotate": [{"value": -24.65, "curve": [0.022, -25.57, 0.044, -26.08]}, {"time": 0.0667, "value": -26.08, "curve": [0.289, -26.08, 0.511, 25.09]}, {"time": 0.7333, "value": 25.09, "curve": [0.933, 25.09, 1.133, -16.36]}, {"time": 1.3333, "value": -24.65}]}, "Ear_L": {"rotate": [{"value": -15.36, "curve": [0.213, -12.5, 0.423, 14.06]}, {"time": 0.6333, "value": 14.06, "curve": [0.856, 14.06, 1.078, -15.77]}, {"time": 1.3, "value": -15.77, "curve": [1.312, -15.77, 1.324, -15.52]}, {"time": 1.3333, "value": -15.36}]}, "Ear_L2": {"rotate": [{"value": -30.17, "curve": [0.022, -31.12, 0.044, -31.64]}, {"time": 0.0667, "value": -31.64, "curve": [0.289, -31.64, 0.511, 20.89]}, {"time": 0.7333, "value": 20.89, "curve": [0.933, 20.89, 1.133, -21.66]}, {"time": 1.3333, "value": -30.17}]}, "Mouth": {"rotate": [{}], "translate": [{}], "scale": [{"curve": [0.144, 1, 0.289, 1.08, 0.144, 1, 0.289, 1.207]}, {"time": 0.4333, "x": 1.08, "y": 1.207, "curve": [0.733, 1.08, 1.033, 1, 0.733, 1.207, 1.033, 1]}, {"time": 1.3333}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot": {"translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "blot_drops_control": {"translate": [{}]}, "Eye_L": {"translate": [{}], "scale": [{}]}, "Eye_R": {"translate": [{}], "scale": [{}]}, "Eyelid_R": {"translate": [{"y": 15.21}]}, "Eyelid_L": {"translate": [{"y": 15.21}]}, "Cntr_control": {"rotate": [{}]}}}, "t1_IDLE_old2": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body": {"attachment": [{"name": "Body"}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Ear_L": {"attachment": [{"name": "Ear_L"}]}, "Ear_L_outline": {"attachment": [{"name": "Ear_L_outline"}]}, "Ear_R": {"attachment": [{"name": "Ear_R"}]}, "Ear_R_outline": {"attachment": [{"name": "Ear_R_outline"}]}, "Eye_Eyelid_L": {"attachment": [{}]}, "Eye_Eyelid_L2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_R": {"attachment": [{}]}, "Eye_Eyelid_R2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_L": {"attachment": [{"name": "Eye_L"}]}, "Eye_R": {"attachment": [{"name": "Eye_R"}]}, "Light_from_Ray": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Light_from_Ray"}]}, "Light_Glow": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Light_Glow"}]}, "Light_Ray": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Light_Ray"}]}, "Light_Ray2": {"rgba": [{"color": "ffffff00"}]}, "Lips": {"attachment": [{"name": "Lips"}]}, "Mouth": {"attachment": [{"name": "Mouth"}]}, "Pupil_L": {"attachment": [{"name": "Pupil_L"}]}, "Pupil_R": {"attachment": [{"name": "Pupil_R"}]}, "Tongue": {"attachment": [{"name": "Tongue"}]}, "Tooth_01": {"attachment": [{"name": "Tooth_01"}]}, "Tooth_02": {"attachment": [{"name": "Tooth_02"}]}, "Tooth_03": {"attachment": [{"name": "Tooth_03"}]}, "Ufo_Body": {"attachment": [{"name": "Ufo_Body"}]}, "Ufo_Body_Outline": {"attachment": [{"name": "Ufo_Body_Outline"}]}}, "bones": {"Cntr": {"rotate": [{}], "translate": [{"y": -2.92, "curve": [0.222, 0, 0.444, 0, 0.222, -2.92, 0.444, 4.92]}, {"time": 0.6667, "y": 4.92, "curve": [0.889, 0, 1.111, 0, 0.889, 4.92, 1.111, -2.92]}, {"time": 1.3333, "y": -2.92}], "scale": [{"x": 1.015, "y": 1.015, "curve": [0.113, 1.007, 0.223, 1, 0.113, 1.007, 0.223, 1]}, {"time": 0.3333, "curve": [0.556, 1, 0.778, 1.029, 0.556, 1, 0.778, 1.029]}, {"time": 1, "x": 1.029, "y": 1.029, "curve": [1.112, 1.029, 1.224, 1.022, 1.112, 1.029, 1.224, 1.022]}, {"time": 1.3333, "x": 1.015, "y": 1.015}]}, "Tongue_tip": {"rotate": [{"value": 35.39, "curve": [0.056, 19.55, 0.111, -1.51]}, {"time": 0.1667, "value": -1.51, "curve": [0.222, -1.51, 0.278, 52.59]}, {"time": 0.3333, "value": 52.59, "curve": [0.411, 52.59, 0.489, -1.51]}, {"time": 0.5667, "value": -1.51, "curve": [0.644, -1.51, 0.722, 52.59]}, {"time": 0.8, "value": 52.59, "curve": [0.878, 52.59, 0.956, -1.51]}, {"time": 1.0333, "value": -1.51, "curve": [1.1, -1.51, 1.167, 52.59]}, {"time": 1.2333, "value": 52.59, "curve": [1.267, 52.59, 1.3, 44.96]}, {"time": 1.3333, "value": 35.39}], "translate": [{"x": 6.82, "y": 1.51, "curve": [0.056, 2.5, 0.111, -3.26, 0.056, -2.54, 0.111, -7.91]}, {"time": 0.1667, "x": -3.26, "y": -7.91, "curve": [0.222, -3.26, 0.278, 11.53, 0.222, -7.91, 0.278, 5.9]}, {"time": 0.3333, "x": 11.53, "y": 5.9, "curve": [0.411, 11.53, 0.489, -3.26, 0.411, 5.9, 0.489, -7.91]}, {"time": 0.5667, "x": -3.26, "y": -7.91, "curve": [0.644, -3.26, 0.722, 11.53, 0.644, -7.91, 0.722, 5.9]}, {"time": 0.8, "x": 11.53, "y": 5.9, "curve": [0.878, 11.53, 0.956, -3.26, 0.878, 5.9, 0.956, -7.91]}, {"time": 1.0333, "x": -3.26, "y": -7.91, "curve": [1.1, -3.26, 1.167, 11.53, 1.1, -7.91, 1.167, 5.9]}, {"time": 1.2333, "x": 11.53, "y": 5.9, "curve": [1.267, 11.53, 1.3, 9.44, 1.267, 5.9, 1.3, 3.95]}, {"time": 1.3333, "x": 6.82, "y": 1.51}]}, "Pupil_R": {"translate": [{"x": -1.51, "y": -1.57}]}, "Pupil_L": {"translate": [{"x": 2.02, "y": -0.64}]}, "Body": {"rotate": [{"value": -0.2}], "translate": [{"y": 17.01, "curve": [0.075, 0, 0.125, 0, 0.075, 17.01, 0.125, 22.08]}, {"time": 0.2, "y": 22.08, "curve": "stepped"}, {"time": 0.2333, "y": 22.08, "curve": [0.308, 0, 0.325, 0, 0.308, 22.08, 0.325, 17.01]}, {"time": 0.4, "y": 17.01, "curve": "stepped"}, {"time": 0.4333, "y": 17.01, "curve": [0.508, 0, 0.559, 0, 0.508, 17.01, 0.559, 22.08]}, {"time": 0.6333, "y": 22.08, "curve": "stepped"}, {"time": 0.6667, "y": 22.08, "curve": [0.741, 0, 0.792, 0, 0.741, 22.08, 0.792, 17.01]}, {"time": 0.8667, "y": 17.01, "curve": "stepped"}, {"time": 0.9, "y": 17.01, "curve": [0.975, 0, 1.025, 0, 0.975, 17.01, 1.025, 22.08]}, {"time": 1.1, "y": 22.08, "curve": "stepped"}, {"time": 1.1333, "y": 22.08, "curve": [1.208, 0, 1.225, 0, 1.208, 22.08, 1.225, 17.01]}, {"time": 1.3, "y": 17.01, "curve": "stepped"}, {"time": 1.3333, "y": 17.01}], "scale": [{"x": 1.05, "y": 0.987, "curve": [0.044, 1.05, 0.089, 1.05, 0.044, 0.972, 0.089, 0.95]}, {"time": 0.1333, "x": 1.05, "y": 0.95, "curve": [0.211, 1.05, 0.289, 1.05, 0.211, 0.95, 0.289, 1]}, {"time": 0.3667, "x": 1.05, "curve": [0.433, 1.05, 0.5, 1.05, 0.433, 1, 0.5, 0.95]}, {"time": 0.5667, "x": 1.05, "y": 0.95, "curve": [0.644, 1.05, 0.722, 1.05, 0.644, 0.95, 0.722, 1]}, {"time": 0.8, "x": 1.05, "curve": [0.878, 1.05, 0.956, 1.05, 0.878, 1, 0.956, 0.95]}, {"time": 1.0333, "x": 1.05, "y": 0.95, "curve": [1.111, 1.05, 1.189, 1.05, 1.111, 0.95, 1.189, 1]}, {"time": 1.2667, "x": 1.05, "curve": [1.289, 1.05, 1.311, 1.05, 1.289, 1, 1.311, 0.994]}, {"time": 1.3333, "x": 1.05, "y": 0.987}]}, "Body2": {"rotate": [{}], "translate": [{"x": -5.32, "curve": "stepped"}, {"time": 0.0333, "x": -5.32, "curve": [0.108, -5.32, 0.159, -1.14, 0.108, 0, 0.159, 0]}, {"time": 0.2333, "x": -1.14, "curve": "stepped"}, {"time": 0.2667, "x": -1.14, "curve": [0.341, -1.14, 0.359, -5.32, 0.341, 0, 0.359, 0]}, {"time": 0.4333, "x": -5.32, "curve": "stepped"}, {"time": 0.4667, "x": -5.32, "curve": [0.541, -5.32, 0.592, -1.14, 0.541, 0, 0.592, 0]}, {"time": 0.6667, "x": -1.14, "curve": "stepped"}, {"time": 0.7, "x": -1.14, "curve": [0.775, -1.14, 0.825, -5.32, 0.775, 0, 0.825, 0]}, {"time": 0.9, "x": -5.32, "curve": "stepped"}, {"time": 0.9333, "x": -5.32, "curve": [1.008, -5.32, 1.059, -1.14, 1.008, 0, 1.059, 0]}, {"time": 1.1333, "x": -1.14, "curve": "stepped"}, {"time": 1.1667, "x": -1.14, "curve": [1.241, -1.14, 1.259, -5.32, 1.241, 0, 1.259, 0]}, {"time": 1.3333, "x": -5.32}]}, "Ear_R": {"rotate": [{"value": 8.06, "curve": [0.034, -1.89, 0.067, -21.52]}, {"time": 0.1, "value": -21.52, "curve": [0.167, -21.52, 0.267, 13.69]}, {"time": 0.3333, "value": 13.69, "curve": [0.405, 13.69, 0.495, -21.52]}, {"time": 0.5667, "value": -21.52, "curve": [0.638, -21.52, 0.728, 13.69]}, {"time": 0.8, "value": 13.69, "curve": [0.867, 13.69, 0.966, -21.52]}, {"time": 1.0333, "value": -21.52, "curve": [1.128, -21.52, 1.205, 13.69]}, {"time": 1.3, "value": 13.69, "curve": [1.311, 13.69, 1.323, 11.43]}, {"time": 1.3333, "value": 8.06}]}, "Ear_R2": {"rotate": [{"value": 12.06, "curve": [0.011, 12.97, 0.022, 13.69]}, {"time": 0.0333, "value": 13.69, "curve": [0.078, 13.69, 0.122, -24.24]}, {"time": 0.1667, "value": -24.24, "curve": [0.233, -24.24, 0.333, 13.69]}, {"time": 0.4, "value": 13.69, "curve": [0.472, 13.69, 0.562, -24.24]}, {"time": 0.6333, "value": -24.24, "curve": [0.705, -24.24, 0.795, 13.69]}, {"time": 0.8667, "value": 13.69, "curve": [0.934, 13.69, 1.033, -24.24]}, {"time": 1.1, "value": -24.24, "curve": [1.184, -24.24, 1.254, 5.15]}, {"time": 1.3333, "value": 12.06}]}, "Ear_L": {"rotate": [{"value": -14.3, "curve": [0.034, -1.72, 0.067, 23.11]}, {"time": 0.1, "value": 23.11, "curve": [0.167, 23.11, 0.267, -21.43]}, {"time": 0.3333, "value": -21.43, "curve": [0.405, -21.43, 0.495, 23.11]}, {"time": 0.5667, "value": 23.11, "curve": [0.638, 23.11, 0.728, -21.43]}, {"time": 0.8, "value": -21.43, "curve": [0.867, -21.43, 0.966, 23.11]}, {"time": 1.0333, "value": 23.11, "curve": [1.128, 23.11, 1.205, -21.43]}, {"time": 1.3, "value": -21.43, "curve": [1.311, -21.43, 1.323, -18.57]}, {"time": 1.3333, "value": -14.3}]}, "Ear_L2": {"rotate": [{"value": -19.43, "curve": [0.011, -20.54, 0.022, -21.43]}, {"time": 0.0333, "value": -21.43, "curve": [0.078, -21.43, 0.122, 24.94]}, {"time": 0.1667, "value": 24.94, "curve": [0.233, 24.94, 0.333, -21.43]}, {"time": 0.4, "value": -21.43, "curve": [0.472, -21.43, 0.562, 24.94]}, {"time": 0.6333, "value": 24.94, "curve": [0.705, 24.94, 0.795, -21.43]}, {"time": 0.8667, "value": -21.43, "curve": [0.934, -21.43, 1.033, 24.94]}, {"time": 1.1, "value": 24.94, "curve": [1.184, 24.94, 1.254, -10.99]}, {"time": 1.3333, "value": -19.43}]}, "Mouth": {"rotate": [{}], "translate": [{"x": 1.03, "curve": [0.023, 0.44, 0.045, 0, 0.023, 0, 0.045, 0]}, {"time": 0.0667, "curve": [0.133, 0, 0.2, 5.13, 0.133, 0, 0.2, -0.02]}, {"time": 0.2667, "x": 5.13, "y": -0.02, "curve": [0.344, 5.13, 0.422, 0, 0.344, -0.02, 0.422, 0]}, {"time": 0.5, "curve": [0.578, 0, 0.656, 5.13, 0.578, 0, 0.656, -0.02]}, {"time": 0.7333, "x": 5.13, "y": -0.02, "curve": [0.811, 5.13, 0.889, 0, 0.811, -0.02, 0.889, 0]}, {"time": 0.9667, "curve": [1.033, 0, 1.1, 5.13, 1.033, 0, 1.1, -0.02]}, {"time": 1.1667, "x": 5.13, "y": -0.02, "curve": [1.222, 5.13, 1.278, 2.52, 1.222, -0.02, 1.278, -0.01]}, {"time": 1.3333, "x": 1.03}], "scale": [{"curve": [0.067, 1, 0.133, 1.044, 0.067, 1, 0.133, 0.698]}, {"time": 0.2, "x": 1.044, "y": 0.698, "curve": [0.278, 1.044, 0.356, 1, 0.278, 0.698, 0.356, 1]}, {"time": 0.4333, "curve": [0.511, 1, 0.589, 1.044, 0.511, 1, 0.589, 0.698]}, {"time": 0.6667, "x": 1.044, "y": 0.698, "curve": [0.744, 1.044, 0.822, 1, 0.744, 0.698, 0.822, 1]}, {"time": 0.9, "curve": [0.967, 1, 1.033, 1.044, 0.967, 1, 1.033, 0.698]}, {"time": 1.1, "x": 1.044, "y": 0.698, "curve": [1.178, 1.044, 1.256, 1, 1.178, 0.698, 1.256, 1]}, {"time": 1.3333}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot": {"translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "blot_drops_control": {"translate": [{}]}, "Eye_L": {"translate": [{}], "scale": [{}]}, "Eye_R": {"translate": [{}], "scale": [{}]}, "Eyelid_R": {"translate": [{"y": 15.21}]}, "Eyelid_L": {"translate": [{"y": 15.21}]}, "Cntr_control": {"rotate": [{}]}}}, "t1_Reaction": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body": {"attachment": [{"name": "Body"}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Ear_L": {"attachment": [{"name": "Ear_L"}]}, "Ear_L_outline": {"attachment": [{"name": "Ear_L_outline"}]}, "Ear_R": {"attachment": [{"name": "Ear_R"}]}, "Ear_R_outline": {"attachment": [{"name": "Ear_R_outline"}]}, "Eye_Eyelid_L": {"attachment": [{"name": "Eyelid_L_cosed"}, {"time": 0.1}]}, "Eye_Eyelid_L2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_R": {"attachment": [{"name": "Eyelid_L_cosed"}, {"time": 0.1}]}, "Eye_Eyelid_R2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_L": {"attachment": [{"name": "Eye_L"}]}, "Eye_R": {"attachment": [{"name": "Eye_R"}]}, "Light_from_Ray": {"rgba": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Light_from_Ray"}]}, "Light_Glow": {"rgba": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Light_Glow"}]}, "Light_Ray": {"rgba": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Light_Ray"}]}, "Light_Ray2": {"rgba": [{"color": "ffffff00"}]}, "Lips": {"attachment": [{"name": "Lips"}]}, "Mouth": {"attachment": [{"name": "Mouth"}]}, "Pupil_L": {"attachment": [{"name": "Pupil_L"}]}, "Pupil_R": {"attachment": [{"name": "Pupil_R"}]}, "Tongue": {"attachment": [{}, {"time": 0.0333, "name": "Tongue"}]}, "Tooth_01": {"attachment": [{"name": "Tooth_01"}]}, "Tooth_02": {"attachment": [{"name": "Tooth_02"}]}, "Tooth_03": {"attachment": [{"name": "Tooth_03"}]}, "Ufo_Body": {"attachment": [{"name": "Ufo_Body"}]}, "Ufo_Body_Outline": {"attachment": [{"name": "Ufo_Body_Outline"}]}}, "bones": {"Cntr": {"rotate": [{}], "translate": [{"y": -2.92, "curve": [0.222, 0, 0.062, 0, 0.222, -2.92, 0.062, 32.02]}, {"time": 0.1, "y": 32.02, "curve": [0.211, 0, 0.322, 0, 0.211, 32.02, 0.322, -2.92]}, {"time": 0.4333, "y": -2.92}], "scale": [{"x": 1.015, "y": 1.015, "curve": [0.113, 1.007, 0.022, 0.9, 0.113, 1.007, 0.022, 1.1]}, {"time": 0.0333, "x": 0.9, "y": 1.1, "curve": [0.067, 0.9, 0.1, 0.978, 0.067, 1.1, 0.1, 1.012]}, {"time": 0.1333, "curve": [0.156, 1.015, 0.222, 0.95, 0.175, 0.985, 0.222, 1.05]}, {"time": 0.2333, "x": 0.95, "y": 1.05, "curve": [0.267, 0.95, 0.359, 1.029, 0.267, 1.05, 0.353, 1]}, {"time": 0.3667, "x": 1.015, "y": 1.015}]}, "Tongue_tip": {"rotate": [{"value": 9.35, "curve": [0.033, 9.35, 0.067, -32.02]}, {"time": 0.1, "value": -32.02, "curve": [0.156, -32.02, 0.211, 67.76]}, {"time": 0.2667, "value": 67.76, "curve": [0.344, 67.76, 0.422, -16.49]}, {"time": 0.5, "value": -16.49, "curve": [0.589, -16.49, 0.678, -1.51]}, {"time": 0.7667, "value": -1.51}], "translate": [{"x": 3.07, "y": 9.11, "curve": [0.033, 3.07, 0.067, -8.26, 0.033, 9.11, 0.067, -2.54]}, {"time": 0.1, "x": -8.26, "y": -2.54, "curve": [0.156, -8.26, 0.211, 20.92, 0.156, -2.54, 0.211, 9.18]}, {"time": 0.2667, "x": 20.92, "y": 9.18, "curve": [0.344, 20.92, 0.422, -2.78, 0.344, 9.18, 0.422, -7.79]}, {"time": 0.5, "x": -2.78, "y": -7.79, "curve": [0.589, -2.78, 0.678, -0.09, 0.589, -7.79, 0.678, -1.13]}, {"time": 0.7667, "x": -0.09, "y": -1.13}]}, "Pupil_R": {"translate": [{"x": -3.06, "y": 4.03}, {"time": 0.2, "x": -1.51, "y": -1.57}], "scale": [{}]}, "Pupil_L": {"translate": [{"x": 0.48, "y": 4.96}, {"time": 0.2, "x": 2.02, "y": -0.64}], "scale": [{}]}, "Body": {"rotate": [{"value": -0.2, "curve": [0.222, 0.72, 0.044, -0.2]}, {"time": 0.0667, "value": -0.2}], "translate": [{"y": 25, "curve": [0.011, 0, 0.022, 0, 0.018, 25.06, 0.022, 25.61]}, {"time": 0.0333, "y": 25.61, "curve": [0.078, 0, 0.122, 0, 0.078, 25.61, 0.122, 50.42]}, {"time": 0.1667, "y": 50.42, "curve": [0.256, 0, 0.344, 0, 0.256, 50.42, 0.344, 3.68]}, {"time": 0.4333, "y": 3.68}], "scale": [{"curve": [0.022, 0.92, 0.044, 0.76, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 0.76, "curve": [0.1, 0.76, 0.133, 1, 0.1, 1, 0.133, 1]}, {"time": 0.1667}]}, "Body2": {"rotate": [{}], "translate": [{"x": -5.32, "curve": [0.011, -5.32, 0.018, -8.31, 0.011, 0, 0.018, 0]}, {"time": 0.0333, "x": -8.31, "curve": [0.078, -8.31, 0.122, 6.62, 0.078, 0, 0.122, 0]}, {"time": 0.1667, "x": 6.62, "curve": [0.267, 6.62, 0.367, 0, 0.267, 0, 0.367, 0]}, {"time": 0.4667}]}, "Ear_R": {"rotate": [{"curve": [0.067, 0, 0.044, -30.67]}, {"time": 0.0667, "value": -30.67, "curve": [0.122, -30.67, 0.178, 12.96]}, {"time": 0.2333, "value": 12.96, "curve": "stepped"}, {"time": 0.3667, "value": 12.96, "curve": [0.433, 12.96, 0.5, 0]}, {"time": 0.5667}]}, "Ear_R2": {"rotate": [{"curve": [0.067, 0, 0.044, -32.92]}, {"time": 0.0667, "value": -32.92, "curve": [0.122, -32.92, 0.178, 13.65]}, {"time": 0.2333, "value": 13.65, "curve": "stepped"}, {"time": 0.3667, "value": 13.65, "curve": [0.433, 13.65, 0.5, 0]}, {"time": 0.5667}]}, "Ear_L": {"rotate": [{"curve": [0.067, 0, 0.044, 37.95]}, {"time": 0.0667, "value": 37.95, "curve": [0.122, 37.95, 0.178, -18.59]}, {"time": 0.2333, "value": -18.59, "curve": "stepped"}, {"time": 0.3667, "value": -18.59, "curve": [0.433, -18.59, 0.5, 0]}, {"time": 0.5667}]}, "Ear_L2": {"rotate": [{"curve": [0.067, 0, 0.044, 26.47]}, {"time": 0.0667, "value": 26.47, "curve": [0.122, 26.47, 0.178, -13.83]}, {"time": 0.2333, "value": -13.83, "curve": "stepped"}, {"time": 0.3667, "value": -13.83, "curve": [0.433, -13.83, 0.5, 0]}, {"time": 0.5667}]}, "Mouth": {"rotate": [{}], "translate": [{"x": 1.03, "curve": [0.044, 1.03, 0.089, -8.06, 0.044, 0, 0.089, 0.03]}, {"time": 0.1333, "x": -8.06, "y": 0.03, "curve": [0.211, -8.06, 0.289, 0, 0.211, 0.03, 0.289, 0]}, {"time": 0.3667}], "scale": [{"y": -0.445, "curve": [0.044, 1, 0.089, 0.715, 0.044, -0.445, 0.089, 1.618]}, {"time": 0.1333, "x": 0.715, "y": 1.618, "curve": [0.211, 0.715, 0.289, 1, 0.211, 1.618, 0.289, 1]}, {"time": 0.3667}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot": {"translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "blot_drops_control": {"translate": [{}]}, "Eye_L": {"translate": [{"x": 0.02, "y": 5.97}], "scale": [{"curve": [0.056, 1, 0.111, 1.417, 0.056, 1, 0.111, 1.417]}, {"time": 0.1667, "x": 1.417, "y": 1.417, "curve": [0.244, 1.417, 0.322, 1, 0.244, 1.417, 0.322, 1]}, {"time": 0.4}]}, "Eye_R": {"translate": [{"x": 0.02, "y": 5.97}], "scale": [{"curve": [0.056, 1, 0.111, 1.417, 0.056, 1, 0.111, 1.417]}, {"time": 0.1667, "x": 1.417, "y": 1.417, "curve": [0.244, 1.417, 0.322, 1, 0.244, 1.417, 0.322, 1]}, {"time": 0.4}]}, "Eyelid_R": {"translate": [{"y": 2.34, "curve": [0.033, 0, 0.067, 0, 0.033, 2.34, 0.067, 15.44]}, {"time": 0.1, "y": 15.44}]}, "Eyelid_L": {"translate": [{"y": 2.34, "curve": [0.033, 0, 0.067, 0, 0.033, 2.34, 0.067, 15.44]}, {"time": 0.1, "y": 15.44}]}, "Cntr_control": {"rotate": [{}]}, "Eyelid_L2": {"translate": [{"y": 7.36, "curve": [0.033, 0, 0.067, 0, 0.033, 7.36, 0.067, -1.5]}, {"time": 0.1, "y": -1.5}]}, "Eyelid_R2": {"translate": [{"y": 7.36, "curve": [0.033, 0, 0.067, 0, 0.033, 7.36, 0.067, -1.5]}, {"time": 0.1, "y": -1.5}]}}}, "t1_Reaction_old": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body": {"attachment": [{"name": "Body"}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Ear_L": {"attachment": [{"name": "Ear_L"}]}, "Ear_L_outline": {"attachment": [{"name": "Ear_L_outline"}]}, "Ear_R": {"attachment": [{"name": "Ear_R"}]}, "Ear_R_outline": {"attachment": [{"name": "Ear_R_outline"}]}, "Eye_Eyelid_L": {"attachment": [{}]}, "Eye_Eyelid_L2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_Eyelid_R": {"attachment": [{}]}, "Eye_Eyelid_R2": {"attachment": [{"name": "Eyelid_L_cosed"}]}, "Eye_L": {"attachment": [{"name": "Eye_L"}]}, "Eye_R": {"attachment": [{"name": "Eye_R"}]}, "Light_from_Ray": {"rgba": [{"color": "ffffff00", "curve": [0.033, 1, 0.067, 1, 0.033, 1, 0.067, 1, 0.033, 1, 0.067, 1, 0.033, 0, 0.067, 1]}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Light_from_Ray"}]}, "Light_Glow": {"rgba": [{"color": "ffffff00", "curve": [0.033, 1, 0.067, 1, 0.033, 1, 0.067, 1, 0.033, 1, 0.067, 1, 0.033, 0, 0.067, 1]}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Light_Glow"}]}, "Light_Ray": {"rgba": [{"color": "ffffff00", "curve": [0.033, 1, 0.067, 1, 0.033, 1, 0.067, 1, 0.033, 1, 0.067, 1, 0.033, 0, 0.067, 1]}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Light_Ray"}]}, "Light_Ray2": {"rgba": [{"color": "ffffff00"}]}, "Lips": {"attachment": [{"name": "Lips"}]}, "Mouth": {"attachment": [{"name": "Mouth"}]}, "Pupil_L": {"attachment": [{"name": "Pupil_L"}]}, "Pupil_R": {"attachment": [{"name": "Pupil_R"}]}, "Tongue": {"attachment": [{}, {"time": 0.0333, "name": "Tongue"}]}, "Tooth_01": {"attachment": [{"name": "Tooth_01"}]}, "Tooth_02": {"attachment": [{"name": "Tooth_02"}]}, "Tooth_03": {"attachment": [{"name": "Tooth_03"}]}, "Ufo_Body": {"attachment": [{"name": "Ufo_Body"}]}, "Ufo_Body_Outline": {"attachment": [{"name": "Ufo_Body_Outline"}]}}, "bones": {"Cntr": {"rotate": [{}], "translate": [{"y": -2.92, "curve": [0.038, 0, 0.062, 0, 0.038, -2.92, 0.062, 32.02]}, {"time": 0.1, "y": 32.02, "curve": [0.211, 0, 0.322, 0, 0.211, 32.02, 0.322, -2.92]}, {"time": 0.4333, "y": -2.92}], "scale": [{"x": 1.015, "y": 1.015, "curve": [0.011, 0.993, 0.022, 0.9, 0.011, 1.026, 0.022, 1.1]}, {"time": 0.0333, "x": 0.9, "y": 1.1, "curve": [0.067, 0.9, 0.1, 0.978, 0.067, 1.1, 0.1, 1.012]}, {"time": 0.1333, "curve": [0.156, 1.015, 0.222, 0.95, 0.175, 0.985, 0.222, 1.05]}, {"time": 0.2333, "x": 0.95, "y": 1.05, "curve": [0.267, 0.95, 0.359, 1.029, 0.267, 1.05, 0.353, 1]}, {"time": 0.3667, "x": 1.015, "y": 1.015}]}, "Tongue_tip": {"rotate": [{"value": 6.33, "curve": [0.033, 6.33, 0.067, -32.02]}, {"time": 0.1, "value": -32.02, "curve": [0.156, -32.02, 0.211, 67.76]}, {"time": 0.2667, "value": 67.76, "curve": [0.344, 67.76, 0.422, -16.49]}, {"time": 0.5, "value": -16.49, "curve": [0.589, -16.49, 0.678, -1.51]}, {"time": 0.7667, "value": -1.51}], "translate": [{"x": 1.5, "y": 10.63, "curve": [0.033, 1.5, 0.067, -8.26, 0.033, 10.63, 0.067, -2.54]}, {"time": 0.1, "x": -8.26, "y": -2.54, "curve": [0.156, -8.26, 0.211, 20.92, 0.156, -2.54, 0.211, 9.18]}, {"time": 0.2667, "x": 20.92, "y": 9.18, "curve": [0.344, 20.92, 0.422, -2.78, 0.344, 9.18, 0.422, -7.79]}, {"time": 0.5, "x": -2.78, "y": -7.79, "curve": [0.589, -2.78, 0.678, -0.09, 0.589, -7.79, 0.678, -1.13]}, {"time": 0.7667, "x": -0.09, "y": -1.13}]}, "Pupil_R": {"translate": [{"x": 5.74, "y": 7.29, "curve": [0.067, 5.74, 0.133, 1.8, 0.067, 7.29, 0.133, 12.96]}, {"time": 0.2, "x": 1.8, "y": 12.96, "curve": [0.267, 1.8, 0.333, -1.51, 0.267, 12.96, 0.333, -1.57]}, {"time": 0.4, "x": -1.51, "y": -1.57}]}, "Pupil_L": {"translate": [{"x": -4.86, "y": 8.61, "curve": [0.067, -4.86, 0.133, -0.27, 0.067, 8.61, 0.133, 14.97]}, {"time": 0.2, "x": -0.27, "y": 14.97, "curve": [0.267, -0.27, 0.333, 2.02, 0.267, 14.97, 0.333, -0.64]}, {"time": 0.4, "x": 2.02, "y": -0.64}]}, "Body": {"rotate": [{"value": -0.4, "curve": [0.022, -0.4, 0.044, -0.2]}, {"time": 0.0667, "value": -0.2}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.078, 0, 0.122, 0, 0.078, 0, 0.122, 34.92]}, {"time": 0.1667, "y": 34.92, "curve": [0.256, 0, 0.344, 0, 0.256, 34.92, 0.344, 17.01]}, {"time": 0.4333, "y": 17.01}], "scale": [{"curve": [0.022, 1, 0.044, 0.76, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 0.76, "curve": [0.1, 0.76, 0.133, 1, 0.1, 1, 0.133, 1]}, {"time": 0.1667}]}, "Body2": {"rotate": [{}], "translate": [{"curve": [0.016, 0, 0.018, -8.31, 0.016, 0, 0.018, 0]}, {"time": 0.0333, "x": -8.31, "curve": [0.078, -8.31, 0.122, 6.62, 0.078, 0, 0.122, 0]}, {"time": 0.1667, "x": 6.62, "curve": [0.267, 6.62, 0.367, 0, 0.267, 0, 0.367, 0]}, {"time": 0.4667}]}, "Ear_R": {"rotate": [{"curve": [0.022, 0, 0.044, -30.67]}, {"time": 0.0667, "value": -30.67, "curve": [0.122, -30.67, 0.178, 12.96]}, {"time": 0.2333, "value": 12.96, "curve": "stepped"}, {"time": 0.3667, "value": 12.96, "curve": [0.433, 12.96, 0.5, 0]}, {"time": 0.5667}]}, "Ear_R2": {"rotate": [{"curve": [0.022, 0, 0.044, -32.92]}, {"time": 0.0667, "value": -32.92, "curve": [0.122, -32.92, 0.178, 13.65]}, {"time": 0.2333, "value": 13.65, "curve": "stepped"}, {"time": 0.3667, "value": 13.65, "curve": [0.433, 13.65, 0.5, 0]}, {"time": 0.5667}]}, "Ear_L": {"rotate": [{"curve": [0.022, 0, 0.044, 37.95]}, {"time": 0.0667, "value": 37.95, "curve": [0.122, 37.95, 0.178, -18.59]}, {"time": 0.2333, "value": -18.59, "curve": "stepped"}, {"time": 0.3667, "value": -18.59, "curve": [0.433, -18.59, 0.5, 0]}, {"time": 0.5667}]}, "Ear_L2": {"rotate": [{"curve": [0.022, 0, 0.044, 26.47]}, {"time": 0.0667, "value": 26.47, "curve": [0.122, 26.47, 0.178, -13.83]}, {"time": 0.2333, "value": -13.83, "curve": "stepped"}, {"time": 0.3667, "value": -13.83, "curve": [0.433, -13.83, 0.5, 0]}, {"time": 0.5667}]}, "Mouth": {"rotate": [{}], "translate": [{"curve": [0.044, 0, 0.089, -8.06, 0.044, 0, 0.089, 0.03]}, {"time": 0.1333, "x": -8.06, "y": 0.03, "curve": [0.211, -8.06, 0.289, 0, 0.211, 0.03, 0.289, 0]}, {"time": 0.3667}], "scale": [{"curve": [0.044, 1, 0.089, 0.715, 0.044, 1, 0.089, 1.618]}, {"time": 0.1333, "x": 0.715, "y": 1.618, "curve": [0.211, 0.715, 0.289, 1, 0.211, 1.618, 0.289, 1]}, {"time": 0.3667}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot": {"translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "blot_drops_control": {"translate": [{}]}, "Eye_L": {"translate": [{}], "scale": [{}]}, "Eye_R": {"translate": [{}], "scale": [{}]}, "Eyelid_R": {"translate": [{"y": 15.21}]}, "Eyelid_L": {"translate": [{"y": 15.21}]}, "Cntr_control": {"rotate": [{}]}}}}}