{"skeleton": {"hash": "qXvLyCP/M68", "spine": "4.2.40", "x": -89.38, "y": -92.42, "width": 175.88, "height": 185.65, "images": "./Images/Monster_Simpy/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "cntr", "parent": "root", "color": "0088ffff", "icon": "arrowsB"}, {"name": "Face", "parent": "cntr", "x": 2.58, "y": -7.98, "color": "ffffffff", "icon": "drop"}, {"name": "eye_R", "parent": "Face", "x": -50, "y": 6.01, "icon": "eye"}, {"name": "pupil_R", "parent": "eye_R", "length": 25, "x": 5.43, "y": -0.88}, {"name": "EyeLiid_R_U", "parent": "eye_R", "rotation": 7.61, "x": 2.3, "y": -1.3, "icon": "arrowUp"}, {"name": "Eyelid_R_B", "parent": "eye_R", "rotation": 4.39, "x": 2.18, "y": -8.07, "icon": "arrowDown"}, {"name": "eye_L", "parent": "Face", "rotation": -7.76, "x": 48.37, "y": 4.6, "icon": "eye"}, {"name": "pupil_L", "parent": "eye_L", "length": 25, "rotation": 7.76, "x": -3.87, "y": -1.19}, {"name": "Eyelid_L_U", "parent": "eye_L", "rotation": 4.78, "x": 1.54, "y": -0.76, "icon": "arrowUp"}, {"name": "Eyelid_L_B", "parent": "eye_L", "rotation": 1.52, "x": 2.23, "y": -6.21, "icon": "arrowDown"}, {"name": "eyebrows_r", "parent": "Face", "x": 45, "y": 42.34}, {"name": "eyebrows_l", "parent": "Face", "x": -52.32, "y": 41.84}, {"name": "body_bottom", "parent": "cntr", "x": 2.67, "y": -73.82, "color": "0088ffff", "icon": "arrowUp"}, {"name": "body_top", "parent": "cntr", "x": -7.16, "y": 73.94, "color": "0088ffff", "icon": "arrowDown"}, {"name": "mouth_base", "parent": "Face", "x": -2.49, "y": -25.96, "color": "ffffffff", "icon": "mouth"}, {"name": "Body_R", "parent": "cntr", "x": 71.67, "y": -0.42, "color": "0088ffff", "icon": "arrowRight"}, {"name": "Body_L", "parent": "cntr", "x": -78.38, "y": -0.18, "color": "0088ffff", "icon": "arrowLeft"}, {"name": "Teeth_R", "parent": "mouth_base", "rotation": -6.09, "x": -13.56, "y": -15.78, "color": "ffffffff", "icon": "romanIII"}, {"name": "Teeth_L", "parent": "mouth_base", "rotation": 4.77, "x": 25.46, "y": -13.19, "color": "ffffffff", "icon": "romanIII"}, {"name": "mouth_2", "parent": "Face", "x": -0.09, "y": -26.69, "color": "ffffffff", "icon": "circle"}, {"name": "Eyes_scale", "parent": "root", "x": 116.39, "y": 34.63, "color": "abe323ff", "icon": "eye"}, {"name": "Mouth_low", "parent": "mouth_base", "x": 3.29, "y": -14.3, "color": "ffffffff", "icon": "arrowDown"}, {"name": "blot", "parent": "root", "rotation": -0.04, "icon": "flower"}, {"name": "blot_drops_control", "parent": "blot", "y": -10.68, "icon": "arrowDown"}, {"name": "Drops", "parent": "root", "scaleX": -1}, {"name": "blot_drop2", "parent": "Drops"}, {"name": "blot_drop_s1", "parent": "Drops"}, {"name": "blot_drop3", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop4", "parent": "Drops", "scaleX": 1.2553, "scaleY": 1.2553}, {"name": "blot_drop_s2", "parent": "Drops"}, {"name": "blot_drop5", "parent": "Drops"}, {"name": "blot_drop_s3", "parent": "Drops"}, {"name": "blot_drop6", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop_s4", "parent": "Drops"}], "slots": [{"name": "body_outline", "bone": "cntr", "attachment": "Simpy_body_outline"}, {"name": "body", "bone": "cntr", "attachment": "Simpy_body"}, {"name": "eye_L", "bone": "eye_L", "attachment": "Simpy_eye_r"}, {"name": "pupil_L", "bone": "pupil_L", "attachment": "Simpy_pupil_r"}, {"name": "eyelid_l_r", "bone": "eye_L", "attachment": "Simpy_eyelid_l_r"}, {"name": "eyelid_u_r", "bone": "eye_L", "attachment": "Simpy_eyelid_u_r"}, {"name": "eye_R", "bone": "eye_R", "attachment": "Simpy_eye_l"}, {"name": "pupil_R", "bone": "pupil_R", "attachment": "Simpy_pupil_l"}, {"name": "eyelid_l_R", "bone": "eye_R", "attachment": "Simpy_eyelid_l_l"}, {"name": "eyelid_u_R", "bone": "eye_R", "attachment": "Simpy_eyelid_u_l"}, {"name": "eyebrows_r", "bone": "eyebrows_r", "attachment": "Simpy_eyebrows_r"}, {"name": "eyebrows_l", "bone": "eyebrows_l", "attachment": "Simpy_eyebrows_l"}, {"name": "mouth_base", "bone": "mouth_base", "attachment": "Simpy_mouth_base"}, {"name": "mouth_1", "bone": "mouth_base", "attachment": "Simpy_mouth_1"}, {"name": "mouth_2", "bone": "mouth_2"}, {"name": "teeth", "bone": "mouth_base", "attachment": "Simpy_teeth"}, {"name": "blot", "bone": "blot"}, {"name": "blot_drop2", "bone": "blot_drop2"}, {"name": "blot_drop_s1", "bone": "blot_drop_s1"}, {"name": "blot_drop3", "bone": "blot_drop3"}, {"name": "blot_drop4", "bone": "blot_drop4"}, {"name": "blot_drop5", "bone": "blot_drop_s2"}, {"name": "blot_drop6", "bone": "blot_drop5"}, {"name": "blot_drop_s2", "bone": "blot_drop_s3"}, {"name": "blot_drop7", "bone": "blot_drop6"}, {"name": "blot_drop8", "bone": "blot_drop_s4"}], "transform": [{"name": "Eyes_scale", "bones": ["eye_L", "eye_R"], "target": "Eyes_scale", "relative": true, "mixRotate": 0, "mixX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"blot": {"blot": {"type": "mesh", "color": "61e82fff", "uvs": [0.16853, 0.04132, 0.20427, 0.04133, 0.235, 0.05776, 0.2567, 0.08503, 0.27019, 0.11893, 0.28083, 0.15284, 0.29008, 0.16859, 0.40306, 0.14175, 0.52511, 0.14243, 0.63958, 0.17341, 0.65099, 0.15069, 0.66785, 0.11583, 0.69938, 0.10078, 0.74656, 0.0982, 0.78663, 0.11447, 0.81478, 0.14412, 0.82679, 0.17844, 0.8274, 0.22268, 0.80551, 0.24979, 0.78146, 0.26764, 0.77045, 0.28539, 0.79007, 0.31899, 0.82203, 0.31695, 0.86186, 0.3347, 0.88752, 0.35974, 0.90407, 0.39632, 0.9038, 0.43154, 0.89703, 0.47361, 0.87282, 0.49811, 0.84367, 0.51769, 0.83976, 0.57695, 0.82721, 0.62819, 0.85091, 0.63399, 0.88661, 0.64029, 0.9085, 0.64943, 0.92589, 0.66799, 0.92962, 0.68768, 0.92352, 0.71185, 0.90345, 0.72913, 0.88078, 0.7345, 0.85869, 0.73204, 0.84233, 0.71683, 0.82827, 0.69353, 0.80387, 0.68283, 0.78923, 0.70054, 0.77272, 0.72456, 0.75456, 0.74543, 0.74252, 0.76337, 0.75174, 0.78241, 0.7707, 0.79541, 0.78966, 0.80098, 0.80915, 0.81654, 0.82604, 0.83018, 0.83576, 0.85319, 0.83587, 0.87721, 0.83318, 0.90725, 0.81778, 0.92725, 0.79106, 0.94395, 0.74837, 0.94704, 0.71572, 0.94051, 0.69219, 0.91664, 0.67776, 0.88946, 0.6729, 0.86403, 0.66254, 0.84197, 0.64012, 0.83666, 0.62051, 0.84647, 0.61585, 0.88202, 0.60876, 0.90761, 0.58596, 0.93801, 0.55205, 0.95614, 0.51472, 0.9587, 0.47446, 0.95391, 0.44301, 0.93005, 0.42458, 0.90092, 0.4098, 0.87756, 0.3531, 0.86126, 0.31071, 0.8462, 0.27287, 0.82849, 0.23944, 0.82863, 0.21336, 0.82462, 0.19205, 0.80713, 0.1763, 0.78103, 0.17544, 0.7546, 0.15225, 0.73275, 0.12704, 0.75153, 0.09896, 0.78407, 0.07158, 0.81002, 0.02474, 0.81038, 0.00898, 0.79541, 0.00014, 0.76864, 0.0126, 0.74387, 0.02727, 0.723, 0.06373, 0.71112, 0.1024, 0.70314, 0.11577, 0.6871, 0.11344, 0.66789, 0.10013, 0.64319, 0.07288, 0.63049, 0.0481, 0.60493, 0.0395, 0.57932, 0.04017, 0.53824, 0.05265, 0.51498, 0.04012, 0.49584, 0.02776, 0.45571, 0.02776, 0.39855, 0.042, 0.36049, 0.07071, 0.32161, 0.10651, 0.29848, 0.14993, 0.28593, 0.17204, 0.25511, 0.157, 0.23649, 0.12839, 0.21904, 0.09915, 0.19856, 0.07437, 0.1592, 0.07434, 0.10411, 0.09293, 0.06764, 0.12711, 0.04525, 0.7637, 0.87399, 0.51839, 0.89337, 0.06159, 0.75434, 0.88018, 0.68481, 0.82945, 0.40232, 0.45472, 0.47982, 0.74434, 0.1825], "triangles": [123, 12, 13, 0, 112, 113, 111, 0, 4, 3, 4, 0, 119, 89, 90, 119, 88, 89, 85, 119, 84, 116, 113, 114, 123, 13, 14, 117, 53, 54, 55, 117, 54, 40, 41, 120, 56, 117, 55, 59, 60, 117, 56, 57, 117, 57, 58, 117, 69, 70, 118, 86, 87, 119, 86, 119, 85, 70, 71, 118, 71, 72, 118, 69, 118, 68, 117, 58, 59, 120, 39, 40, 38, 39, 120, 38, 120, 37, 37, 120, 36, 120, 35, 36, 120, 34, 35, 117, 51, 52, 117, 52, 53, 117, 50, 51, 2, 0, 1, 3, 0, 2, 115, 116, 114, 116, 0, 113, 87, 88, 119, 97, 98, 99, 96, 97, 99, 122, 79, 80, 79, 122, 78, 77, 78, 122, 122, 96, 101, 111, 112, 0, 123, 14, 15, 11, 12, 123, 93, 94, 83, 84, 93, 83, 119, 91, 92, 90, 91, 119, 119, 93, 84, 93, 119, 92, 63, 64, 47, 48, 63, 47, 62, 63, 48, 117, 48, 49, 117, 49, 50, 62, 48, 117, 65, 118, 74, 61, 62, 117, 66, 118, 65, 73, 74, 118, 67, 118, 66, 60, 61, 117, 72, 73, 118, 68, 118, 67, 42, 31, 32, 122, 83, 95, 46, 122, 45, 82, 83, 122, 47, 122, 46, 81, 82, 122, 122, 80, 81, 77, 122, 76, 47, 64, 122, 76, 122, 75, 64, 65, 122, 122, 74, 75, 65, 74, 122, 101, 99, 100, 95, 96, 122, 31, 44, 122, 123, 15, 16, 123, 16, 17, 4, 110, 111, 110, 4, 5, 110, 5, 6, 18, 123, 17, 109, 110, 6, 19, 123, 18, 20, 123, 19, 121, 22, 23, 121, 23, 24, 121, 24, 25, 26, 121, 25, 27, 121, 26, 122, 7, 8, 122, 8, 9, 6, 7, 122, 109, 6, 122, 108, 109, 122, 10, 11, 123, 9, 10, 123, 20, 122, 9, 20, 9, 123, 105, 103, 104, 101, 102, 103, 28, 121, 27, 105, 107, 103, 101, 103, 108, 106, 107, 105, 108, 103, 107, 101, 108, 122, 29, 121, 28, 121, 122, 21, 21, 22, 121, 122, 121, 29, 122, 20, 21, 29, 30, 122, 31, 122, 30, 101, 96, 99, 45, 122, 44, 120, 32, 33, 120, 33, 34, 83, 94, 95, 42, 32, 120, 43, 31, 42, 43, 44, 31, 41, 42, 120], "vertices": [2, 23, -90.99, 159.47, 0.02155, 24, -90.99, 170.14, 0.97845, 2, 23, -79.49, 159.46, 0.00946, 24, -79.49, 170.13, 0.99054, 2, 23, -69.52, 154, 0.153, 24, -69.52, 164.68, 0.847, 2, 23, -62.4, 144.94, 0.39795, 24, -62.4, 155.62, 0.60205, 2, 23, -57.89, 133.68, 0.70584, 24, -57.89, 144.36, 0.29416, 2, 23, -54.34, 122.38, 0.93146, 24, -54.34, 133.05, 0.06854, 2, 23, -51.35, 117.08, 0.95362, 24, -51.35, 127.76, 0.04638, 2, 23, -15.08, 126.02, 0.7559, 24, -15.08, 136.7, 0.2441, 2, 23, 24.22, 125.79, 0.76179, 24, 24.22, 136.47, 0.23821, 2, 23, 61.07, 115.34, 0.74063, 24, 61.07, 126.02, 0.25937, 2, 23, 64.7, 122.95, 0.64513, 24, 64.7, 133.62, 0.35487, 2, 23, 69.97, 134.55, 0.36729, 24, 69.97, 145.22, 0.63271, 2, 23, 80.03, 139.53, 0.19886, 24, 80.03, 150.2, 0.80114, 2, 23, 95.17, 140.34, 0.10171, 24, 95.17, 151.02, 0.89829, 2, 23, 108.13, 134.91, 0.19647, 24, 108.13, 145.59, 0.80353, 2, 23, 117.28, 125.01, 0.36288, 24, 117.28, 135.69, 0.63712, 2, 23, 121.2, 113.5, 0.46208, 24, 121.2, 124.17, 0.53792, 2, 23, 121.47, 98.66, 0.59174, 24, 121.47, 109.33, 0.40826, 2, 23, 114.5, 89.6, 0.74078, 24, 114.5, 100.28, 0.25922, 2, 23, 106.82, 83.66, 0.86799, 24, 106.82, 94.33, 0.13201, 1, 23, 103.35, 77.75, 1, 1, 23, 109.67, 66.42, 1, 2, 23, 119.95, 67.1, 0.98349, 24, 119.95, 77.78, 0.01651, 2, 23, 132.69, 61.03, 0.81787, 24, 132.69, 71.71, 0.18213, 2, 23, 140.89, 52.53, 0.70315, 24, 140.89, 63.21, 0.29685, 2, 23, 146.18, 40.16, 0.62876, 24, 146.18, 50.84, 0.37124, 2, 23, 146.08, 28.28, 0.60882, 24, 146.08, 38.96, 0.39118, 2, 23, 143.91, 14.12, 0.63067, 24, 143.91, 24.79, 0.36933, 2, 23, 136.18, 5.92, 0.74414, 24, 136.18, 16.6, 0.25586, 2, 23, 126.87, -0.6, 0.88743, 24, 126.87, 10.07, 0.11257, 1, 23, 125.67, -20.51, 1, 2, 23, 121.59, -37.82, 0.92415, 24, 121.59, -27.14, 0.07585, 2, 23, 129.18, -39.81, 0.85626, 24, 129.18, -29.14, 0.14374, 2, 23, 140.57, -42.04, 0.65141, 24, 140.57, -31.37, 0.34859, 2, 23, 147.51, -45.23, 0.45337, 24, 147.51, -34.55, 0.54663, 2, 23, 152.98, -51.61, 0.22414, 24, 152.98, -40.93, 0.77586, 2, 23, 154.14, -58.3, 0.12863, 24, 154.14, -47.62, 0.87137, 2, 23, 152.13, -66.48, 0.06435, 24, 152.13, -55.8, 0.93565, 2, 23, 145.68, -72.29, 0.0759, 24, 145.68, -61.62, 0.9241, 2, 23, 138.4, -74.08, 0.11319, 24, 138.4, -63.41, 0.88681, 2, 23, 131.35, -73.19, 0.22246, 24, 131.35, -62.52, 0.77754, 2, 23, 126.18, -67.96, 0.41853, 24, 126.18, -57.29, 0.58147, 2, 23, 121.79, -59.98, 0.66246, 24, 121.79, -49.3, 0.33754, 2, 23, 114.11, -56.19, 0.99426, 24, 114.11, -45.52, 0.00574, 2, 23, 109.4, -62.16, 0.99314, 24, 109.4, -51.49, 0.00686, 2, 23, 104.08, -70.26, 0.99213, 24, 104.08, -59.58, 0.00787, 2, 23, 98.23, -77.29, 0.99754, 24, 98.23, -66.61, 0.00246, 2, 23, 94.15, -83.54, 0.60983, 24, 94.15, -72.87, 0.39017, 2, 23, 97.15, -89.92, 0.67939, 24, 97.15, -79.25, 0.32061, 2, 23, 103.21, -94.36, 0.58167, 24, 103.21, -83.68, 0.41833, 2, 23, 109.23, -96.31, 0.43988, 24, 109.23, -85.64, 0.56012, 2, 23, 115.45, -101.62, 0.3264, 24, 115.45, -90.94, 0.6736, 2, 23, 120.82, -106.28, 0.20757, 24, 120.82, -95.6, 0.79243, 2, 23, 123.96, -114.02, 0.2256, 24, 123.96, -103.35, 0.7744, 2, 23, 124.06, -122.06, 0.33257, 24, 124.06, -111.38, 0.66743, 2, 23, 123.16, -132.21, 0.27292, 24, 123.16, -121.54, 0.72708, 2, 23, 118.12, -139.03, 0.13068, 24, 118.12, -128.35, 0.86932, 1, 24, 109.45, -134.05, 1, 1, 24, 95.7, -135.09, 1, 2, 23, 85.24, -143.52, 0.09602, 24, 85.24, -132.84, 0.90398, 2, 23, 77.82, -135.31, 0.39368, 24, 77.82, -124.64, 0.60632, 2, 23, 73.34, -125.99, 0.70124, 24, 73.34, -115.31, 0.29876, 2, 23, 71.87, -117.33, 0.86966, 24, 71.87, -106.65, 0.13034, 2, 23, 68.58, -109.85, 0.95079, 24, 68.58, -99.17, 0.04921, 2, 23, 61.37, -108.04, 0.97473, 24, 61.37, -97.37, 0.02527, 2, 23, 55.04, -111.36, 0.95012, 24, 55.04, -100.69, 0.04988, 2, 23, 53.48, -123.41, 0.83978, 24, 53.48, -112.73, 0.16022, 2, 23, 51.15, -132.08, 0.7483, 24, 51.15, -121.4, 0.2517, 2, 23, 43.68, -142.45, 0.50798, 24, 43.68, -131.78, 0.49202, 2, 23, 32.6, -148.73, 0.20256, 24, 32.6, -138.05, 0.79744, 2, 23, 20.47, -149.7, 0.00552, 24, 20.47, -139.02, 0.99448, 1, 24, 7.5, -137.41, 1, 2, 23, -2.46, -139.88, 0.29991, 24, -2.46, -129.21, 0.70009, 2, 23, -8.22, -129.89, 0.61942, 24, -8.22, -119.22, 0.38058, 2, 23, -12.91, -121.95, 0.74609, 24, -12.91, -111.28, 0.25391, 2, 23, -31.13, -116.42, 0.81636, 24, -31.13, -105.75, 0.18364, 2, 23, -44.88, -111.44, 0.63451, 24, -44.88, -100.77, 0.36549, 2, 23, -57.09, -105.5, 0.59263, 24, -57.09, -94.82, 0.40737, 2, 23, -67.91, -105.6, 0.49938, 24, -67.91, -94.92, 0.50062, 2, 23, -76.33, -104.27, 0.44983, 24, -76.33, -93.6, 0.55017, 2, 23, -83.13, -98.32, 0.5575, 24, -83.13, -87.64, 0.4425, 2, 23, -88.06, -89.39, 0.81245, 24, -88.06, -78.71, 0.18755, 2, 23, -88.25, -80.39, 0.9861, 24, -88.25, -69.71, 0.0139, 2, 23, -95.71, -73.02, 0.99035, 24, -95.71, -62.35, 0.00965, 2, 23, -104.08, -79.6, 0.52724, 24, -104.08, -68.92, 0.47276, 1, 24, -113.41, -80.17, 1, 1, 24, -122.22, -88.92, 1, 1, 24, -137.31, -89.04, 1, 2, 23, -142.34, -94.62, 0.08531, 24, -142.34, -83.95, 0.91469, 2, 23, -145.05, -85.47, 0.33535, 24, -145.05, -74.79, 0.66465, 2, 23, -140.97, -77.05, 0.46148, 24, -140.97, -66.38, 0.53852, 2, 23, -136.19, -69.97, 0.55661, 24, -136.19, -59.29, 0.44339, 2, 23, -124.38, -65.89, 0.68332, 24, -124.38, -55.22, 0.31668, 2, 23, -111.88, -63.16, 0.77501, 24, -111.88, -52.48, 0.22499, 2, 23, -107.54, -57.72, 0.83963, 24, -107.54, -47.04, 0.16037, 1, 23, -108.21, -51.16, 1, 2, 23, -112.82, -43.16, 0.39981, 24, -112.82, -32.48, 0.60019, 2, 23, -121.62, -38.91, 0.33627, 24, -121.62, -28.24, 0.66373, 2, 23, -129.52, -30.21, 0.50107, 24, -129.52, -19.53, 0.49893, 2, 23, -132.22, -21.51, 0.61634, 24, -132.22, -10.84, 0.38366, 2, 23, -131.98, -7.65, 0.65964, 24, -131.98, 3.03, 0.34036, 2, 23, -127.97, 0.19, 0.65438, 24, -127.97, 10.86, 0.34562, 2, 23, -131.99, 6.65, 0.6746, 24, -131.99, 17.33, 0.3254, 2, 23, -135.85, 20.3, 0.90838, 24, -135.85, 30.97, 0.09162, 2, 23, -135.9, 39.51, 0.80863, 24, -135.9, 50.18, 0.19137, 2, 23, -131.36, 52.28, 0.71293, 24, -131.36, 62.96, 0.28707, 2, 23, -122.13, 65.37, 0.68699, 24, -122.13, 76.05, 0.31301, 2, 23, -110.55, 73.22, 0.78083, 24, -110.55, 83.89, 0.21917, 2, 23, -96.49, 77.53, 0.94418, 24, -96.49, 88.21, 0.05582, 2, 23, -89.51, 87.77, 0.67426, 24, -89.51, 98.45, 0.32574, 2, 23, -94.35, 94.05, 0.67915, 24, -94.35, 104.73, 0.32085, 2, 23, -103.64, 99.85, 0.53234, 24, -103.64, 110.53, 0.46766, 2, 23, -113.05, 106.77, 0.55156, 24, -113.05, 117.44, 0.44844, 2, 23, -121.17, 119.89, 0.28889, 24, -121.17, 130.56, 0.71111, 2, 23, -121.32, 138.32, 0.03582, 24, -121.32, 148.99, 0.96418, 1, 24, -115.35, 161.26, 1, 1, 24, -104.34, 168.81, 1, 2, 23, 101.02, -120.77, 0.71328, 24, 101.02, -110.09, 0.28672, 2, 23, 22.04, -127.29, 0.72168, 24, 22.04, -116.62, 0.27832, 2, 23, -125.18, -80.57, 0.48636, 24, -125.18, -69.89, 0.51364, 2, 23, 138.41, -57.14, 0.48188, 24, 138.41, -46.46, 0.51812, 2, 23, 122.3, 38.29, 0.90859, 24, 122.3, 48.97, 0.09141, 2, 23, 1.32, 11.85, 0.31422, 24, 1.32, 22.53, 0.68578, 2, 23, 94.84, 112.32, 0.81578, 24, 94.84, 123, 0.18422], "hull": 117, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 34, 36, 58, 60, 60, 62, 72, 74, 152, 154, 172, 174, 174, 176, 176, 178, 210, 212, 218, 220, 224, 226, 226, 228, 228, 230, 230, 232, 166, 168, 168, 170, 170, 172, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 200, 202, 202, 204, 196, 198, 198, 200, 158, 160, 160, 162, 154, 156, 156, 158, 162, 164, 164, 166, 146, 148, 142, 144, 144, 146, 140, 142, 134, 136, 132, 134, 136, 138, 138, 140, 128, 130, 130, 132, 126, 128, 122, 124, 124, 126, 118, 120, 120, 122, 114, 116, 116, 118, 110, 112, 112, 114, 108, 110, 178, 180, 180, 182, 102, 104, 100, 102, 94, 96, 104, 106, 106, 108, 96, 98, 98, 100, 90, 92, 92, 94, 86, 88, 88, 90, 82, 84, 84, 86, 74, 76, 76, 78, 78, 80, 80, 82, 68, 70, 70, 72, 62, 64, 64, 66, 66, 68, 54, 56, 56, 58, 50, 52, 52, 54, 46, 48, 48, 50, 44, 46, 244, 60, 40, 42, 42, 44, 36, 38, 38, 40, 30, 32, 32, 34, 26, 28, 28, 30, 18, 20, 20, 22, 40, 244, 148, 150, 150, 152, 156, 244, 160, 244, 162, 244, 158, 244, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 232, 220, 222, 222, 224, 216, 218, 212, 214, 214, 216, 208, 210, 206, 208, 204, 206, 244, 202], "width": 322, "height": 337}}, "blot_drop2": {"blot_drop2": {"color": "61e82fff", "width": 63, "height": 52}}, "blot_drop3": {"blot_drop2": {"color": "61e82fff", "width": 63, "height": 52}}, "blot_drop4": {"blot_drop2": {"color": "61e82fff", "width": 63, "height": 52}}, "blot_drop5": {"blot_drop1": {"color": "61e82fff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop6": {"blot_drop2": {"color": "61e82fff", "width": 63, "height": 52}}, "blot_drop7": {"blot_drop2": {"color": "61e82fff", "width": 63, "height": 52}}, "blot_drop8": {"blot_drop1": {"color": "61e82fff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s1": {"blot_drop1": {"color": "61e82fff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s2": {"blot_drop1": {"color": "61e82fff", "rotation": -0.04, "width": 30, "height": 29}}, "body": {"Simpy_body": {"type": "mesh", "uvs": [0.35944, 0.00514, 0.4387, 0.05946, 0.53738, 0.05665, 0.64892, 0.08092, 0.73353, 0.11624, 0.84354, 0.11499, 0.86412, 0.13652, 0.86345, 0.24292, 0.90806, 0.31175, 0.93697, 0.40036, 0.94316, 0.51236, 1, 0.60041, 0.98709, 0.62994, 0.90545, 0.68423, 0.85693, 0.77376, 0.7711, 0.85917, 0.70538, 0.90453, 0.67889, 0.96887, 0.65706, 0.99474, 0.62638, 1, 0.53307, 0.95262, 0.39042, 0.95326, 0.26442, 0.91298, 0.1683, 0.84213, 0.09709, 0.84257, 0.06292, 0.83106, 0.04861, 0.78409, 0.06341, 0.70656, 0.02292, 0.63432, 0.00995, 0.56175, 0.00838, 0.46439, 0.02549, 0.37312, 0.00592, 0.31178, 0.00505, 0.25788, 0.01995, 0.23814, 0.11705, 0.21519, 0.17916, 0.1543, 0.25176, 0.10638, 0.31364, 0.00535, 0.49538, 0.4969], "triangles": [39, 27, 29, 29, 30, 39, 30, 31, 39, 35, 39, 34, 39, 33, 34, 39, 31, 33, 33, 31, 32, 39, 22, 23, 23, 24, 39, 39, 24, 25, 25, 26, 39, 26, 27, 39, 27, 28, 29, 10, 39, 9, 8, 9, 39, 39, 7, 8, 7, 39, 6, 39, 4, 5, 6, 39, 5, 14, 39, 13, 13, 39, 12, 12, 39, 11, 39, 10, 11, 39, 1, 2, 35, 36, 39, 39, 3, 4, 39, 2, 3, 36, 37, 39, 39, 0, 1, 38, 0, 39, 39, 37, 38, 18, 19, 39, 17, 18, 39, 16, 17, 39, 20, 21, 39, 19, 20, 39, 22, 39, 21, 15, 39, 14, 15, 16, 39], "vertices": [2, 14, -3.05, 11.92, 0.99839, 17, 68.18, 86.04, 0.00161, 4, 13, -1.1, 148.54, 0.00054, 14, 8.72, 0.78, 0.95396, 16, -70.1, 75.13, 0.04542, 17, 79.94, 74.9, 9e-05, 3, 13, 15.44, 146.35, 0.00111, 14, 25.26, -1.41, 0.79527, 16, -53.56, 72.94, 0.20362, 3, 13, 33.4, 139.32, 0.00132, 14, 43.22, -8.44, 0.60559, 16, -35.6, 65.91, 0.39309, 4, 13, 46.57, 131.18, 0.00082, 14, 56.39, -16.58, 0.44103, 16, -22.43, 57.78, 0.558, 17, 127.61, 57.54, 0.00014, 2, 14, 74.78, -19.33, 0.33106, 16, -4.04, 55.02, 0.66894, 2, 14, 77.64, -23.46, 0.32398, 16, -1.19, 50.89, 0.67602, 4, 13, 64.85, 106.67, 0.00028, 14, 74.68, -41.09, 0.23286, 16, -4.15, 33.27, 0.76637, 17, 145.9, 33.03, 0.00049, 3, 14, 80.28, -53.7, 0.10982, 16, 1.45, 20.65, 0.89005, 17, 151.5, 20.42, 0.00013, 2, 14, 82.73, -69.18, 0.01457, 16, 3.91, 5.17, 0.98543, 3, 13, 70.94, 59.84, 0.07234, 16, 1.94, -13.57, 0.92741, 17, 151.99, -13.8, 0.00025, 2, 13, 78.06, 43.7, 0.13962, 16, 9.07, -29.7, 0.86038, 2, 13, 75.12, 39.15, 0.14935, 16, 6.12, -34.25, 0.85065, 4, 13, 60.04, 32.35, 0.27668, 14, 69.87, -115.41, 0.00036, 16, -8.95, -41.06, 0.72256, 17, 141.09, -41.29, 0.00039, 4, 13, 49.55, 18.8, 0.48374, 14, 59.38, -128.95, 4e-05, 16, -19.44, -54.6, 0.51617, 17, 130.6, -54.83, 5e-05, 3, 13, 32.95, 6.95, 0.71503, 14, 42.77, -140.81, 0.00029, 16, -36.05, -66.45, 0.28467, 4, 13, 20.77, 1.2, 0.87954, 14, 30.59, -146.56, 0.00041, 16, -48.23, -72.21, 0.11993, 17, 101.81, -72.44, 0.00012, 2, 13, 14.62, -8.76, 0.97751, 16, -54.37, -82.17, 0.02249, 3, 13, 10.29, -12.46, 0.99241, 16, -58.71, -85.87, 0.00759, 17, 91.34, -86.1, 0, 3, 13, 5.03, -12.51, 0.99793, 16, -63.97, -85.91, 0.00207, 17, 86.08, -86.15, 0, 4, 13, -9.27, -2.14, 0.95546, 14, 0.56, -149.9, 0.00034, 16, -78.27, -75.54, 8e-05, 17, 71.78, -75.77, 0.04411, 3, 13, -33.09, 1.6, 0.73817, 14, -23.26, -146.16, 3e-05, 17, 47.96, -72.04, 0.2618, 3, 13, -53.03, 11.67, 0.53341, 14, -43.2, -136.09, 5e-05, 17, 28.02, -61.97, 0.46654, 4, 13, -67.17, 26.01, 0.31756, 14, -57.34, -121.75, 0.00071, 16, -136.17, -47.39, 0.0006, 17, 13.88, -47.63, 0.68112, 2, 13, -79.06, 27.86, 0.22961, 17, 1.99, -45.78, 0.77039, 2, 13, -84.45, 30.68, 0.214, 17, -3.41, -42.95, 0.786, 2, 13, -85.59, 38.86, 0.1967, 17, -4.54, -34.78, 0.8033, 4, 13, -81.04, 51.32, 0.13065, 14, -71.21, -96.44, 0.00013, 16, -150.04, -22.08, 0.00058, 17, 0.01, -22.32, 0.86864, 3, 13, -85.86, 64.39, 0.02945, 16, -154.86, -9.01, 6e-05, 17, -4.81, -9.25, 0.9705, 2, 14, -76.26, -70.98, 0.00768, 17, -5.03, 3.14, 0.99232, 2, 14, -73.91, -54.79, 0.11004, 17, -2.69, 19.33, 0.88996, 4, 13, -78.44, 107.64, 0.00018, 14, -68.61, -40.11, 0.25861, 16, -147.44, 34.24, 0.00032, 17, 2.61, 34.01, 0.7409, 2, 14, -70.24, -29.41, 0.33469, 17, 0.99, 44.71, 0.66531, 2, 14, -68.94, -20.45, 0.36415, 17, 2.28, 53.67, 0.63585, 2, 14, -65.93, -17.58, 0.37211, 17, 5.3, 56.54, 0.62789, 4, 13, -58.94, 131.37, 0.00135, 14, -49.11, -16.39, 0.49114, 16, -127.93, 57.97, 0.00012, 17, 22.11, 57.73, 0.50739, 3, 13, -46.94, 139.8, 0.00198, 14, -37.12, -7.96, 0.67114, 17, 34.1, 66.16, 0.32688, 4, 13, -33.55, 145.79, 0.00121, 14, -23.72, -1.97, 0.85115, 16, -102.55, 72.39, 4e-05, 17, 47.5, 72.15, 0.1476, 2, 14, -10.69, 13.12, 0.9918, 17, 60.53, 87.24, 0.0082, 4, 13, -3.36, 74.46, 0.23982, 14, 6.47, -73.3, 0.25212, 16, -72.35, 1.06, 0.27593, 17, 77.69, 0.82, 0.23214], "hull": 39, "edges": [0, 76, 2, 4, 8, 10, 10, 12, 14, 16, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 50, 52, 56, 58, 52, 54, 54, 56, 64, 66, 62, 64, 58, 60, 60, 62, 0, 2, 4, 6, 6, 8, 12, 14, 16, 18, 18, 20, 20, 22, 28, 30, 30, 32, 78, 68, 66, 78, 78, 10, 12, 78, 22, 78, 24, 78, 38, 78, 36, 78, 34, 78, 0, 78, 76, 78, 52, 78, 50, 78, 48, 78], "width": 169, "height": 168}}, "body_outline": {"Simpy_body_outline": {"type": "mesh", "uvs": [0.36958, 0.00525, 0.45082, 0.05178, 0.54838, 0.05403, 0.66144, 0.07865, 0.72634, 0.1087, 0.83055, 0.11008, 0.87308, 0.15482, 0.87408, 0.25559, 0.91153, 0.3228, 0.94486, 0.41557, 0.94536, 0.49265, 0.99485, 0.56677, 0.99343, 0.63165, 0.94864, 0.67062, 0.90274, 0.70326, 0.85781, 0.7788, 0.78477, 0.85493, 0.72084, 0.89871, 0.69861, 0.95194, 0.65836, 0.9944, 0.59439, 0.99485, 0.52994, 0.95727, 0.45504, 0.95644, 0.38015, 0.95561, 0.30227, 0.93229, 0.23327, 0.89942, 0.17435, 0.85631, 0.08893, 0.85132, 0.04967, 0.8085, 0.04933, 0.69473, 0.02674, 0.641, 0.0088, 0.55596, 0.00979, 0.47226, 0.02173, 0.38693, 1e-05, 0.28627, 0.02732, 0.22849, 0.12408, 0.20568, 0.17818, 0.15061, 0.24675, 0.10686, 0.28666, 0.0321, 0.31494, 0.00539, 0.48489, 0.48835], "triangles": [26, 41, 25, 26, 27, 41, 27, 28, 41, 28, 29, 41, 29, 30, 41, 30, 31, 41, 41, 31, 32, 41, 32, 33, 35, 36, 41, 35, 41, 34, 34, 41, 33, 15, 41, 14, 14, 41, 13, 13, 41, 12, 12, 41, 11, 41, 10, 11, 9, 10, 41, 41, 6, 7, 41, 7, 8, 41, 8, 9, 5, 41, 4, 5, 6, 41, 19, 20, 41, 18, 19, 41, 21, 41, 20, 41, 21, 22, 41, 22, 23, 17, 18, 41, 24, 41, 23, 41, 24, 25, 16, 17, 41, 16, 41, 15, 36, 37, 41, 37, 38, 41, 38, 39, 41, 39, 40, 41, 40, 0, 41, 0, 1, 41, 41, 3, 4, 41, 2, 3, 41, 1, 2], "vertices": [3, 14, -1.96, 17.74, 0.99901, 16, -80.78, 92.1, 1e-05, 17, 69.26, 91.86, 0.00098, 3, 14, 11.22, 7.13, 0.95999, 13, 1.4, 154.89, 0, 16, -67.6, 81.48, 0.04001, 2, 14, 28.59, 3.92, 0.80922, 16, -50.23, 78.27, 0.19078, 4, 14, 48.09, -3.72, 0.60867, 13, 38.26, 144.04, 5e-05, 16, -30.73, 70.63, 0.39127, 17, 119.31, 70.4, 0, 4, 14, 58.82, -10.93, 0.47315, 13, 49, 136.83, 0.00018, 16, -20, 63.42, 0.52659, 17, 130.05, 63.19, 9e-05, 2, 14, 77.41, -14.18, 0.34687, 16, -1.42, 60.17, 0.65313, 2, 14, 83.72, -23.36, 0.3151, 16, 4.9, 50.99, 0.6849, 4, 14, 81.01, -41.29, 0.21215, 13, 71.18, 106.46, 4e-05, 16, 2.19, 33.06, 0.78756, 17, 152.23, 32.83, 0.00024, 2, 14, 85.78, -54.32, 0.09174, 16, 6.95, 20.04, 0.90826, 3, 14, 89.07, -71.76, 0.00474, 13, 79.24, 75.99, 0.00019, 16, 10.25, 2.59, 0.99507, 3, 13, 77.12, 62.28, 0.04243, 16, 8.12, -11.12, 0.95755, 17, 158.17, -11.35, 2e-05, 2, 13, 83.84, 47.68, 0.1089, 16, 14.84, -25.72, 0.8911, 2, 13, 81.73, 36.2, 0.14409, 16, 12.73, -37.21, 0.85591, 2, 13, 72.6, 30.56, 0.19924, 16, 3.61, -42.84, 0.80076, 4, 14, 73.29, -121.67, 5e-05, 13, 63.47, 26.09, 0.30609, 16, -5.53, -47.32, 0.69376, 17, 144.51, -47.55, 0.0001, 2, 13, 53.27, 13.96, 0.49937, 16, -15.73, -59.45, 0.50063, 3, 14, 47.86, -145.22, 1e-05, 13, 38.04, 2.54, 0.71107, 16, -30.96, -70.87, 0.28892, 3, 14, 35.18, -151.16, 0.0001, 13, 25.36, -3.4, 0.87391, 16, -43.64, -76.8, 0.12599, 2, 13, 19.86, -12.22, 0.95979, 16, -49.14, -85.62, 0.04021, 2, 13, 11.45, -18.6, 0.99111, 16, -57.55, -92.01, 0.00889, 2, 13, 0, -16.84, 0.99986, 17, 81.05, -90.47, 0.00014, 2, 13, -10.44, -8.3, 0.96733, 17, 70.61, -81.94, 0.03267, 2, 13, -23.79, -5.99, 0.85435, 17, 57.25, -79.63, 0.14565, 2, 13, -37.15, -3.68, 0.74137, 17, 43.89, -77.32, 0.25863, 2, 13, -50.4, 2.71, 0.61103, 17, 30.65, -70.93, 0.38897, 4, 14, -51.96, -137.22, 1e-05, 13, -61.79, 10.54, 0.47941, 16, -130.79, -62.87, 2e-05, 17, 19.26, -63.1, 0.52057, 4, 14, -61.25, -127.86, 0.0001, 13, -71.08, 19.9, 0.33569, 16, -140.08, -53.51, 0.00017, 17, 9.97, -53.74, 0.66404, 2, 13, -86.2, 23.25, 0.22731, 17, -5.15, -50.39, 0.77269, 2, 13, -91.99, 31.99, 0.19749, 17, -10.94, -41.65, 0.80251, 3, 13, -88.78, 52.22, 0.09972, 16, -157.78, -21.19, 5e-05, 17, -7.74, -21.42, 0.90022, 2, 13, -91.28, 62.42, 0.03463, 17, -10.23, -11.22, 0.96537, 2, 14, -82.22, -69.71, 0.01012, 17, -11, 4.41, 0.98988, 3, 14, -79.64, -54.87, 0.09208, 16, -158.47, 19.49, 0, 17, -8.42, 19.25, 0.90791, 3, 14, -75.06, -40.05, 0.23012, 16, -153.88, 34.3, 6e-05, 17, -3.84, 34.07, 0.76982, 2, 14, -76.05, -21.54, 0.34521, 17, -4.83, 52.58, 0.65479, 2, 14, -69.52, -12.06, 0.38242, 17, 1.7, 62.06, 0.61758, 4, 14, -51.57, -10.79, 0.51336, 13, -61.4, 136.96, 0.00038, 16, -130.4, 63.56, 8e-05, 17, 19.65, 63.33, 0.48617, 3, 14, -40.33, -2.57, 0.68278, 13, -50.15, 145.19, 0.00037, 17, 30.9, 71.55, 0.31685, 3, 14, -26.82, 3.23, 0.85518, 13, -36.65, 150.99, 0.00024, 17, 44.4, 77.35, 0.14458, 2, 14, -17.54, 15.36, 0.97323, 17, 53.68, 89.48, 0.02677, 2, 14, -11.72, 19.29, 0.98936, 17, 59.5, 93.41, 0.01064, 4, 14, 4.79, -71.43, 0.27154, 13, -5.04, 76.33, 0.23142, 16, -74.03, 2.92, 0.25556, 17, 76.01, 2.69, 0.24148], "hull": 41, "edges": [0, 80, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 38, 40, 40, 42, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 68, 70, 70, 72, 76, 78, 78, 80, 34, 36, 36, 38, 30, 32, 32, 34, 24, 26, 26, 28, 12, 14, 14, 16, 0, 2, 2, 4, 4, 6, 72, 74, 74, 76, 66, 68, 62, 64, 64, 66, 82, 24, 22, 82, 12, 82, 10, 82, 0, 82, 80, 82, 78, 82, 82, 66, 82, 68, 82, 70, 82, 56, 82, 54, 82, 40, 82, 38, 82, 36, 42, 44, 44, 46], "width": 181, "height": 180}}, "eyebrows_l": {"Simpy_eyebrows_l": {"rotation": -9.17, "width": 28, "height": 21}}, "eyebrows_r": {"Simpy_eyebrows_r": {"rotation": -9.17, "width": 30, "height": 15}}, "eyelid_l_r": {"Simpy_eyelid_l_r": {"type": "mesh", "uvs": [0.71881, 0.24387, 0.80211, 0.17576, 0.88858, 0.10067, 0.97176, 0.02179, 0.9718, 0.45269, 0.83827, 0.77979, 0.67214, 0.95529, 0.48625, 0.95516, 0.30036, 0.95503, 0.15942, 0.80747, 0.0803, 0.67149, 0.0149, 0.45677, 0.10586, 0.4412, 0.23333, 0.43168, 0.33783, 0.41572, 0.45813, 0.38335, 0.53872, 0.34435, 0.62343, 0.29811], "triangles": [14, 9, 13, 2, 3, 4, 7, 15, 16, 6, 16, 17, 17, 0, 5, 14, 15, 7, 4, 0, 1, 1, 2, 4, 17, 5, 6, 6, 7, 16, 8, 14, 7, 8, 9, 14, 9, 10, 13, 13, 10, 12, 5, 0, 4, 10, 11, 12], "vertices": [2, 7, 6.77, -2.31, 0.00703, 10, 4.64, 3.77, 0.99297, 2, 7, 9.72, -0.89, 0.04373, 10, 7.63, 5.12, 0.95627, 2, 7, 12.79, 0.69, 0.14917, 10, 10.74, 6.62, 0.85083, 2, 7, 15.74, 2.35, 0.37447, 10, 13.73, 8.2, 0.62553, 2, 7, 15.51, -7.12, 0.94205, 10, 13.25, -1.27, 0.05795, 1, 7, 10.66, -14.2, 1, 1, 7, 4.75, -17.92, 1, 1, 7, -1.75, -17.76, 1, 1, 7, -8.26, -17.59, 1, 2, 7, -13.11, -14.23, 0.98806, 10, -15.54, -7.61, 0.01194, 2, 7, -15.81, -11.17, 0.97272, 10, -18.16, -4.48, 0.02728, 2, 7, -17.98, -6.39, 0.93227, 10, -20.2, 0.35, 0.06773, 2, 7, -14.79, -6.13, 0.56251, 10, -17, 0.53, 0.43749, 2, 7, -10.32, -6.03, 0.26645, 10, -12.54, 0.52, 0.73355, 2, 7, -6.66, -5.77, 0.13825, 10, -8.87, 0.68, 0.86175, 2, 7, -2.43, -5.16, 0.05461, 10, -4.63, 1.18, 0.94539, 2, 7, 0.41, -4.37, 0.00752, 10, -1.76, 1.89, 0.99248, 1, 10, 1.25, 2.75, 1], "hull": 18, "edges": [6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 12, 14, 14, 16, 26, 28, 28, 30, 22, 24, 24, 26, 30, 32, 32, 34, 2, 0, 0, 34, 2, 4, 4, 6], "width": 35, "height": 22}}, "eyelid_l_R": {"Simpy_eyelid_l_l": {"type": "mesh", "uvs": [0.16101, 0.4497, 0.28195, 0.43811, 0.40918, 0.41324, 0.54101, 0.37721, 0.65646, 0.31624, 0.77043, 0.23925, 0.87882, 0.14249, 0.97994, 0.02805, 0.97584, 0.40287, 0.84556, 0.72984, 0.66592, 0.9523, 0.33333, 0.9591, 0.18914, 0.83389, 0.05021, 0.59032, 0.04007, 0.46129], "triangles": [12, 0, 1, 4, 5, 9, 3, 4, 10, 10, 2, 3, 11, 1, 2, 8, 5, 6, 10, 11, 2, 12, 1, 11, 10, 4, 9, 13, 0, 12, 8, 9, 5, 13, 14, 0, 8, 6, 7], "vertices": [2, 3, -14.23, -5.09, 0.32245, 6, -16.13, 4.23, 0.67755, 2, 3, -9.17, -5.62, 0.09826, 6, -11.13, 3.31, 0.90174, 1, 6, -5.8, 2.63, 1, 1, 6, -0.21, 2.18, 1, 1, 6, 4.85, 2.46, 1, 2, 3, 11.84, -4.18, 0.10057, 6, 9.93, 3.14, 0.89943, 2, 3, 16.71, -2.62, 0.39406, 6, 14.9, 4.33, 0.60594, 2, 3, 21.34, -0.58, 0.94767, 6, 19.68, 6, 0.05233, 1, 3, 19.73, -9.43, 1, 1, 3, 13.08, -16.31, 1, 1, 3, 4.78, -20.38, 1, 1, 3, -9.03, -18.31, 1, 2, 3, -14.53, -14.38, 0.99847, 6, -17.15, -5.01, 0.00153, 2, 3, -19.36, -7.68, 0.99447, 6, -21.45, 2.04, 0.00553, 2, 3, -19.29, -4.56, 0.92232, 6, -21.14, 5.15, 0.07768], "hull": 15, "edges": [14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 16, 18, 18, 20, 10, 12, 12, 14, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 28], "width": 42, "height": 24}}, "eyelid_u_r": {"Simpy_eyelid_u_r": {"type": "mesh", "uvs": [0.68197, 0.03628, 0.86653, 0.20099, 0.97291, 0.47918, 0.97408, 0.66046, 0.88465, 0.72883, 0.79259, 0.79401, 0.71018, 0.84344, 0.62514, 0.88968, 0.51163, 0.92486, 0.39813, 0.96005, 0.29774, 0.96217, 0.19736, 0.96428, 0.11187, 0.96394, 0.02639, 0.9636, 0.0274, 0.4485, 0.15689, 0.17999, 0.32129, 0.03498], "triangles": [5, 6, 1, 10, 11, 14, 10, 14, 9, 8, 9, 15, 16, 7, 8, 7, 0, 6, 14, 11, 12, 12, 13, 14, 15, 9, 14, 8, 15, 16, 7, 16, 0, 4, 5, 2, 1, 6, 0, 4, 2, 3, 2, 5, 1], "vertices": [1, 7, 5.71, 20.1, 1, 1, 7, 12.24, 15.49, 1, 2, 7, 15.89, 7.89, 0.97922, 9, 15.02, 7.42, 0.02078, 2, 7, 15.81, 3, 0.83457, 9, 14.53, 2.55, 0.16543, 2, 7, 12.55, 1.23, 0.48464, 9, 11.13, 1.06, 0.51536, 2, 7, 9.19, -0.45, 0.28531, 9, 7.65, -0.33, 0.71469, 2, 7, 6.19, -1.71, 0.17433, 9, 4.55, -1.33, 0.82567, 2, 7, 3.1, -2.88, 0.07402, 9, 1.37, -2.25, 0.92598, 2, 7, -1.01, -3.73, 0.04833, 9, -2.79, -2.75, 0.95167, 2, 7, -5.12, -4.58, 0.03284, 9, -6.96, -3.25, 0.96716, 2, 7, -8.73, -4.55, 0.13478, 9, -10.55, -2.92, 0.86522, 2, 7, -12.35, -4.51, 0.24221, 9, -14.15, -2.59, 0.75779, 2, 7, -15.42, -4.43, 0.41963, 9, -17.21, -2.25, 0.58037, 2, 7, -18.5, -4.35, 0.73572, 9, -20.27, -1.9, 0.26428, 1, 7, -18.12, 9.56, 1, 1, 7, -13.28, 16.69, 1, 1, 7, -7.27, 20.46, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 26, 28, 28, 30, 30, 32, 6, 8, 8, 10, 22, 24, 24, 26, 18, 20, 20, 22, 14, 16, 16, 18, 10, 12, 12, 14], "width": 36, "height": 27}}, "eyelid_u_R": {"Simpy_eyelid_u_l": {"type": "mesh", "uvs": [0.86975, 0.18877, 1, 0.56602, 1, 0.5691, 0.92098, 0.65093, 0.84197, 0.73276, 0.73577, 0.80385, 0.62958, 0.87494, 0.50217, 0.92089, 0.38613, 0.9507, 0.28658, 0.96716, 0.1984, 0.96749, 0.11021, 0.96782, 0.02203, 0.96815, 0.02254, 0.50865, 0.09761, 0.30063, 0.21332, 0.1271, 0.35046, 0.03181, 0.64937, 0.03147], "triangles": [4, 5, 0, 6, 17, 5, 7, 16, 6, 7, 8, 14, 8, 9, 14, 3, 0, 1, 2, 3, 1, 0, 5, 17, 4, 0, 3, 6, 16, 17, 7, 15, 16, 7, 14, 15, 13, 14, 9, 10, 13, 9, 11, 13, 10, 12, 13, 11], "vertices": [1, 3, 18.87, 14.01, 1, 2, 3, 22.6, 1.94, 0.92064, 5, 20.55, 0.53, 0.07936, 2, 3, 22.59, 1.85, 0.91881, 5, 20.52, 0.44, 0.08119, 2, 3, 18.84, -0.03, 0.54174, 5, 16.56, -0.93, 0.45826, 2, 3, 15.09, -1.91, 0.26912, 5, 12.6, -2.3, 0.73088, 2, 3, 10.25, -3.29, 0.10439, 5, 7.61, -3.02, 0.89561, 1, 5, 2.63, -3.75, 1, 2, 3, -0.23, -5.16, 0.00404, 5, -3.02, -3.49, 0.99596, 2, 3, -5.3, -5.24, 0.04831, 5, -8.05, -2.9, 0.95169, 2, 3, -9.6, -5.05, 0.19288, 5, -12.29, -2.14, 0.80712, 2, 3, -13.35, -4.45, 0.35026, 5, -15.93, -1.06, 0.64974, 2, 3, -17.09, -3.86, 0.58143, 5, -19.56, 0.03, 0.41857, 2, 3, -20.84, -3.27, 0.83864, 5, -23.19, 1.11, 0.16136, 1, 3, -18.62, 10.34, 1, 1, 3, -14.44, 15.99, 1, 1, 3, -8.7, 20.33, 1, 1, 3, -2.42, 22.22, 1, 1, 3, 10.27, 20.18, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 24, 26, 30, 32, 32, 34, 4, 6, 6, 8, 20, 22, 22, 24, 12, 14, 14, 16, 8, 10, 10, 12, 16, 18, 18, 20, 26, 28, 28, 30], "width": 43, "height": 30}}, "eye_L": {"Simpy_eye_r": {"x": -1.83, "y": 0.26, "rotation": -1.41, "width": 43, "height": 50}}, "eye_R": {"Simpy_eye_l": {"rotation": -9.17, "width": 52, "height": 57}}, "mouth_1": {"Simpy_mouth_1": {"type": "mesh", "uvs": [0.98559, 0.0343, 0.98613, 0.43711, 0.79506, 0.78553, 0.57275, 0.96517, 0.22046, 0.96474, 0.09779, 0.86708, 0.01467, 0.74273, 0.01406, 0.43055, 0.2796, 0.41358, 0.53056, 0.3434, 0.73615, 0.19247, 0.8915, 0.03341], "triangles": [1, 2, 10, 3, 9, 2, 4, 8, 3, 1, 11, 0, 11, 1, 10, 9, 10, 2, 5, 6, 7, 8, 5, 7, 8, 9, 3, 4, 5, 8], "vertices": [1, 15, 34.44, 6.13, 1, 2, 15, 32.68, -5.01, 0.41514, 22, 29.39, 9.29, 0.58486, 2, 15, 18.29, -12.57, 0.08274, 22, 15, 1.73, 0.91726, 2, 15, 2.57, -15.13, 0.01672, 22, -0.72, -0.83, 0.98328, 2, 15, -21.08, -11.3, 0.23061, 22, -24.37, 3, 0.76939, 2, 15, -28.88, -7.27, 0.47131, 22, -32.17, 7.03, 0.52869, 1, 15, -33.9, -2.93, 1, 1, 15, -32.55, 5.7, 1, 2, 15, -14.65, 3.3, 0.99441, 22, -17.94, 17.59, 0.00559, 1, 15, 2.51, 2.52, 1, 1, 15, 16.99, 4.46, 1, 1, 15, 28.12, 7.17, 1], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 68, "height": 28}}, "mouth_2": {"Simpy_mouth_2": {"rotation": -9.17, "width": 26, "height": 20}}, "mouth_base": {"Simpy_mouth_base": {"rotation": -9.17, "width": 76, "height": 39}}, "pupil_L": {"Simpy_pupil_r": {"rotation": -9.17, "width": 11, "height": 13}}, "pupil_R": {"Simpy_pupil_l": {"rotation": -9.17, "width": 11, "height": 13}}, "teeth": {"Simpy_teeth": {"type": "mesh", "uvs": [0.94616, 0.0302, 0.98514, 0.31334, 0.98498, 0.56433, 0.90983, 0.71924, 0.72697, 0.7193, 0.69394, 0.58324, 0.63201, 0.19446, 0.5612, 0.235, 0.57955, 0.63212, 0.54207, 0.93102, 0.41636, 0.94698, 0.2687, 0.96574, 0.12104, 0.98449, 0.05739, 0.92257, 0.01496, 0.75729, 0.0145, 0.34372, 0.12565, 0.34241, 0.2368, 0.34109, 0.37853, 0.29857, 0.52025, 0.25606, 0.84803, 0.03094], "triangles": [2, 3, 1, 3, 5, 1, 4, 5, 3, 16, 12, 14, 8, 10, 18, 9, 10, 8, 12, 13, 14, 10, 11, 18, 11, 12, 17, 5, 6, 20, 14, 15, 16, 19, 20, 7, 19, 7, 8, 8, 18, 19, 11, 17, 18, 6, 7, 20, 12, 16, 17, 0, 1, 20, 20, 1, 5], "vertices": [2, 15, 32.72, 5.39, 0.99143, 18, 43.78, 25.96, 0.00857, 3, 15, 33.82, -3.97, 0.45222, 18, 45.86, 16.77, 0.00706, 19, 9.1, 8.5, 0.54072, 3, 15, 32.53, -11.89, 0.0002, 18, 45.42, 8.75, 0.00857, 19, 7.15, 0.71, 0.99123, 2, 18, 40.2, 4.07, 0.00857, 19, 1.14, -2.91, 0.99143, 3, 15, 14.93, -14.08, 0.00742, 18, 28.15, 4.71, 0.00857, 19, -10.57, 0, 0.98401, 3, 15, 13.47, -9.43, 0.25373, 18, 26.2, 9.18, 0.00649, 19, -11.64, 4.75, 0.73978, 1, 15, 11.42, 3.5, 1, 1, 15, 6.6, 2.97, 1, 3, 15, 5.77, -9.77, 0.31874, 18, 18.58, 8.02, 0.59355, 19, -19.34, 5.05, 0.08771, 2, 18, 15.6, -1.4, 0.95788, 19, -24.05, -3.64, 0.04212, 2, 18, 7.29, -1.46, 0.95723, 19, -32.22, -2.14, 0.04277, 2, 18, -2.48, -1.54, 0.96857, 19, -41.83, -0.37, 0.03143, 2, 18, -12.24, -1.61, 0.96857, 19, -51.43, 1.39, 0.03143, 2, 18, -16.33, 0.59, 0.96855, 19, -55.03, 4.33, 0.03145, 2, 15, -31.66, -7.79, 0.29714, 18, -18.84, 6.02, 0.70286, 1, 15, -29.58, 5.28, 1, 1, 15, -22.33, 4.15, 1, 1, 15, -15.08, 3.03, 1, 1, 15, -5.63, 2.88, 1, 1, 15, 3.82, 2.73, 1, 2, 15, 26.33, 6.4, 0.99143, 18, 37.31, 26.28, 0.00857], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 24, 26, 26, 28, 28, 30, 38, 40, 34, 36, 36, 38, 30, 32, 32, 34, 18, 20, 20, 22, 22, 24, 14, 16, 16, 18, 10, 12, 12, 14], "width": 66, "height": 32}}}}], "animations": {"t0_000000": {"slots": {"body_outline": {"rgba": [{"color": "0000007f"}]}}}, "t0_405c80": {"slots": {"body_outline": {"rgba": [{"color": "405c80ff"}]}}}, "t1_Death": {"slots": {"blot": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3667, "name": "blot"}]}, "blot_drop2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.6667}]}, "blot_drop4": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop5": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop1"}, {"time": 0.5667}]}, "blot_drop6": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop7": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.7}]}, "blot_drop8": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3667, "name": "blot_drop1"}, {"time": 0.8}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop1"}, {"time": 0.5333}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop1"}, {"time": 0.5}]}, "body": {"attachment": [{"time": 0.3667}]}, "body_outline": {"attachment": [{"time": 0.3667}]}, "eyebrows_l": {"attachment": [{"time": 0.3667}]}, "eyebrows_r": {"attachment": [{"time": 0.3667}]}, "eyelid_l_r": {"attachment": [{}]}, "eyelid_l_R": {"attachment": [{}]}, "eyelid_u_r": {"attachment": [{}]}, "eyelid_u_R": {"attachment": [{}]}, "eye_L": {"attachment": [{"time": 0.3667}]}, "eye_R": {"attachment": [{"time": 0.3667}]}, "mouth_1": {"attachment": [{}]}, "mouth_2": {"attachment": [{"name": "Simpy_mouth_2"}, {"time": 0.3667}]}, "mouth_base": {"attachment": [{"time": 0.3667}]}, "pupil_L": {"attachment": [{"time": 0.3667}]}, "pupil_R": {"attachment": [{"time": 0.3667}]}, "teeth": {"attachment": [{}]}}, "bones": {"EyeLiid_R_U": {"translate": [{"x": -3.48, "y": 26.04}]}, "Eyelid_R_B": {"translate": [{"x": 0.59, "y": -12.83}]}, "Eyelid_L_U": {"translate": [{"x": -2.09, "y": 24.95}]}, "Eyelid_L_B": {"translate": [{"x": 0.18, "y": -13.45}]}, "eye_L": {"translate": [{"curve": [0.04, 0, 0.06, 28.69, 0.04, 0, 0.06, 13.84]}, {"time": 0.1, "x": 28.69, "y": 13.84, "curve": [0.122, 28.69, 0.144, 32.46, 0.122, 13.84, 0.144, 13.84]}, {"time": 0.1667, "x": 32.46, "y": 13.84, "curve": [0.189, 32.46, 0.211, 28.69, 0.189, 13.84, 0.211, 13.84]}, {"time": 0.2333, "x": 28.69, "y": 13.84, "curve": [0.256, 28.69, 0.278, 32.46, 0.256, 13.84, 0.278, 13.84]}, {"time": 0.3, "x": 32.46, "y": 13.84, "curve": [0.322, 32.46, 0.344, 28.69, 0.322, 13.84, 0.344, 13.84]}, {"time": 0.3667, "x": 28.69, "y": 13.84, "curve": [0.389, 28.69, 0.411, 32.46, 0.389, 13.84, 0.411, 13.84]}, {"time": 0.4333, "x": 32.46, "y": 13.84}], "scale": [{"x": 1.18, "y": 1.03, "curve": [0.04, 1.18, 0.06, 1.182, 0.04, 1.03, 0.06, 1.032]}, {"time": 0.1, "x": 1.182, "y": 1.032, "curve": [0.122, 1.182, 0.144, 1.261, 0.122, 1.032, 0.144, 1.101]}, {"time": 0.1667, "x": 1.261, "y": 1.101, "curve": [0.189, 1.261, 0.211, 1.182, 0.189, 1.101, 0.211, 1.032]}, {"time": 0.2333, "x": 1.182, "y": 1.032, "curve": [0.256, 1.182, 0.278, 1.261, 0.256, 1.032, 0.278, 1.101]}, {"time": 0.3, "x": 1.261, "y": 1.101, "curve": [0.322, 1.261, 0.344, 1.182, 0.322, 1.101, 0.344, 1.032]}, {"time": 0.3667, "x": 1.182, "y": 1.032, "curve": [0.389, 1.182, 0.411, 1.261, 0.389, 1.032, 0.411, 1.101]}, {"time": 0.4333, "x": 1.261, "y": 1.101}]}, "eye_R": {"translate": [{"curve": [0.04, 0, 0.06, -31.16, 0.04, 0, 0.06, 9.39]}, {"time": 0.1, "x": -31.16, "y": 9.39, "curve": [0.122, -31.16, 0.144, -35.85, 0.122, 9.39, 0.144, 9.39]}, {"time": 0.1667, "x": -35.85, "y": 9.39, "curve": [0.189, -35.85, 0.211, -31.16, 0.189, 9.39, 0.211, 9.39]}, {"time": 0.2333, "x": -31.16, "y": 9.39, "curve": [0.256, -31.16, 0.278, -35.85, 0.256, 9.39, 0.278, 9.39]}, {"time": 0.3, "x": -35.85, "y": 9.39, "curve": [0.322, -35.85, 0.344, -31.16, 0.322, 9.39, 0.344, 9.39]}, {"time": 0.3667, "x": -31.16, "y": 9.39, "curve": [0.389, -31.16, 0.411, -35.85, 0.389, 9.39, 0.411, 9.39]}, {"time": 0.4333, "x": -35.85, "y": 9.39}], "scale": [{"curve": [0.04, 1, 0.06, 1.002, 0.04, 1, 0.06, 1.002]}, {"time": 0.1, "x": 1.002, "y": 1.002, "curve": [0.122, 1.002, 0.144, 1.069, 0.122, 1.002, 0.144, 1.069]}, {"time": 0.1667, "x": 1.069, "y": 1.069, "curve": [0.189, 1.069, 0.211, 1.002, 0.189, 1.069, 0.211, 1.002]}, {"time": 0.2333, "x": 1.002, "y": 1.002, "curve": [0.256, 1.002, 0.278, 1.069, 0.256, 1.002, 0.278, 1.069]}, {"time": 0.3, "x": 1.069, "y": 1.069, "curve": [0.322, 1.069, 0.344, 1.002, 0.322, 1.069, 0.344, 1.002]}, {"time": 0.3667, "x": 1.002, "y": 1.002, "curve": [0.389, 1.002, 0.411, 1.069, 0.389, 1.002, 0.411, 1.069]}, {"time": 0.4333, "x": 1.069, "y": 1.069}]}, "Face": {"translate": [{"x": -6.82, "y": -6.34}]}, "cntr": {"rotate": [{"value": -1}], "translate": [{"y": 2.43}], "scale": [{"x": 1.02, "y": 1.02, "curve": [0.033, 1.02, 0.067, 1.286, 0.033, 1.02, 0.067, 0.954]}, {"time": 0.1, "x": 1.286, "y": 0.954, "curve": "stepped"}, {"time": 0.3, "x": 1.286, "y": 0.954, "curve": [0.322, 1.286, 0.344, 0.68, 0.322, 0.954, 0.344, 1.289]}, {"time": 0.3667, "x": 0.68, "y": 1.289}]}, "Eyes_scale": {"scale": [{}]}, "eyebrows_l": {"rotate": [{"curve": [0.04, 0, 0.06, -38.45]}, {"time": 0.1, "value": -38.45, "curve": [0.122, -38.45, 0.144, -45.56]}, {"time": 0.1667, "value": -45.56, "curve": [0.189, -45.56, 0.189, -38.45]}, {"time": 0.2333, "value": -38.45, "curve": [0.256, -38.45, 0.278, -45.56]}, {"time": 0.3, "value": -45.56, "curve": [0.322, -45.56, 0.322, -38.45]}, {"time": 0.3667, "value": -38.45, "curve": [0.389, -38.45, 0.411, -45.56]}, {"time": 0.4333, "value": -45.56}], "translate": [{"curve": [0.04, 0, 0.06, -18.7, 0.04, 0, 0.06, 9.1]}, {"time": 0.1, "x": -18.7, "y": 9.1, "curve": [0.122, -18.7, 0.144, -21.98, 0.122, 9.1, 0.144, 9.1]}, {"time": 0.1667, "x": -21.98, "y": 9.1, "curve": [0.189, -21.98, 0.189, -18.7, 0.189, 9.1, 0.189, 9.1]}, {"time": 0.2333, "x": -18.7, "y": 9.1, "curve": [0.256, -18.7, 0.278, -21.98, 0.256, 9.1, 0.278, 9.1]}, {"time": 0.3, "x": -21.98, "y": 9.1, "curve": [0.322, -21.98, 0.322, -18.7, 0.322, 9.1, 0.322, 9.1]}, {"time": 0.3667, "x": -18.7, "y": 9.1, "curve": [0.389, -18.7, 0.411, -21.98, 0.389, 9.1, 0.411, 9.1]}, {"time": 0.4333, "x": -21.98, "y": 9.1}], "scale": [{}]}, "eyebrows_r": {"rotate": [{"curve": [0.04, 0, 0.06, 31.16]}, {"time": 0.1, "value": 31.16, "curve": [0.122, 31.16, 0.144, 41.3]}, {"time": 0.1667, "value": 41.3, "curve": [0.189, 41.3, 0.189, 31.16]}, {"time": 0.2333, "value": 31.16, "curve": [0.256, 31.16, 0.278, 41.3]}, {"time": 0.3, "value": 41.3, "curve": [0.322, 41.3, 0.322, 31.16]}, {"time": 0.3667, "value": 31.16, "curve": [0.389, 31.16, 0.411, 41.3]}, {"time": 0.4333, "value": 41.3}], "translate": [{"curve": [0.04, 0, 0.06, 19.61, 0.04, 0, 0.06, 8.25]}, {"time": 0.1, "x": 19.61, "y": 8.25, "curve": [0.122, 19.61, 0.144, 24.77, 0.122, 8.25, 0.144, 10.14]}, {"time": 0.1667, "x": 24.77, "y": 10.14, "curve": [0.189, 24.77, 0.189, 19.61, 0.189, 10.14, 0.189, 8.25]}, {"time": 0.2333, "x": 19.61, "y": 8.25, "curve": [0.256, 19.61, 0.278, 24.77, 0.256, 8.25, 0.278, 10.14]}, {"time": 0.3, "x": 24.77, "y": 10.14, "curve": [0.322, 24.77, 0.322, 19.61, 0.322, 10.14, 0.322, 8.25]}, {"time": 0.3667, "x": 19.61, "y": 8.25, "curve": [0.389, 19.61, 0.411, 24.77, 0.389, 8.25, 0.411, 10.14]}, {"time": 0.4333, "x": 24.77, "y": 10.14}], "scale": [{}]}, "pupil_R": {"translate": [{"x": -5.96, "y": -7.67, "curve": [0.033, -5.96, 0.067, -18.34, 0.033, -7.67, 0.067, 3.18]}, {"time": 0.1, "x": -18.34, "y": 3.18}]}, "pupil_L": {"translate": [{"x": 4.86, "y": -6.62, "curve": [0.033, 4.86, 0.067, 12.06, 0.033, -6.62, 0.067, 7.44]}, {"time": 0.1, "x": 12.06, "y": 7.44}]}, "mouth_base": {"translate": [{"curve": [0.033, 0, 0.067, 0, 0.033, 0, 0.067, 32.03]}, {"time": 0.1, "y": 32.03}], "scale": [{"x": 0.77, "curve": [0.04, 0.77, 0.06, 1.088, 0.04, 1, 0.06, 0.603]}, {"time": 0.1, "x": 1.088, "y": 0.603, "curve": [0.122, 1.088, 0.144, 1.231, 0.122, 0.603, 0.144, 0.453]}, {"time": 0.1667, "x": 1.231, "y": 0.453, "curve": [0.189, 1.231, 0.211, 1.088, 0.189, 0.453, 0.211, 0.603]}, {"time": 0.2333, "x": 1.088, "y": 0.603, "curve": [0.256, 1.088, 0.278, 1.231, 0.256, 0.603, 0.278, 0.453]}, {"time": 0.3, "x": 1.231, "y": 0.453, "curve": [0.322, 1.231, 0.344, 1.088, 0.322, 0.453, 0.344, 0.603]}, {"time": 0.3667, "x": 1.088, "y": 0.603, "curve": [0.389, 1.088, 0.411, 1.231, 0.389, 0.603, 0.411, 0.453]}, {"time": 0.4333, "x": 1.231, "y": 0.453}]}, "Mouth_low": {"translate": [{"y": 13.72}]}, "Teeth_L": {"translate": [{"y": 14.6}]}, "Teeth_R": {"translate": [{"y": 14.6}]}, "mouth_2": {"translate": [{"curve": [0.04, 0, 0.06, 0, 0.04, 0, 0.06, 31.49]}, {"time": 0.1, "y": 31.49, "curve": [0.122, 0, 0.144, 0, 0.122, 31.49, 0.144, 33.66]}, {"time": 0.1667, "y": 33.66, "curve": [0.189, 0, 0.211, 0, 0.189, 33.66, 0.211, 31.49]}, {"time": 0.2333, "y": 31.49, "curve": [0.256, 0, 0.278, 0, 0.256, 31.49, 0.278, 33.66]}, {"time": 0.3, "y": 33.66, "curve": [0.322, 0, 0.344, 0, 0.322, 33.66, 0.344, 31.49]}, {"time": 0.3667, "y": 31.49, "curve": [0.389, 0, 0.411, 0, 0.389, 31.49, 0.411, 33.66]}, {"time": 0.4333, "y": 33.66}], "scale": [{"y": 1.297, "curve": [0.04, 1, 0.06, 2.416, 0.04, 1.297, 0.06, 0.989]}, {"time": 0.1, "x": 2.416, "y": 0.989, "curve": [0.122, 2.416, 0.144, 2.549, 0.122, 0.989, 0.144, 0.697]}, {"time": 0.1667, "x": 2.549, "y": 0.697, "curve": [0.189, 2.549, 0.211, 2.4, 0.189, 0.697, 0.211, 0.989]}, {"time": 0.2333, "x": 2.4, "y": 0.989, "curve": [0.256, 2.4, 0.278, 2.549, 0.256, 0.989, 0.278, 0.697]}, {"time": 0.3, "x": 2.549, "y": 0.697, "curve": [0.322, 2.549, 0.344, 2.4, 0.322, 0.697, 0.344, 0.989]}, {"time": 0.3667, "x": 2.4, "y": 0.989, "curve": [0.389, 2.4, 0.411, 2.549, 0.389, 0.989, 0.411, 0.697]}, {"time": 0.4333, "x": 2.549, "y": 0.697}]}, "body_top": {"translate": [{"curve": [0.04, 0, 0.06, 0, 0.04, 0, 0.06, -62.48]}, {"time": 0.1, "y": -62.48, "curve": [0.122, 0, 0.144, 0, 0.122, -62.48, 0.144, -70.05]}, {"time": 0.1667, "y": -70.05, "curve": [0.189, 0, 0.189, 0, 0.189, -70.05, 0.189, -62.48]}, {"time": 0.2333, "y": -62.48, "curve": [0.256, 0, 0.278, 0, 0.256, -62.48, 0.278, -70.05]}, {"time": 0.3, "y": -70.05, "curve": [0.322, 0, 0.322, 0, 0.322, -70.05, 0.322, -62.48]}, {"time": 0.3667, "y": -62.48, "curve": [0.389, 0, 0.411, 0, 0.389, -62.48, 0.411, -70.05]}, {"time": 0.4333, "y": -70.05}], "scale": [{"curve": [0.04, 1, 0.06, 0.775, 0.04, 1, 0.06, 0.775]}, {"time": 0.1, "x": 0.775, "y": 0.775, "curve": [0.122, 0.775, 0.144, 0.673, 0.122, 0.775, 0.144, 0.775]}, {"time": 0.1667, "x": 0.673, "y": 0.775, "curve": [0.189, 0.673, 0.189, 0.775, 0.189, 0.775, 0.189, 0.775]}, {"time": 0.2333, "x": 0.775, "y": 0.775, "curve": [0.256, 0.775, 0.278, 0.673, 0.256, 0.775, 0.278, 0.775]}, {"time": 0.3, "x": 0.673, "y": 0.775, "curve": [0.322, 0.673, 0.322, 0.775, 0.322, 0.775, 0.322, 0.775]}, {"time": 0.3667, "x": 0.775, "y": 0.775, "curve": [0.389, 0.775, 0.411, 0.673, 0.389, 0.775, 0.411, 0.775]}, {"time": 0.4333, "x": 0.673, "y": 0.775}]}, "body_bottom": {"translate": [{"curve": [0.04, 0, 0.06, 0, 0.04, 0, 0.06, 47.58]}, {"time": 0.1, "y": 47.58, "curve": [0.122, 0, 0.144, -0.1, 0.122, 47.58, 0.144, 54.35]}, {"time": 0.1667, "x": -0.1, "y": 54.35, "curve": [0.189, -0.1, 0.189, 0, 0.189, 54.35, 0.189, 47.58]}, {"time": 0.2333, "y": 47.58, "curve": [0.256, 0, 0.278, -0.1, 0.256, 47.58, 0.278, 54.35]}, {"time": 0.3, "x": -0.1, "y": 54.35, "curve": [0.322, -0.1, 0.322, 0, 0.322, 54.35, 0.322, 47.58]}, {"time": 0.3667, "y": 47.58, "curve": [0.389, 0, 0.411, -0.1, 0.389, 47.58, 0.411, 54.35]}, {"time": 0.4333, "x": -0.1, "y": 54.35}], "scale": [{"curve": [0.04, 1, 0.06, 0.54, 0.04, 1, 0.06, 1]}, {"time": 0.1, "x": 0.54, "curve": [0.122, 0.54, 0.144, 0.362, 0.122, 1, 0.144, 1]}, {"time": 0.1667, "x": 0.362, "curve": [0.189, 0.362, 0.189, 0.54, 0.189, 1, 0.189, 1]}, {"time": 0.2333, "x": 0.54, "curve": [0.256, 0.54, 0.278, 0.362, 0.256, 1, 0.278, 1]}, {"time": 0.3, "x": 0.362, "curve": [0.322, 0.362, 0.322, 0.54, 0.322, 1, 0.322, 1]}, {"time": 0.3667, "x": 0.54, "curve": [0.389, 0.54, 0.411, 0.362, 0.389, 1, 0.411, 1]}, {"time": 0.4333, "x": 0.362}]}, "Body_R": {"translate": [{"curve": [0.04, 0, 0.06, 22.53, 0.04, 0, 0.06, 0.39]}, {"time": 0.1, "x": 22.53, "y": 0.39, "curve": [0.122, 22.53, 0.144, 31.01, 0.122, 0.39, 0.144, 0.39]}, {"time": 0.1667, "x": 31.01, "y": 0.39, "curve": [0.189, 31.01, 0.189, 22.53, 0.189, 0.39, 0.189, 0.39]}, {"time": 0.2333, "x": 22.53, "y": 0.39, "curve": [0.256, 22.53, 0.278, 31.01, 0.256, 0.39, 0.278, 0.39]}, {"time": 0.3, "x": 31.01, "y": 0.39, "curve": [0.322, 31.01, 0.322, 22.53, 0.322, 0.39, 0.322, 0.39]}, {"time": 0.3667, "x": 22.53, "y": 0.39, "curve": [0.389, 22.53, 0.411, 26.3, 0.389, 0.39, 0.411, 0.39]}, {"time": 0.4333, "x": 26.3, "y": 0.39}], "scale": [{"curve": [0.04, 1, 0.06, 1, 0.04, 1, 0.06, 1.248]}, {"time": 0.1, "y": 1.248, "curve": [0.122, 1, 0.144, 1, 0.122, 1.248, 0.144, 1.339]}, {"time": 0.1667, "y": 1.339, "curve": [0.189, 1, 0.189, 1, 0.189, 1.339, 0.189, 1.248]}, {"time": 0.2333, "y": 1.248, "curve": [0.256, 1, 0.278, 1, 0.256, 1.248, 0.278, 1.339]}, {"time": 0.3, "y": 1.339, "curve": [0.322, 1, 0.322, 1, 0.322, 1.339, 0.322, 1.248]}, {"time": 0.3667, "y": 1.248, "curve": [0.389, 1, 0.411, 1, 0.389, 1.248, 0.411, 1.339]}, {"time": 0.4333, "y": 1.339}]}, "Body_L": {"translate": [{"curve": [0.04, 0, 0.06, -27.57, 0.04, 0, 0.06, 0]}, {"time": 0.1, "x": -27.57, "curve": [0.122, -27.57, 0.144, -41.02, 0.122, 0, 0.144, 0]}, {"time": 0.1667, "x": -41.02, "curve": [0.189, -41.02, 0.189, -27.57, 0.189, 0, 0.189, 0]}, {"time": 0.2333, "x": -27.57, "curve": [0.256, -27.57, 0.278, -41.02, 0.256, 0, 0.278, 0]}, {"time": 0.3, "x": -41.02, "curve": [0.322, -41.02, 0.322, -27.57, 0.322, 0, 0.322, 0]}, {"time": 0.3667, "x": -27.57, "curve": [0.389, -27.57, 0.411, -32.26, 0.389, 0, 0.411, 0]}, {"time": 0.4333, "x": -32.26}], "scale": [{"curve": [0.04, 1, 0.06, 1, 0.04, 1, 0.06, 1.153]}, {"time": 0.1, "y": 1.153, "curve": [0.122, 1, 0.144, 1, 0.122, 1.153, 0.144, 1.23]}, {"time": 0.1667, "y": 1.23, "curve": [0.189, 1, 0.189, 1, 0.189, 1.23, 0.189, 1.153]}, {"time": 0.2333, "y": 1.153, "curve": [0.256, 1, 0.278, 1, 0.256, 1.153, 0.278, 1.23]}, {"time": 0.3, "y": 1.23, "curve": [0.322, 1, 0.322, 1, 0.322, 1.23, 0.322, 1.153]}, {"time": 0.3667, "y": 1.153, "curve": [0.389, 1, 0.411, 1, 0.389, 1.153, 0.411, 1.23]}, {"time": 0.4333, "y": 1.23}]}, "blot": {"translate": [{"curve": [0.133, 0, 0.267, -12.21, 0.133, 0, 0.267, 29.42]}, {"time": 0.4, "x": -12.21, "y": 15.4, "curve": [0.511, -12.21, 0.622, -12.22, 0.469, 8.18, 0.622, 1.44]}, {"time": 0.7333, "x": -12.22, "y": 1.44}], "scale": [{"curve": [0.122, 1, 0.357, 0.7, 0.122, 1, 0.357, 0.7]}, {"time": 0.3667, "x": 0.85, "y": 0.85, "curve": [0.394, 1.26, 0.544, 1.2, 0.394, 1.26, 0.544, 1.2]}, {"time": 0.6333, "x": 1.2, "y": 1.2}]}, "blot_drops_control": {"translate": [{}, {"time": 0.4, "x": -0.54, "y": -0.54, "curve": [0.622, -0.54, 0.844, 0, 0.545, -0.67, 0.844, -16.59]}, {"time": 1.0667, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": [0.4, 0, 0.467, -67.84]}, {"time": 0.5333, "value": -67.84}], "translate": [{"curve": [0.111, 0, 0.259, 0, 0.111, 0, 0.323, 15.83]}, {"time": 0.3333, "x": 41.44, "y": 7.91, "curve": [0.398, 77.65, 0.489, 273.93, 0.394, -38.99, 0.502, -211.23]}, {"time": 0.5667, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{"curve": [0.111, 0, 0.301, -70.17]}, {"time": 0.3333, "value": -35.08, "curve": [0.396, 32.76, 0.556, 77.81]}, {"time": 0.6667, "value": 77.81}], "translate": [{"curve": [0.111, 0, 0.222, -22.4, 0.111, 0, 0.323, 0]}, {"time": 0.3333, "x": -74.68, "y": 24.95, "curve": [0.419, -115.07, 0.556, -322.02, 0.43, 261.05, 0.574, -337.68]}, {"time": 0.6667, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5333, "curve": [0.578, 1, 0.622, 0.4, 0.578, 1, 0.622, 0.4]}, {"time": 0.6667, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{"curve": [0.111, 0, 0.303, 0]}, {"time": 0.3333, "value": 16.41, "curve": [0.43, 68.37, 0.533, 77.81]}, {"time": 0.6333, "value": 77.81}], "translate": [{"curve": [0.111, 0, 0.311, 0, 0.111, 0, 0.316, 0]}, {"time": 0.3333, "x": -48.78, "y": -9.58, "curve": [0.387, -164.38, 0.533, -211.51, 0.411, -53.5, 0.562, -476.89]}, {"time": 0.6333, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": [0.111, 0, 0.31, 0, 0.111, 0, 0.25, 58.15]}, {"time": 0.3333, "x": 54.1, "y": 29.08, "curve": [0.401, 209.62, 0.467, 276.96, 0.401, 5.57, 0.475, -88.86]}, {"time": 0.5333, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": [0.111, 0, 0.31, 0, 0.111, 0, 0.315, 0]}, {"time": 0.3333, "x": -53.64, "y": 36.2, "curve": [0.381, -164.57, 0.476, -313.12, 0.41, 187.62, 0.493, -267.19]}, {"time": 0.5667, "x": -368.12, "y": -387.78}]}, "blot_drop5": {"rotate": [{"curve": [0.111, 0, 0.222, 103.14]}, {"time": 0.3333, "value": 103.14, "curve": [0.422, 103.14, 0.585, 97.56]}, {"time": 0.6, "value": 97.56}], "translate": [{"curve": [0.111, 0, 0.222, 6.98, 0.111, 0, 0.31, 0]}, {"time": 0.3333, "x": 35.48, "y": -17.82, "curve": [0.422, 58.13, 0.533, 37.99, 0.411, -78.13, 0.55, -373.65]}, {"time": 0.6333, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": [0.111, 0, 0.298, 21.09, 0.111, 0, 0.318, 0]}, {"time": 0.3333, "x": 10.55, "y": -19.73, "curve": [0.39, -6.01, 0.437, -21.01, 0.38, -79.55, 0.427, -201.42]}, {"time": 0.5, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{"curve": [0.111, 0, 0.263, 0]}, {"time": 0.3333, "value": -75.4, "curve": [0.376, -120.98, 0.46, -263.98]}, {"time": 0.5667, "value": -261.68}], "translate": [{"curve": [0.111, 0, 0.326, 0, 0.111, 0, 0.328, 0]}, {"time": 0.3333, "x": 9.31, "y": 29.92, "curve": [0.425, 118.46, 0.578, 297.6, 0.389, 351.23, 0.547, 364.92]}, {"time": 0.7, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667, "curve": [0.611, 1, 0.656, 0.4, 0.611, 1, 0.656, 0.4]}, {"time": 0.7, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": [0.122, 0, 0.326, -53.64, 0.122, 0, 0.297, 83.69]}, {"time": 0.3667, "x": -53.64, "y": 83.69, "curve": [0.467, -154.19, 0.65, -338.78, 0.538, 242.44, 0.692, -264.59]}, {"time": 0.8, "x": -378.34, "y": -598.88}]}}}, "t1_Death2": {"slots": {"blot": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3667, "name": "blot"}]}, "blot_drop2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.6667}]}, "blot_drop4": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop5": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop1"}, {"time": 0.5667}]}, "blot_drop6": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop7": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.7}]}, "blot_drop8": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3667, "name": "blot_drop1"}, {"time": 0.8}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop1"}, {"time": 0.5333}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop1"}, {"time": 0.5}]}, "body": {"attachment": [{"name": "Simpy_body"}, {"time": 0.3667}]}, "body_outline": {"attachment": [{"name": "Simpy_body_outline"}, {"time": 0.3667}]}, "eyebrows_l": {"attachment": [{"name": "Simpy_eyebrows_l"}, {"time": 0.3667}]}, "eyebrows_r": {"attachment": [{"name": "Simpy_eyebrows_r"}, {"time": 0.3667}]}, "eyelid_l_r": {"attachment": [{}]}, "eyelid_l_R": {"attachment": [{}]}, "eyelid_u_r": {"attachment": [{}]}, "eyelid_u_R": {"attachment": [{}]}, "eye_L": {"attachment": [{"name": "Simpy_eye_r"}, {"time": 0.3667}]}, "eye_R": {"attachment": [{"name": "Simpy_eye_l"}, {"time": 0.3667}]}, "mouth_1": {"attachment": [{}]}, "mouth_2": {"attachment": [{"name": "Simpy_mouth_2"}, {"time": 0.3667}]}, "mouth_base": {"attachment": [{"name": "Simpy_mouth_base"}, {"time": 0.3667}]}, "pupil_L": {"attachment": [{"name": "Simpy_pupil_r"}, {"time": 0.3667}]}, "pupil_R": {"attachment": [{"name": "Simpy_pupil_l"}, {"time": 0.3667}]}, "teeth": {"attachment": [{}]}}, "bones": {"EyeLiid_R_U": {"translate": [{"x": -3.48, "y": 26.04}]}, "Eyelid_R_B": {"translate": [{"x": 0.59, "y": -12.83}]}, "Eyelid_L_U": {"translate": [{"x": -2.09, "y": 24.95}]}, "Eyelid_L_B": {"translate": [{"x": 0.18, "y": -13.45}]}, "eye_L": {"translate": [{"curve": [0.04, 0, 0.06, 36.68, 0.04, 0, 0.06, 13.84]}, {"time": 0.1, "x": 36.68, "y": 13.84, "curve": [0.122, 36.68, 0.144, 40.45, 0.122, 13.84, 0.144, 13.84]}, {"time": 0.1667, "x": 40.45, "y": 13.84, "curve": [0.189, 40.45, 0.211, 36.68, 0.189, 13.84, 0.211, 13.84]}, {"time": 0.2333, "x": 36.68, "y": 13.84, "curve": [0.256, 36.68, 0.278, 40.45, 0.256, 13.84, 0.278, 13.84]}, {"time": 0.3, "x": 40.45, "y": 13.84, "curve": [0.322, 40.45, 0.344, 36.68, 0.322, 13.84, 0.344, 13.84]}, {"time": 0.3667, "x": 36.68, "y": 13.84, "curve": [0.389, 36.68, 0.411, 40.45, 0.389, 13.84, 0.411, 13.84]}, {"time": 0.4333, "x": 40.45, "y": 13.84}], "scale": [{"x": 1.18, "y": 1.03, "curve": [0.04, 1.18, 0.06, 1.182, 0.04, 1.03, 0.06, 1.032]}, {"time": 0.1, "x": 1.182, "y": 1.032, "curve": [0.122, 1.182, 0.144, 1.261, 0.122, 1.032, 0.144, 1.101]}, {"time": 0.1667, "x": 1.261, "y": 1.101, "curve": [0.189, 1.261, 0.211, 1.182, 0.189, 1.101, 0.211, 1.032]}, {"time": 0.2333, "x": 1.182, "y": 1.032, "curve": [0.256, 1.182, 0.278, 1.261, 0.256, 1.032, 0.278, 1.101]}, {"time": 0.3, "x": 1.261, "y": 1.101, "curve": [0.322, 1.261, 0.344, 1.182, 0.322, 1.101, 0.344, 1.032]}, {"time": 0.3667, "x": 1.182, "y": 1.032, "curve": [0.389, 1.182, 0.411, 1.261, 0.389, 1.032, 0.411, 1.101]}, {"time": 0.4333, "x": 1.261, "y": 1.101}]}, "eye_R": {"translate": [{"curve": [0.04, 0, 0.06, -34.19, 0.04, 0, 0.06, 9.39]}, {"time": 0.1, "x": -34.19, "y": 9.39, "curve": [0.122, -34.19, 0.144, -38.88, 0.122, 9.39, 0.144, 9.39]}, {"time": 0.1667, "x": -38.88, "y": 9.39, "curve": [0.189, -38.88, 0.211, -34.19, 0.189, 9.39, 0.211, 9.39]}, {"time": 0.2333, "x": -34.19, "y": 9.39, "curve": [0.256, -34.19, 0.278, -38.88, 0.256, 9.39, 0.278, 9.39]}, {"time": 0.3, "x": -38.88, "y": 9.39, "curve": [0.322, -38.88, 0.344, -34.19, 0.322, 9.39, 0.344, 9.39]}, {"time": 0.3667, "x": -34.19, "y": 9.39, "curve": [0.389, -34.19, 0.411, -38.88, 0.389, 9.39, 0.411, 9.39]}, {"time": 0.4333, "x": -38.88, "y": 9.39}], "scale": [{"curve": [0.04, 1, 0.06, 1.002, 0.04, 1, 0.06, 1.002]}, {"time": 0.1, "x": 1.002, "y": 1.002, "curve": [0.122, 1.002, 0.144, 1.069, 0.122, 1.002, 0.144, 1.069]}, {"time": 0.1667, "x": 1.069, "y": 1.069, "curve": [0.189, 1.069, 0.211, 1.002, 0.189, 1.069, 0.211, 1.002]}, {"time": 0.2333, "x": 1.002, "y": 1.002, "curve": [0.256, 1.002, 0.278, 1.069, 0.256, 1.002, 0.278, 1.069]}, {"time": 0.3, "x": 1.069, "y": 1.069, "curve": [0.322, 1.069, 0.344, 1.002, 0.322, 1.069, 0.344, 1.002]}, {"time": 0.3667, "x": 1.002, "y": 1.002, "curve": [0.389, 1.002, 0.411, 1.069, 0.389, 1.002, 0.411, 1.069]}, {"time": 0.4333, "x": 1.069, "y": 1.069}]}, "Face": {"translate": [{"x": -6.82, "y": -6.34}]}, "cntr": {"rotate": [{"value": -1}], "translate": [{"y": 2.43}], "scale": [{"x": 1.02, "y": 1.02, "curve": [0.033, 1.02, 0.067, 1.286, 0.033, 1.02, 0.067, 0.954]}, {"time": 0.1, "x": 1.286, "y": 0.954, "curve": "stepped"}, {"time": 0.3, "x": 1.286, "y": 0.954, "curve": [0.322, 1.286, 0.344, 0.68, 0.322, 0.954, 0.344, 1.289]}, {"time": 0.3667, "x": 0.68, "y": 1.289}]}, "Eyes_scale": {"scale": [{}]}, "eyebrows_l": {"rotate": [{"curve": [0.04, 0, 0.06, -38.45]}, {"time": 0.1, "value": -38.45, "curve": [0.122, -38.45, 0.144, -45.56]}, {"time": 0.1667, "value": -45.56, "curve": [0.189, -45.56, 0.189, -38.45]}, {"time": 0.2333, "value": -38.45, "curve": [0.256, -38.45, 0.278, -45.56]}, {"time": 0.3, "value": -45.56, "curve": [0.322, -45.56, 0.322, -38.45]}, {"time": 0.3667, "value": -38.45, "curve": [0.389, -38.45, 0.411, -45.56]}, {"time": 0.4333, "value": -45.56}], "translate": [{"curve": [0.04, 0, 0.06, -18.7, 0.04, 0, 0.06, 9.1]}, {"time": 0.1, "x": -18.7, "y": 9.1, "curve": [0.122, -18.7, 0.144, -21.98, 0.122, 9.1, 0.144, 9.1]}, {"time": 0.1667, "x": -21.98, "y": 9.1, "curve": [0.189, -21.98, 0.189, -18.7, 0.189, 9.1, 0.189, 9.1]}, {"time": 0.2333, "x": -18.7, "y": 9.1, "curve": [0.256, -18.7, 0.278, -21.98, 0.256, 9.1, 0.278, 9.1]}, {"time": 0.3, "x": -21.98, "y": 9.1, "curve": [0.322, -21.98, 0.322, -18.7, 0.322, 9.1, 0.322, 9.1]}, {"time": 0.3667, "x": -18.7, "y": 9.1, "curve": [0.389, -18.7, 0.411, -21.98, 0.389, 9.1, 0.411, 9.1]}, {"time": 0.4333, "x": -21.98, "y": 9.1}], "scale": [{}]}, "eyebrows_r": {"rotate": [{"curve": [0.04, 0, 0.06, 31.16]}, {"time": 0.1, "value": 31.16, "curve": [0.122, 31.16, 0.144, 41.3]}, {"time": 0.1667, "value": 41.3, "curve": [0.189, 41.3, 0.189, 31.16]}, {"time": 0.2333, "value": 31.16, "curve": [0.256, 31.16, 0.278, 41.3]}, {"time": 0.3, "value": 41.3, "curve": [0.322, 41.3, 0.322, 31.16]}, {"time": 0.3667, "value": 31.16, "curve": [0.389, 31.16, 0.411, 41.3]}, {"time": 0.4333, "value": 41.3}], "translate": [{"curve": [0.04, 0, 0.06, 19.61, 0.04, 0, 0.06, 8.25]}, {"time": 0.1, "x": 19.61, "y": 8.25, "curve": [0.122, 19.61, 0.144, 24.77, 0.122, 8.25, 0.144, 10.14]}, {"time": 0.1667, "x": 24.77, "y": 10.14, "curve": [0.189, 24.77, 0.189, 19.61, 0.189, 10.14, 0.189, 8.25]}, {"time": 0.2333, "x": 19.61, "y": 8.25, "curve": [0.256, 19.61, 0.278, 24.77, 0.256, 8.25, 0.278, 10.14]}, {"time": 0.3, "x": 24.77, "y": 10.14, "curve": [0.322, 24.77, 0.322, 19.61, 0.322, 10.14, 0.322, 8.25]}, {"time": 0.3667, "x": 19.61, "y": 8.25, "curve": [0.389, 19.61, 0.411, 24.77, 0.389, 8.25, 0.411, 10.14]}, {"time": 0.4333, "x": 24.77, "y": 10.14}], "scale": [{}]}, "pupil_R": {"translate": [{"x": -5.96, "y": -7.67, "curve": [0.033, -5.96, 0.067, -18.34, 0.033, -7.67, 0.067, 3.18]}, {"time": 0.1, "x": -18.34, "y": 3.18}]}, "pupil_L": {"translate": [{"x": 4.86, "y": -6.62, "curve": [0.033, 4.86, 0.067, 12.06, 0.033, -6.62, 0.067, 7.44]}, {"time": 0.1, "x": 12.06, "y": 7.44}]}, "mouth_base": {"translate": [{"curve": [0.033, 0, 0.067, 0, 0.033, 0, 0.067, 32.03]}, {"time": 0.1, "y": 32.03}], "scale": [{"x": 0.77, "curve": [0.04, 0.77, 0.06, 1.088, 0.04, 1, 0.06, 0.603]}, {"time": 0.1, "x": 1.088, "y": 0.603, "curve": [0.122, 1.088, 0.144, 1.231, 0.122, 0.603, 0.144, 0.453]}, {"time": 0.1667, "x": 1.231, "y": 0.453, "curve": [0.189, 1.231, 0.211, 1.088, 0.189, 0.453, 0.211, 0.603]}, {"time": 0.2333, "x": 1.088, "y": 0.603, "curve": [0.256, 1.088, 0.278, 1.231, 0.256, 0.603, 0.278, 0.453]}, {"time": 0.3, "x": 1.231, "y": 0.453, "curve": [0.322, 1.231, 0.344, 1.088, 0.322, 0.453, 0.344, 0.603]}, {"time": 0.3667, "x": 1.088, "y": 0.603, "curve": [0.389, 1.088, 0.411, 1.231, 0.389, 0.603, 0.411, 0.453]}, {"time": 0.4333, "x": 1.231, "y": 0.453}]}, "Mouth_low": {"translate": [{"y": 13.72}]}, "Teeth_L": {"translate": [{"y": 14.6}]}, "Teeth_R": {"translate": [{"y": 14.6}]}, "mouth_2": {"translate": [{"curve": [0.04, 0, 0.06, 0, 0.04, 0, 0.06, 31.49]}, {"time": 0.1, "y": 31.49, "curve": [0.122, 0, 0.144, 0, 0.122, 31.49, 0.144, 33.66]}, {"time": 0.1667, "y": 33.66, "curve": [0.189, 0, 0.211, 0, 0.189, 33.66, 0.211, 31.49]}, {"time": 0.2333, "y": 31.49, "curve": [0.256, 0, 0.278, 0, 0.256, 31.49, 0.278, 33.66]}, {"time": 0.3, "y": 33.66, "curve": [0.322, 0, 0.344, 0, 0.322, 33.66, 0.344, 31.49]}, {"time": 0.3667, "y": 31.49, "curve": [0.389, 0, 0.411, 0, 0.389, 31.49, 0.411, 33.66]}, {"time": 0.4333, "y": 33.66}], "scale": [{"y": 1.297, "curve": [0.04, 1, 0.06, 2.416, 0.04, 1.297, 0.06, 0.989]}, {"time": 0.1, "x": 2.416, "y": 0.989, "curve": [0.122, 2.416, 0.144, 2.549, 0.122, 0.989, 0.144, 0.697]}, {"time": 0.1667, "x": 2.549, "y": 0.697, "curve": [0.189, 2.549, 0.211, 2.4, 0.189, 0.697, 0.211, 0.989]}, {"time": 0.2333, "x": 2.4, "y": 0.989, "curve": [0.256, 2.4, 0.278, 2.549, 0.256, 0.989, 0.278, 0.697]}, {"time": 0.3, "x": 2.549, "y": 0.697, "curve": [0.322, 2.549, 0.344, 2.4, 0.322, 0.697, 0.344, 0.989]}, {"time": 0.3667, "x": 2.4, "y": 0.989, "curve": [0.389, 2.4, 0.411, 2.549, 0.389, 0.989, 0.411, 0.697]}, {"time": 0.4333, "x": 2.549, "y": 0.697}]}, "body_top": {"translate": [{"curve": [0.04, 0, 0.06, 0, 0.04, 0, 0.06, -62.48]}, {"time": 0.1, "y": -62.48, "curve": [0.122, 0, 0.144, 0, 0.122, -62.48, 0.144, -70.05]}, {"time": 0.1667, "y": -70.05, "curve": [0.189, 0, 0.189, 0, 0.189, -70.05, 0.189, -62.48]}, {"time": 0.2333, "y": -62.48, "curve": [0.256, 0, 0.278, 0, 0.256, -62.48, 0.278, -70.05]}, {"time": 0.3, "y": -70.05, "curve": [0.322, 0, 0.322, 0, 0.322, -70.05, 0.322, -62.48]}, {"time": 0.3667, "y": -62.48, "curve": [0.389, 0, 0.411, 0, 0.389, -62.48, 0.411, -70.05]}, {"time": 0.4333, "y": -70.05}], "scale": [{"curve": [0.04, 1, 0.06, 0.775, 0.04, 1, 0.06, 0.775]}, {"time": 0.1, "x": 0.775, "y": 0.775, "curve": [0.122, 0.775, 0.144, 0.673, 0.122, 0.775, 0.144, 0.775]}, {"time": 0.1667, "x": 0.673, "y": 0.775, "curve": [0.189, 0.673, 0.189, 0.775, 0.189, 0.775, 0.189, 0.775]}, {"time": 0.2333, "x": 0.775, "y": 0.775, "curve": [0.256, 0.775, 0.278, 0.673, 0.256, 0.775, 0.278, 0.775]}, {"time": 0.3, "x": 0.673, "y": 0.775, "curve": [0.322, 0.673, 0.322, 0.775, 0.322, 0.775, 0.322, 0.775]}, {"time": 0.3667, "x": 0.775, "y": 0.775, "curve": [0.389, 0.775, 0.411, 0.673, 0.389, 0.775, 0.411, 0.775]}, {"time": 0.4333, "x": 0.673, "y": 0.775}]}, "body_bottom": {"translate": [{"curve": [0.04, 0, 0.06, 0, 0.04, 0, 0.06, 47.58]}, {"time": 0.1, "y": 47.58, "curve": [0.122, 0, 0.144, -0.1, 0.122, 47.58, 0.144, 54.35]}, {"time": 0.1667, "x": -0.1, "y": 54.35, "curve": [0.189, -0.1, 0.189, 0, 0.189, 54.35, 0.189, 47.58]}, {"time": 0.2333, "y": 47.58, "curve": [0.256, 0, 0.278, -0.1, 0.256, 47.58, 0.278, 54.35]}, {"time": 0.3, "x": -0.1, "y": 54.35, "curve": [0.322, -0.1, 0.322, 0, 0.322, 54.35, 0.322, 47.58]}, {"time": 0.3667, "y": 47.58, "curve": [0.389, 0, 0.411, -0.1, 0.389, 47.58, 0.411, 54.35]}, {"time": 0.4333, "x": -0.1, "y": 54.35}], "scale": [{"curve": [0.04, 1, 0.06, 0.54, 0.04, 1, 0.06, 1]}, {"time": 0.1, "x": 0.54, "curve": [0.122, 0.54, 0.144, 0.362, 0.122, 1, 0.144, 1]}, {"time": 0.1667, "x": 0.362, "curve": [0.189, 0.362, 0.189, 0.54, 0.189, 1, 0.189, 1]}, {"time": 0.2333, "x": 0.54, "curve": [0.256, 0.54, 0.278, 0.362, 0.256, 1, 0.278, 1]}, {"time": 0.3, "x": 0.362, "curve": [0.322, 0.362, 0.322, 0.54, 0.322, 1, 0.322, 1]}, {"time": 0.3667, "x": 0.54, "curve": [0.389, 0.54, 0.411, 0.362, 0.389, 1, 0.411, 1]}, {"time": 0.4333, "x": 0.362}]}, "Body_R": {"translate": [{"curve": [0.04, 0, 0.06, 22.53, 0.04, 0, 0.06, 0.39]}, {"time": 0.1, "x": 22.53, "y": 0.39, "curve": [0.122, 22.53, 0.144, 31.01, 0.122, 0.39, 0.144, 0.39]}, {"time": 0.1667, "x": 31.01, "y": 0.39, "curve": [0.189, 31.01, 0.189, 22.53, 0.189, 0.39, 0.189, 0.39]}, {"time": 0.2333, "x": 22.53, "y": 0.39, "curve": [0.256, 22.53, 0.278, 31.01, 0.256, 0.39, 0.278, 0.39]}, {"time": 0.3, "x": 31.01, "y": 0.39, "curve": [0.322, 31.01, 0.322, 22.53, 0.322, 0.39, 0.322, 0.39]}, {"time": 0.3667, "x": 22.53, "y": 0.39, "curve": [0.389, 22.53, 0.411, 26.3, 0.389, 0.39, 0.411, 0.39]}, {"time": 0.4333, "x": 26.3, "y": 0.39}], "scale": [{"curve": [0.04, 1, 0.06, 1, 0.04, 1, 0.06, 1.248]}, {"time": 0.1, "y": 1.248, "curve": [0.122, 1, 0.144, 1, 0.122, 1.248, 0.144, 1.339]}, {"time": 0.1667, "y": 1.339, "curve": [0.189, 1, 0.189, 1, 0.189, 1.339, 0.189, 1.248]}, {"time": 0.2333, "y": 1.248, "curve": [0.256, 1, 0.278, 1, 0.256, 1.248, 0.278, 1.339]}, {"time": 0.3, "y": 1.339, "curve": [0.322, 1, 0.322, 1, 0.322, 1.339, 0.322, 1.248]}, {"time": 0.3667, "y": 1.248, "curve": [0.389, 1, 0.411, 1, 0.389, 1.248, 0.411, 1.339]}, {"time": 0.4333, "y": 1.339}]}, "Body_L": {"translate": [{"curve": [0.04, 0, 0.06, -27.57, 0.04, 0, 0.06, 0]}, {"time": 0.1, "x": -27.57, "curve": [0.122, -27.57, 0.144, -41.02, 0.122, 0, 0.144, 0]}, {"time": 0.1667, "x": -41.02, "curve": [0.189, -41.02, 0.189, -27.57, 0.189, 0, 0.189, 0]}, {"time": 0.2333, "x": -27.57, "curve": [0.256, -27.57, 0.278, -41.02, 0.256, 0, 0.278, 0]}, {"time": 0.3, "x": -41.02, "curve": [0.322, -41.02, 0.322, -27.57, 0.322, 0, 0.322, 0]}, {"time": 0.3667, "x": -27.57, "curve": [0.389, -27.57, 0.411, -32.26, 0.389, 0, 0.411, 0]}, {"time": 0.4333, "x": -32.26}], "scale": [{"curve": [0.04, 1, 0.06, 1, 0.04, 1, 0.06, 1.153]}, {"time": 0.1, "y": 1.153, "curve": [0.122, 1, 0.144, 1, 0.122, 1.153, 0.144, 1.23]}, {"time": 0.1667, "y": 1.23, "curve": [0.189, 1, 0.189, 1, 0.189, 1.23, 0.189, 1.153]}, {"time": 0.2333, "y": 1.153, "curve": [0.256, 1, 0.278, 1, 0.256, 1.153, 0.278, 1.23]}, {"time": 0.3, "y": 1.23, "curve": [0.322, 1, 0.322, 1, 0.322, 1.23, 0.322, 1.153]}, {"time": 0.3667, "y": 1.153, "curve": [0.389, 1, 0.411, 1, 0.389, 1.153, 0.411, 1.23]}, {"time": 0.4333, "y": 1.23}]}, "blot": {"translate": [{"curve": [0.133, 0, 0.267, -12.21, 0.133, 0, 0.267, 29.42]}, {"time": 0.4, "x": -12.21, "y": 15.4, "curve": [0.511, -12.21, 0.622, -12.22, 0.469, 8.18, 0.622, 1.44]}, {"time": 0.7333, "x": -12.22, "y": 1.44}], "scale": [{"curve": [0.122, 1, 0.357, 0.7, 0.122, 1, 0.357, 0.7]}, {"time": 0.3667, "x": 0.85, "y": 0.85, "curve": [0.394, 1.26, 0.544, 1.2, 0.394, 1.26, 0.544, 1.2]}, {"time": 0.6333, "x": 1.2, "y": 1.2}]}, "blot_drops_control": {"translate": [{"curve": [0.133, 0, 0.267, -0.54, 0.133, 0, 0.267, -0.42]}, {"time": 0.4, "x": -0.54, "y": -0.54, "curve": [0.622, -0.54, 0.844, 0, 0.545, -0.67, 0.844, -16.59]}, {"time": 1.0667, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": [0.4, 0, 0.467, -67.84]}, {"time": 0.5333, "value": -67.84}], "translate": [{"curve": [0.111, 0, 0.259, 0, 0.111, 0, 0.323, 15.83]}, {"time": 0.3333, "x": 41.44, "y": 7.91, "curve": [0.398, 77.65, 0.489, 273.93, 0.394, -38.99, 0.502, -211.23]}, {"time": 0.5667, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{"curve": [0.111, 0, 0.301, -70.17]}, {"time": 0.3333, "value": -35.08, "curve": [0.396, 32.76, 0.556, 77.81]}, {"time": 0.6667, "value": 77.81}], "translate": [{"curve": [0.111, 0, 0.222, -22.4, 0.111, 0, 0.323, 0]}, {"time": 0.3333, "x": -74.68, "y": 24.95, "curve": [0.419, -115.07, 0.556, -322.02, 0.43, 261.05, 0.574, -337.68]}, {"time": 0.6667, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5333, "curve": [0.578, 1, 0.622, 0.4, 0.578, 1, 0.622, 0.4]}, {"time": 0.6667, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{"curve": [0.111, 0, 0.303, 0]}, {"time": 0.3333, "value": 16.41, "curve": [0.43, 68.37, 0.533, 77.81]}, {"time": 0.6333, "value": 77.81}], "translate": [{"curve": [0.111, 0, 0.311, 0, 0.111, 0, 0.316, 0]}, {"time": 0.3333, "x": -48.78, "y": -9.58, "curve": [0.387, -164.38, 0.533, -211.51, 0.411, -53.5, 0.562, -476.89]}, {"time": 0.6333, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": [0.111, 0, 0.31, 0, 0.111, 0, 0.25, 58.15]}, {"time": 0.3333, "x": 54.1, "y": 29.08, "curve": [0.401, 209.62, 0.467, 276.96, 0.401, 5.57, 0.475, -88.86]}, {"time": 0.5333, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": [0.111, 0, 0.31, 0, 0.111, 0, 0.315, 0]}, {"time": 0.3333, "x": -53.64, "y": 36.2, "curve": [0.381, -164.57, 0.476, -313.12, 0.41, 187.62, 0.493, -267.19]}, {"time": 0.5667, "x": -368.12, "y": -387.78}]}, "blot_drop5": {"rotate": [{"curve": [0.111, 0, 0.222, 103.14]}, {"time": 0.3333, "value": 103.14, "curve": [0.422, 103.14, 0.585, 97.56]}, {"time": 0.6, "value": 97.56}], "translate": [{"curve": [0.111, 0, 0.222, 6.98, 0.111, 0, 0.31, 0]}, {"time": 0.3333, "x": 35.48, "y": -17.82, "curve": [0.422, 58.13, 0.533, 37.99, 0.411, -78.13, 0.55, -373.65]}, {"time": 0.6333, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": [0.111, 0, 0.298, 21.09, 0.111, 0, 0.318, 0]}, {"time": 0.3333, "x": 10.55, "y": -19.73, "curve": [0.39, -6.01, 0.437, -21.01, 0.38, -79.55, 0.427, -201.42]}, {"time": 0.5, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{"curve": [0.111, 0, 0.263, 0]}, {"time": 0.3333, "value": -75.4, "curve": [0.376, -120.98, 0.46, -263.98]}, {"time": 0.5667, "value": -261.68}], "translate": [{"curve": [0.111, 0, 0.326, 0, 0.111, 0, 0.328, 0]}, {"time": 0.3333, "x": 9.31, "y": 29.92, "curve": [0.425, 118.46, 0.578, 297.6, 0.389, 351.23, 0.547, 364.92]}, {"time": 0.7, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667, "curve": [0.611, 1, 0.656, 0.4, 0.611, 1, 0.656, 0.4]}, {"time": 0.7, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": [0.122, 0, 0.326, -53.64, 0.122, 0, 0.297, 83.69]}, {"time": 0.3667, "x": -53.64, "y": 83.69, "curve": [0.467, -154.19, 0.65, -338.78, 0.538, 242.44, 0.692, -264.59]}, {"time": 0.8, "x": -378.34, "y": -598.88}]}}, "attachments": {"default": {"body": {"Simpy_body": {"deform": [{"curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "offset": 4, "vertices": [0.15664, 4.3322, 0.10908, 5.58915, 0.08448, 3.47, 0.08465, 3.75845, -3.56599, -1.94425, -2.4835, -2.5083, -1.92504, -1.55729, -6.18879, 2.1845, -4.31011, 2.81833, -3.34089, 1.74974, -9.41726, 8.03886, -6.55855, 10.37128, -5.08379, 6.43896, -5.08332, 6.97421, -13.08364, 5.77024, -10.14141, 3.5824, 0.09879, 2.53205, 0.07658, 1.57202, 12.2119, 7.24183, 8.50484, 9.34292, 6.59224, 5.80054, 6.59242, 6.2827, 5.51512, 7.38515, 4.27487, 4.58505, 4.27512, 4.96618, 6.27318, 2.65656, 4.86246, 1.6493, 16.17928, -0.68198, 8.73393, -0.54621, 8.73417, -0.5916, 1.06834, 4.05636, 0.57671, 3.24906, -9.45325, -0.22548, -5.1031, -0.18057, -0.59408, -5.97609, -0.41374, -7.70978, -0.32082, -4.78666, -0.3205, -5.18452, -0.52276, -1.21822, -0.36407, -1.57164, -0.28223, -0.97576, -0.28217, -1.05686, 2.6966, 3.14731, 1.87801, 4.06044, 1.45567, 2.52092, 0, 0, 0, 0, 0, 0, 0, 0, 1.93571, 4.69773, 1.04488, 3.76277, -2.84473, 7.56866, -1.53573, 6.06232, -1.53552, 6.56624, -8.28485, 7.15865, -4.47243, 5.73392, -4.47215, 6.21055, -12.14642, -2.58744, -8.45923, -3.33808, -6.55694, -2.07246, -6.55688, -2.24472, -2.26642, 1.36104, -1.57843, 1.75605, -1.22314, 1.18084, -1.52444, -5.21324, -1.06168, -6.72568, -0.82282, -4.52274, -2.79156, -6.69987, -1.94416, -8.64357, -1.50706, -5.36639, -1.50675, -5.81245, -0.99787, -0.08176, -0.53859, -0.0709, -5.73673, 3.08789, -3.09673, 2.67893, -12.12626, 1.68553, -6.54592, 1.46232, -18.34507, 1.13311, -12.77621, 1.46207, -9.90314, 0.90766, -9.90258, 0.98313, -2.95207, 1.05836, -1.59362, 0.84773, -1.5935, 0.9182, 0, 0, 0, 0, -2.90064, 3.3806, -2.2483, 2.2733, -14.16252, 4.81371, -9.86333, 6.21034, -7.6453, 3.85567, -7.6452, 4.17617, -3.64056, 7.43002, -2.82179, 4.99636, 1.70871, 3.62983, 1.3245, 2.4409, 8.98733, 4.60558, 6.96647, 3.09703, -0.28743, 10.21686, -0.20018, 13.18109, -0.15522, 8.18346, -0.15495, 8.8637, -6.89185, 7.37347, -4.79976, 9.51275, -3.72018, 6.3969, -9.68589, 7.68951, -6.74564, 9.92054, -5.22879, 6.15913, -5.22845, 6.67112, -6.22209, 0.99199, -4.82269, 0.66705]}]}}, "body_outline": {"Simpy_body_outline": {"deform": [{"curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "vertices": [4.57603, 0.39926, 3.5469, 0.24785, 3.54716, 0.26846, 2.99164, 8.95725, 4.29563, 6.94288, 2.3188, 5.56109, -4.11284, 2.59885, -3.18801, 1.61348, -10.78042, 7.38092, -15.47935, 5.72105, -8.35614, 4.58243, -8.35593, 4.96334, -6.99477, 16.8008, -10.04362, 13.02254, -5.42184, 10.43075, -5.42156, 11.29779, -9.9131, 9.06579, -7.68388, 5.62848, 4.22037, 4.73951, 3.27123, 2.9425, 14.91701, 6.99142, 21.41899, 5.41911, 11.56242, 4.3406, 11.56264, 4.70141, 11.36962, -0.49802, 8.81277, -0.30923, 7.31335, 0.58384, 10.50108, 0.45245, 5.66872, 0.36244, 16.75756, -2.86168, 9.04608, -2.2921, 9.04628, -2.48262, 9.49349, 4.0492, 5.12476, 3.24332, 0, 0, 0, 0, 2.32003, -3.26597, 1.25237, -2.61595, 0.15605, -10.27676, 0.22407, -7.96577, 0.12083, -6.38035, 0.12106, -6.91069, 0.38073, -3.6335, 0.20548, -2.91033, 0, 0, 0, 0, 0, 0, 1.98613, -1.46835, 2.85184, -1.13816, 1.53947, -0.91163, 0.47169, 3.2788, 0.25459, 2.62624, -0.98847, 8.40723, -0.53369, 6.73401, -5.23194, 5.55504, -2.8241, 4.81935, -5.76336, -2.2842, -3.11107, -1.98163, 0, 0, 0, 0, 0, 0, 0, 0, 4.85999, -4.45088, 2.62366, -3.86134, 0.89721, -10.44014, 1.2883, -8.09243, 0.69536, -6.4818, 0.69569, -7.02057, -8.20068, -10.15454, -11.77517, -7.87103, -6.35652, -6.30447, -6.35635, -6.82851, -1.33948, -0.86274, -0.72304, -0.74846, -8.43608, 2.08917, -4.55386, 1.8125, -13.11085, -4.86821, -7.07754, -3.89929, -7.07729, -4.22339, -5.99672, -1.51926, -3.2371, -1.31802, -0.54255, -0.01093, -0.42051, -0.00735, -4.764, 0.99826, -3.69268, 0.61974, -3.69249, 0.67127, -11.06745, 5.09184, -8.57863, 3.16124, -8.57841, 3.42402, -7.5124, 3.28762, -5.82288, 2.21077, -1.68699, 8.9051, -1.30736, 5.98827, 0.02347, 18.40828, 0.0337, 14.26853, 0.01813, 11.42875, 0.01842, 12.37876, -0.96934, 14.283, -1.39186, 11.07096, -0.75114, 9.60469, -4.0301, 13.96837, -5.78671, 10.82709, -3.12363, 9.39311, -5.11665, 6.51691, -3.96587, 4.38232, 0.31756, 0.63468, 0.2462, 0.4268]}]}}}}}, "t1_IDLE": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "body": {"attachment": [{"name": "Simpy_body"}]}, "body_outline": {"attachment": [{"name": "Simpy_body_outline"}]}, "eyebrows_l": {"attachment": [{"name": "Simpy_eyebrows_l"}]}, "eyebrows_r": {"attachment": [{"name": "Simpy_eyebrows_r"}]}, "eyelid_l_r": {"attachment": [{"name": "Simpy_eyelid_l_r"}]}, "eyelid_l_R": {"attachment": [{"name": "Simpy_eyelid_l_l"}]}, "eyelid_u_r": {"attachment": [{}, {"time": 0.1, "name": "Simpy_eyelid_u_r"}, {"time": 0.3333}, {"time": 1.5333, "name": "Simpy_eyelid_u_r"}, {"time": 1.7667}, {"time": 2.2333, "name": "Simpy_eyelid_u_r"}, {"time": 2.4667}, {"time": 3.3, "name": "Simpy_eyelid_u_r"}, {"time": 3.5333}]}, "eyelid_u_R": {"attachment": [{}, {"time": 0.1, "name": "Simpy_eyelid_u_l"}, {"time": 0.3333}, {"time": 1.5333, "name": "Simpy_eyelid_u_l"}, {"time": 1.7667}, {"time": 2.2333, "name": "Simpy_eyelid_u_l"}, {"time": 2.4667}, {"time": 3.3, "name": "Simpy_eyelid_u_l"}, {"time": 3.5333}]}, "eye_L": {"attachment": [{"name": "Simpy_eye_r"}]}, "eye_R": {"attachment": [{"name": "Simpy_eye_l"}]}, "mouth_1": {"attachment": [{"name": "Simpy_mouth_1"}]}, "mouth_2": {"attachment": [{}]}, "mouth_base": {"attachment": [{"name": "Simpy_mouth_base"}]}, "pupil_L": {"attachment": [{"name": "Simpy_pupil_r"}]}, "pupil_R": {"attachment": [{"name": "Simpy_pupil_l"}]}, "teeth": {"attachment": [{"name": "Simpy_teeth"}]}}, "bones": {"EyeLiid_R_U": {"translate": [{"x": -3.48, "y": 26.04, "curve": "stepped"}, {"time": 0.0667, "x": -3.48, "y": 26.04, "curve": [0.115, -3.48, 0.118, 0.45, 0.115, 26.04, 0.118, -3.34]}, {"time": 0.1667, "x": 0.45, "y": -3.34, "curve": "stepped"}, {"time": 0.2333, "x": 0.45, "y": -3.34, "curve": [0.278, 0.45, 0.322, -3.48, 0.278, -3.34, 0.322, 26.04]}, {"time": 0.3667, "x": -3.48, "y": 26.04, "curve": "stepped"}, {"time": 1.5, "x": -3.48, "y": 26.04, "curve": [1.549, -3.48, 1.551, 0.45, 1.549, 26.04, 1.551, -3.34]}, {"time": 1.6, "x": 0.45, "y": -3.34, "curve": "stepped"}, {"time": 1.6667, "x": 0.45, "y": -3.34, "curve": [1.711, 0.45, 1.756, -3.48, 1.711, -3.34, 1.756, 26.04]}, {"time": 1.8, "x": -3.48, "y": 26.04, "curve": "stepped"}, {"time": 2.2, "x": -3.48, "y": 26.04, "curve": [2.249, -3.48, 2.251, 0.45, 2.249, 26.04, 2.251, -3.34]}, {"time": 2.3, "x": 0.45, "y": -3.34, "curve": "stepped"}, {"time": 2.3667, "x": 0.45, "y": -3.34, "curve": [2.411, 0.45, 2.456, -3.48, 2.411, -3.34, 2.456, 26.04]}, {"time": 2.5, "x": -3.48, "y": 26.04, "curve": "stepped"}, {"time": 3.2667, "x": -3.48, "y": 26.04, "curve": [3.315, -3.48, 3.318, 0.45, 3.315, 26.04, 3.318, -3.34]}, {"time": 3.3667, "x": 0.45, "y": -3.34, "curve": "stepped"}, {"time": 3.4333, "x": 0.45, "y": -3.34, "curve": [3.478, 0.45, 3.522, -3.48, 3.478, -3.34, 3.522, 26.04]}, {"time": 3.5667, "x": -3.48, "y": 26.04}]}, "Eyelid_R_B": {"translate": [{"x": 0.59, "y": -7.66, "curve": "stepped"}, {"time": 0.0667, "x": 0.59, "y": -7.66, "curve": [0.115, 0.59, 0.118, -0.15, 0.115, -7.66, 0.118, 1.93]}, {"time": 0.1667, "x": -0.15, "y": 1.93, "curve": "stepped"}, {"time": 0.2333, "x": -0.15, "y": 1.93, "curve": [0.278, -0.15, 0.322, 0.59, 0.278, 1.93, 0.322, -7.66]}, {"time": 0.3667, "x": 0.59, "y": -7.66, "curve": "stepped"}, {"time": 1.5, "x": 0.59, "y": -7.66, "curve": [1.549, 0.59, 1.551, -0.15, 1.549, -7.66, 1.551, 1.93]}, {"time": 1.6, "x": -0.15, "y": 1.93, "curve": "stepped"}, {"time": 1.6667, "x": -0.15, "y": 1.93, "curve": [1.711, -0.15, 1.756, 0.59, 1.711, 1.93, 1.756, -7.66]}, {"time": 1.8, "x": 0.59, "y": -7.66, "curve": "stepped"}, {"time": 2.2, "x": 0.59, "y": -7.66, "curve": [2.249, 0.59, 2.251, -0.15, 2.249, -7.66, 2.251, 1.93]}, {"time": 2.3, "x": -0.15, "y": 1.93, "curve": "stepped"}, {"time": 2.3667, "x": -0.15, "y": 1.93, "curve": [2.411, -0.15, 2.456, 0.59, 2.411, 1.93, 2.456, -7.66]}, {"time": 2.5, "x": 0.59, "y": -7.66, "curve": "stepped"}, {"time": 3.2667, "x": 0.59, "y": -7.66, "curve": [3.315, 0.59, 3.318, -0.15, 3.315, -7.66, 3.318, 1.93]}, {"time": 3.3667, "x": -0.15, "y": 1.93, "curve": "stepped"}, {"time": 3.4333, "x": -0.15, "y": 1.93, "curve": [3.478, -0.15, 3.522, 0.59, 3.478, 1.93, 3.522, -7.66]}, {"time": 3.5667, "x": 0.59, "y": -7.66}]}, "Eyelid_L_U": {"translate": [{"x": -2.09, "y": 24.95, "curve": "stepped"}, {"time": 0.0667, "x": -2.09, "y": 24.95, "curve": [0.115, -2.09, 0.118, 0.29, 0.115, 24.95, 0.118, -3.42]}, {"time": 0.1667, "x": 0.29, "y": -3.42, "curve": "stepped"}, {"time": 0.2333, "x": 0.29, "y": -3.42, "curve": [0.278, 0.29, 0.322, -2.09, 0.278, -3.42, 0.322, 24.95]}, {"time": 0.3667, "x": -2.09, "y": 24.95, "curve": "stepped"}, {"time": 1.5, "x": -2.09, "y": 24.95, "curve": [1.549, -2.09, 1.551, 0.29, 1.549, 24.95, 1.551, -3.42]}, {"time": 1.6, "x": 0.29, "y": -3.42, "curve": "stepped"}, {"time": 1.6667, "x": 0.29, "y": -3.42, "curve": [1.711, 0.29, 1.756, -2.09, 1.711, -3.42, 1.756, 24.95]}, {"time": 1.8, "x": -2.09, "y": 24.95, "curve": "stepped"}, {"time": 2.2, "x": -2.09, "y": 24.95, "curve": [2.249, -2.09, 2.251, 0.29, 2.249, 24.95, 2.251, -3.42]}, {"time": 2.3, "x": 0.29, "y": -3.42, "curve": "stepped"}, {"time": 2.3667, "x": 0.29, "y": -3.42, "curve": [2.411, 0.29, 2.456, -2.09, 2.411, -3.42, 2.456, 24.95]}, {"time": 2.5, "x": -2.09, "y": 24.95, "curve": "stepped"}, {"time": 3.2667, "x": -2.09, "y": 24.95, "curve": [3.315, -2.09, 3.318, 0.29, 3.315, 24.95, 3.318, -3.42]}, {"time": 3.3667, "x": 0.29, "y": -3.42, "curve": "stepped"}, {"time": 3.4333, "x": 0.29, "y": -3.42, "curve": [3.478, 0.29, 3.522, -2.09, 3.478, -3.42, 3.522, 24.95]}, {"time": 3.5667, "x": -2.09, "y": 24.95}]}, "Eyelid_L_B": {"translate": [{"x": 0.18, "y": -6.64, "curve": "stepped"}, {"time": 0.0667, "x": 0.18, "y": -6.64, "curve": [0.115, 0.18, 0.118, -0.12, 0.115, -6.64, 0.118, 4.65]}, {"time": 0.1667, "x": -0.12, "y": 4.65, "curve": "stepped"}, {"time": 0.2333, "x": -0.12, "y": 4.65, "curve": [0.278, -0.12, 0.322, 0.18, 0.278, 4.65, 0.322, -6.64]}, {"time": 0.3667, "x": 0.18, "y": -6.64, "curve": "stepped"}, {"time": 1.5, "x": 0.18, "y": -6.64, "curve": [1.549, 0.18, 1.551, -0.12, 1.549, -6.64, 1.551, 4.65]}, {"time": 1.6, "x": -0.12, "y": 4.65, "curve": "stepped"}, {"time": 1.6667, "x": -0.12, "y": 4.65, "curve": [1.711, -0.12, 1.756, 0.18, 1.711, 4.65, 1.756, -6.64]}, {"time": 1.8, "x": 0.18, "y": -6.64, "curve": "stepped"}, {"time": 2.2, "x": 0.18, "y": -6.64, "curve": [2.249, 0.18, 2.251, -0.12, 2.249, -6.64, 2.251, 4.65]}, {"time": 2.3, "x": -0.12, "y": 4.65, "curve": "stepped"}, {"time": 2.3667, "x": -0.12, "y": 4.65, "curve": [2.411, -0.12, 2.456, 0.18, 2.411, 4.65, 2.456, -6.64]}, {"time": 2.5, "x": 0.18, "y": -6.64, "curve": "stepped"}, {"time": 3.2667, "x": 0.18, "y": -6.64, "curve": [3.315, 0.18, 3.318, -0.12, 3.315, -6.64, 3.318, 4.65]}, {"time": 3.3667, "x": -0.12, "y": 4.65, "curve": "stepped"}, {"time": 3.4333, "x": -0.12, "y": 4.65, "curve": [3.478, -0.12, 3.522, 0.18, 3.478, 4.65, 3.522, -6.64]}, {"time": 3.5667, "x": 0.18, "y": -6.64}]}, "eye_L": {"translate": [{}], "scale": [{"x": 1.239, "y": 1.098, "curve": "stepped"}, {"time": 0.1667, "x": 1.239, "y": 1.098, "curve": [0.227, 1.239, 0.306, 1, 0.227, 1.098, 0.306, 1]}, {"time": 0.3667, "curve": "stepped"}, {"time": 2.2667, "curve": [2.333, 1, 2.4, 1.239, 2.333, 1, 2.4, 1.098]}, {"time": 2.4667, "x": 1.239, "y": 1.098, "curve": "stepped"}, {"time": 3.3333, "x": 1.239, "y": 1.098, "curve": [3.389, 1.239, 3.444, 1.077, 3.389, 1.098, 3.444, 1]}, {"time": 3.5, "x": 1.077, "curve": "stepped"}, {"time": 4.2, "x": 1.077, "curve": [4.256, 1.077, 4.311, 1.239, 4.256, 1, 4.311, 1.098]}, {"time": 4.3667, "x": 1.239, "y": 1.098}]}, "eye_R": {"translate": [{}], "scale": [{"x": 0.863, "y": 0.953, "curve": "stepped"}, {"time": 0.1667, "x": 0.863, "y": 0.953, "curve": [0.227, 0.863, 0.306, 1, 0.227, 0.953, 0.306, 1]}, {"time": 0.3667, "curve": "stepped"}, {"time": 2.2667, "curve": [2.333, 1, 2.4, 0.863, 2.333, 1, 2.4, 0.953]}, {"time": 2.4667, "x": 0.863, "y": 0.953, "curve": "stepped"}, {"time": 3.3333, "x": 0.863, "y": 0.953, "curve": [3.389, 0.863, 3.444, 0.954, 3.389, 0.953, 3.444, 0.954]}, {"time": 3.5, "x": 0.954, "y": 0.954, "curve": "stepped"}, {"time": 4.2, "x": 0.954, "y": 0.954, "curve": [4.256, 0.954, 4.311, 0.863, 4.256, 0.954, 4.311, 0.953]}, {"time": 4.3667, "x": 0.863, "y": 0.953}]}, "Face": {"translate": [{"x": -15.18, "curve": "stepped"}, {"time": 0.0333, "x": -15.18, "curve": [0.078, -15.18, 0.122, -17.18, 0.078, 0, 0.122, 0]}, {"time": 0.1667, "x": -17.18, "curve": [0.233, -17.18, 0.306, 7.35, 0.233, 0, 0.306, 0]}, {"time": 0.3667, "x": 7.35, "curve": [0.422, 7.35, 0.478, 5.73, 0.422, 0, 0.478, 0]}, {"time": 0.5333, "x": 5.73, "curve": "stepped"}, {"time": 2.1333, "x": 5.73, "curve": [2.178, 5.73, 2.222, 7.26, 2.178, 0, 2.222, 0]}, {"time": 2.2667, "x": 7.26, "curve": [2.333, 7.26, 2.4, -16.78, 2.333, 0, 2.4, 0]}, {"time": 2.4667, "x": -16.78, "curve": [2.522, -16.78, 2.578, -15.18, 2.522, 0, 2.578, 0]}, {"time": 2.6333, "x": -15.18, "curve": "stepped"}, {"time": 3.2, "x": -15.18, "curve": [3.244, -15.18, 3.289, -18.64, 3.244, 0, 3.289, 0]}, {"time": 3.3333, "x": -18.64, "curve": [3.389, -18.64, 3.444, -4.11, 3.389, 0, 3.444, 0]}, {"time": 3.5, "x": -4.11, "curve": [3.556, -4.11, 3.611, -7.5, 3.556, 0, 3.611, 0]}, {"time": 3.6667, "x": -7.5, "curve": "stepped"}, {"time": 4.0333, "x": -7.5, "curve": [4.089, -7.5, 4.144, -5.81, 4.089, 0, 4.144, 0]}, {"time": 4.2, "x": -5.81, "curve": [4.256, -5.81, 4.311, -17.2, 4.256, 0, 4.311, 0]}, {"time": 4.3667, "x": -17.2, "curve": [4.422, -17.2, 4.478, -15.18, 4.422, 0, 4.478, 0]}, {"time": 4.5333, "x": -15.18}]}, "cntr": {"rotate": [{"value": -9.71, "curve": [0.044, -9.71, 0.089, -12.02]}, {"time": 0.1333, "value": -12.02, "curve": [0.2, -12.02, 0.267, 9.78]}, {"time": 0.3333, "value": 9.78, "curve": [0.389, 9.78, 0.444, 7.59]}, {"time": 0.5, "value": 7.59, "curve": "stepped"}, {"time": 2.1, "value": 7.59, "curve": [2.144, 7.59, 2.189, 9.5]}, {"time": 2.2333, "value": 9.5, "curve": [2.299, 9.5, 2.367, -12.61]}, {"time": 2.4333, "value": -12.61, "curve": [2.489, -12.61, 2.544, -9.71]}, {"time": 2.6, "value": -9.71, "curve": "stepped"}, {"time": 3.1667, "value": -9.71, "curve": [3.212, -9.71, 3.256, -11.18]}, {"time": 3.3, "value": -11.18, "curve": [3.356, -11.18, 3.411, 1.02]}, {"time": 3.4667, "value": 1.02, "curve": [3.522, 1.02, 3.578, -0.36]}, {"time": 3.6333, "value": -0.36, "curve": "stepped"}, {"time": 4, "value": -0.36, "curve": [4.056, -0.36, 4.111, 1.59]}, {"time": 4.1667, "value": 1.59, "curve": [4.222, 1.59, 4.278, -11.8]}, {"time": 4.3333, "value": -11.8, "curve": [4.389, -11.8, 4.444, -9.71]}, {"time": 4.5, "value": -9.71}], "translate": [{"y": 2.43, "curve": [0.191, 0, 0.38, 0, 0.172, 5.98, 0.38, 10.22]}, {"time": 0.5667, "y": 10.22, "curve": [1.019, 0, 1.448, 0, 1.019, 10.22, 1.448, -8.73]}, {"time": 1.9, "y": -8.73, "curve": [2.352, 0, 2.815, 0, 2.352, -8.73, 2.815, 10.22]}, {"time": 3.2667, "y": 10.22, "curve": [3.719, 0, 4.181, 0, 3.719, 10.22, 4.181, -8.73]}, {"time": 4.6333, "y": -8.73, "curve": [4.898, 0, 5.167, 0, 4.898, -8.73, 5.215, -2.08]}, {"time": 5.4333, "y": 2.43}], "scale": [{"x": 0.95, "y": 1.05, "curve": [0.452, 0.95, 0.915, 1.05, 0.452, 1.05, 0.915, 0.95]}, {"time": 1.3667, "x": 1.05, "y": 0.95, "curve": [1.819, 1.05, 2.248, 0.95, 1.819, 0.95, 2.248, 1.05]}, {"time": 2.7, "x": 0.95, "y": 1.05, "curve": [3.152, 0.95, 3.615, 1.05, 3.152, 1.05, 3.615, 0.95]}, {"time": 4.0667, "x": 1.05, "y": 0.95, "curve": [4.519, 1.05, 4.981, 0.95, 4.519, 0.95, 4.981, 1.05]}, {"time": 5.4333, "x": 0.95, "y": 1.05}]}, "Eyes_scale": {"scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.115, 1, 0.118, 1.1, 0.115, 1, 0.118, 0.8]}, {"time": 0.1667, "x": 1.1, "y": 0.8, "curve": "stepped"}, {"time": 0.2333, "x": 1.1, "y": 0.8, "curve": [0.288, 1.1, 0.312, 0.95, 0.288, 0.8, 0.312, 1.05]}, {"time": 0.3667, "x": 0.95, "y": 1.05, "curve": [0.422, 0.95, 0.478, 0.983, 0.422, 1.05, 0.478, 1.017]}, {"time": 0.5333, "curve": "stepped"}, {"time": 1.5, "curve": [1.549, 1, 1.551, 1.1, 1.549, 1, 1.551, 0.8]}, {"time": 1.6, "x": 1.1, "y": 0.8, "curve": "stepped"}, {"time": 1.6667, "x": 1.1, "y": 0.8, "curve": [1.721, 1.1, 1.746, 0.95, 1.721, 0.8, 1.746, 1.05]}, {"time": 1.8, "x": 0.95, "y": 1.05, "curve": [1.856, 0.95, 1.911, 0.983, 1.856, 1.05, 1.911, 1.017]}, {"time": 1.9667, "curve": "stepped"}, {"time": 2.2, "curve": [2.249, 1, 2.251, 1.1, 2.249, 1, 2.251, 0.8]}, {"time": 2.3, "x": 1.1, "y": 0.8, "curve": "stepped"}, {"time": 2.3667, "x": 1.1, "y": 0.8, "curve": [2.421, 1.1, 2.446, 0.95, 2.421, 0.8, 2.446, 1.05]}, {"time": 2.5, "x": 0.95, "y": 1.05, "curve": [2.556, 0.95, 2.611, 0.983, 2.556, 1.05, 2.611, 1.017]}, {"time": 2.6667, "curve": "stepped"}, {"time": 3.2667, "curve": [3.315, 1, 3.318, 1.1, 3.315, 1, 3.318, 0.8]}, {"time": 3.3667, "x": 1.1, "y": 0.8, "curve": "stepped"}, {"time": 3.4333, "x": 1.1, "y": 0.8, "curve": [3.488, 1.1, 3.512, 0.95, 3.488, 0.8, 3.512, 1.05]}, {"time": 3.5667, "x": 0.95, "y": 1.05, "curve": [3.622, 0.95, 3.678, 0.983, 3.622, 1.05, 3.678, 1.017]}, {"time": 3.7333}]}, "eyebrows_l": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.1, 0, 0.133, -6.86]}, {"time": 0.1667, "value": -6.86, "curve": "stepped"}, {"time": 0.2333, "value": -6.86, "curve": [0.278, -6.86, 0.322, 0]}, {"time": 0.3667, "curve": "stepped"}, {"time": 1.5, "curve": [1.533, 0, 1.567, -6.86]}, {"time": 1.6, "value": -6.86, "curve": "stepped"}, {"time": 1.6667, "value": -6.86, "curve": [1.711, -6.86, 1.756, 0]}, {"time": 1.8, "curve": "stepped"}, {"time": 2.2, "curve": [2.233, 0, 2.267, -6.86]}, {"time": 2.3, "value": -6.86, "curve": "stepped"}, {"time": 2.3667, "value": -6.86, "curve": [2.411, -6.86, 2.456, 0]}, {"time": 2.5, "curve": "stepped"}, {"time": 3.2667, "curve": [3.3, 0, 3.333, -6.86]}, {"time": 3.3667, "value": -6.86, "curve": "stepped"}, {"time": 3.4333, "value": -6.86, "curve": [3.478, -6.86, 3.522, 0]}, {"time": 3.5667}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.1, 0, 0.133, 0, 0.1, 0, 0.133, -4.03]}, {"time": 0.1667, "y": -4.03, "curve": "stepped"}, {"time": 0.2333, "y": -4.03, "curve": [0.256, 0, 0.322, 0, 0.256, -4.03, 0.322, 0]}, {"time": 0.3667, "curve": "stepped"}, {"time": 1.5, "curve": [1.533, 0, 1.567, 0, 1.533, 0, 1.567, -4.03]}, {"time": 1.6, "y": -4.03, "curve": "stepped"}, {"time": 1.6667, "y": -4.03, "curve": [1.689, 0, 1.756, 0, 1.689, -4.03, 1.756, 0]}, {"time": 1.8, "curve": "stepped"}, {"time": 2.2, "curve": [2.233, 0, 2.267, 0, 2.233, 0, 2.267, -4.03]}, {"time": 2.3, "y": -4.03, "curve": "stepped"}, {"time": 2.3667, "y": -4.03, "curve": [2.389, 0, 2.456, 0, 2.389, -4.03, 2.456, 0]}, {"time": 2.5, "curve": "stepped"}, {"time": 3.2667, "curve": [3.3, 0, 3.333, 0, 3.3, 0, 3.333, -4.03]}, {"time": 3.3667, "y": -4.03, "curve": "stepped"}, {"time": 3.4333, "y": -4.03, "curve": [3.456, 0, 3.522, 0, 3.456, -4.03, 3.522, 0]}, {"time": 3.5667}], "scale": [{}]}, "eyebrows_r": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.1, 0, 0.133, 5.35]}, {"time": 0.1667, "value": 5.35, "curve": "stepped"}, {"time": 0.2333, "value": 5.35, "curve": [0.278, 5.35, 0.322, 0]}, {"time": 0.3667, "curve": "stepped"}, {"time": 1.5, "curve": [1.533, 0, 1.567, 5.35]}, {"time": 1.6, "value": 5.35, "curve": "stepped"}, {"time": 1.6667, "value": 5.35, "curve": [1.711, 5.35, 1.756, 0]}, {"time": 1.8, "curve": "stepped"}, {"time": 2.2, "curve": [2.233, 0, 2.267, 5.35]}, {"time": 2.3, "value": 5.35, "curve": "stepped"}, {"time": 2.3667, "value": 5.35, "curve": [2.411, 5.35, 2.456, 0]}, {"time": 2.5, "curve": "stepped"}, {"time": 3.2667, "curve": [3.3, 0, 3.333, 5.35]}, {"time": 3.3667, "value": 5.35, "curve": "stepped"}, {"time": 3.4333, "value": 5.35, "curve": [3.478, 5.35, 3.522, 0]}, {"time": 3.5667}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.1, 0, 0.133, 0, 0.1, 0, 0.133, -4.03]}, {"time": 0.1667, "y": -4.03, "curve": "stepped"}, {"time": 0.2333, "y": -4.03, "curve": [0.256, 0, 0.322, 0, 0.256, -4.03, 0.322, 0]}, {"time": 0.3667, "curve": "stepped"}, {"time": 1.5, "curve": [1.533, 0, 1.567, 0, 1.533, 0, 1.567, -4.03]}, {"time": 1.6, "y": -4.03, "curve": "stepped"}, {"time": 1.6667, "y": -4.03, "curve": [1.689, 0, 1.756, 0, 1.689, -4.03, 1.756, 0]}, {"time": 1.8, "curve": "stepped"}, {"time": 2.2, "curve": [2.233, 0, 2.267, 0, 2.233, 0, 2.267, -4.03]}, {"time": 2.3, "y": -4.03, "curve": "stepped"}, {"time": 2.3667, "y": -4.03, "curve": [2.389, 0, 2.456, 0, 2.389, -4.03, 2.456, 0]}, {"time": 2.5, "curve": "stepped"}, {"time": 3.2667, "curve": [3.3, 0, 3.333, 0, 3.3, 0, 3.333, -4.03]}, {"time": 3.3667, "y": -4.03, "curve": "stepped"}, {"time": 3.4333, "y": -4.03, "curve": [3.456, 0, 3.522, 0, 3.456, -4.03, 3.522, 0]}, {"time": 3.5667}], "scale": [{}]}, "pupil_R": {"translate": [{"x": -16.39, "y": 2.66, "curve": "stepped"}, {"time": 0.1667, "x": -16.39, "y": 2.66, "curve": [0.233, -16.39, 0.3, 8.92, 0.233, 2.66, 0.3, 6.26]}, {"time": 0.3667, "x": 8.92, "y": 6.26, "curve": "stepped"}, {"time": 0.9, "x": 8.92, "y": 6.26, "curve": [0.933, 8.92, 0.967, 9.07, 0.933, 6.26, 0.967, -1.14]}, {"time": 1, "x": 9.07, "y": -1.14, "curve": "stepped"}, {"time": 1.2, "x": 9.07, "y": -1.14, "curve": [1.233, 9.07, 1.267, 8.41, 1.233, -1.14, 1.267, 1.03]}, {"time": 1.3, "x": 8.41, "y": 1.03, "curve": "stepped"}, {"time": 1.7, "x": 8.41, "y": 1.03, "curve": [1.744, 8.41, 1.789, 1.98, 1.744, 1.03, 1.789, 10.2]}, {"time": 1.8333, "x": 1.98, "y": 10.2, "curve": "stepped"}, {"time": 2.2667, "x": 1.98, "y": 10.2, "curve": [2.322, 1.98, 2.378, -16.47, 2.322, 10.2, 2.378, -1.82]}, {"time": 2.4333, "x": -16.47, "y": -1.82, "curve": "stepped"}, {"time": 3, "x": -16.47, "y": -1.82, "curve": [3.056, -16.47, 3.111, -18.05, 3.056, -1.82, 3.111, 2.92]}, {"time": 3.1667, "x": -18.05, "y": 2.92, "curve": "stepped"}, {"time": 3.4, "x": -18.05, "y": 2.92, "curve": [3.444, -18.05, 3.489, -3.12, 3.444, 2.92, 3.489, -0.09]}, {"time": 3.5333, "x": -3.12, "y": -0.09, "curve": "stepped"}, {"time": 4.2, "x": -3.12, "y": -0.09, "curve": [4.244, -3.12, 4.289, -15.98, 4.244, -0.09, 4.289, 7.38]}, {"time": 4.3333, "x": -15.98, "y": 7.38, "curve": "stepped"}, {"time": 4.7, "x": -15.98, "y": 7.38, "curve": [4.744, -15.98, 4.789, -17.46, 4.744, 7.38, 4.789, 2.35]}, {"time": 4.8333, "x": -17.46, "y": 2.35, "curve": "stepped"}, {"time": 5.2667, "x": -17.46, "y": 2.35, "curve": [5.322, -17.46, 5.378, -16.39, 5.322, 2.35, 5.378, 2.66]}, {"time": 5.4333, "x": -16.39, "y": 2.66}]}, "pupil_L": {"translate": [{"x": -5.58, "y": 1.64, "curve": "stepped"}, {"time": 0.1667, "x": -5.58, "y": 1.64, "curve": [0.233, -5.58, 0.3, 12.18, 0.233, 1.64, 0.3, 8.53]}, {"time": 0.3667, "x": 12.18, "y": 8.53, "curve": "stepped"}, {"time": 0.9, "x": 12.18, "y": 8.53, "curve": [0.933, 12.18, 0.967, 13.33, 0.933, 8.53, 0.967, 1.21]}, {"time": 1, "x": 13.33, "y": 1.21, "curve": "stepped"}, {"time": 1.2, "x": 13.33, "y": 1.21, "curve": [1.233, 13.33, 1.267, 12.39, 1.233, 1.21, 1.267, 3.28]}, {"time": 1.3, "x": 12.39, "y": 3.28, "curve": "stepped"}, {"time": 1.7, "x": 12.39, "y": 3.28, "curve": [1.744, 12.39, 1.789, 4.78, 1.744, 3.28, 1.789, 11.49]}, {"time": 1.8333, "x": 4.78, "y": 11.49, "curve": "stepped"}, {"time": 2.2667, "x": 4.78, "y": 11.49, "curve": [2.322, 4.78, 2.378, -5.1, 2.322, 11.49, 2.378, -1.85]}, {"time": 2.4333, "x": -5.1, "y": -1.85, "curve": "stepped"}, {"time": 3, "x": -5.1, "y": -1.85, "curve": [3.056, -5.1, 3.111, -7.3, 3.056, -1.85, 3.111, 2.64]}, {"time": 3.1667, "x": -7.3, "y": 2.64, "curve": "stepped"}, {"time": 3.4, "x": -7.3, "y": 2.64, "curve": [3.444, -7.3, 3.489, 3.32, 3.444, 2.64, 3.489, 1.12]}, {"time": 3.5333, "x": 3.32, "y": 1.12, "curve": "stepped"}, {"time": 4.2, "x": 3.32, "y": 1.12, "curve": [4.244, 3.32, 4.289, -5.81, 4.244, 1.12, 4.289, 6.37]}, {"time": 4.3333, "x": -5.81, "y": 6.37, "curve": "stepped"}, {"time": 4.7, "x": -5.81, "y": 6.37, "curve": [4.744, -5.81, 4.789, -6.59, 4.744, 6.37, 4.789, 1.19]}, {"time": 4.8333, "x": -6.59, "y": 1.19, "curve": "stepped"}, {"time": 5.2667, "x": -6.59, "y": 1.19, "curve": [5.322, -6.59, 5.378, -5.58, 5.322, 1.19, 5.378, 1.64]}, {"time": 5.4333, "x": -5.58, "y": 1.64}]}, "mouth_base": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.122, 0, 0.178, 0, 0.122, -1.14, 0.178, -3.42]}, {"time": 0.2333, "y": -3.42, "curve": [0.315, 0, 0.385, 0, 0.315, -3.42, 0.385, 1.31]}, {"time": 0.4667, "y": 1.31, "curve": [0.589, 0, 0.711, 0, 0.569, 1.31, 0.711, 0]}, {"time": 0.8333, "curve": "stepped"}, {"time": 2.0667, "curve": [2.111, 0, 2.321, 0, 2.111, -0.91, 2.321, 1.31]}, {"time": 2.4, "y": 1.31, "curve": [2.522, 0, 2.644, 0, 2.502, 1.31, 2.644, 0]}, {"time": 2.7667, "curve": "stepped"}, {"time": 4, "curve": [4.056, 0, 4.111, 0, 4.056, -1.14, 4.111, -3.42]}, {"time": 4.1667, "y": -3.42, "curve": [4.26, 0, 4.374, 0, 4.26, -3.42, 4.374, 1.31]}, {"time": 4.4667, "y": 1.31, "curve": [4.589, 0, 4.711, 0, 4.569, 1.31, 4.711, 0]}, {"time": 4.8333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": [0.161, 1, 0.206, 0.887, 0.161, 1, 0.206, 1]}, {"time": 0.2667, "x": 0.887, "curve": [0.322, 0.887, 0.378, 1.062, 0.322, 1, 0.378, 1]}, {"time": 0.4333, "x": 1.062, "curve": [0.544, 1.062, 0.656, 1, 0.544, 1, 0.656, 1]}, {"time": 0.7667, "curve": "stepped"}, {"time": 2.1, "curve": [2.149, 1, 2.315, 1.062, 2.149, 1, 2.315, 1]}, {"time": 2.3667, "x": 1.062, "curve": [2.478, 1.062, 2.589, 1, 2.478, 1, 2.589, 1]}, {"time": 2.7, "curve": "stepped"}, {"time": 4.0333, "curve": [4.064, 1, 4.172, 1.062, 4.064, 1, 4.172, 1]}, {"time": 4.2, "x": 1.062, "curve": [4.277, 1.062, 4.424, 0.887, 4.277, 1, 4.424, 1]}, {"time": 4.4667, "x": 0.887, "curve": [4.517, 0.887, 4.667, 1, 4.517, 1, 4.667, 1]}, {"time": 4.7667}]}, "Mouth_low": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.133, 0, 0.2, 0, 0.133, 0, 0.2, -4.49]}, {"time": 0.2667, "y": -4.49, "curve": [0.356, 0, 0.444, 0, 0.356, -4.49, 0.444, 5.84]}, {"time": 0.5333, "y": 5.84, "curve": [0.689, 0, 0.844, 0, 0.614, 5.84, 0.733, 0]}, {"time": 1, "curve": "stepped"}, {"time": 1.4, "curve": [1.644, 0, 1.889, 0, 1.644, 0, 1.911, 5.84]}, {"time": 2.1333, "y": 5.84, "curve": [2.211, 0, 2.344, 0, 2.211, 5.84, 2.289, -2.56]}, {"time": 2.3667, "y": -2.56, "curve": [2.467, 0, 2.567, 0, 2.467, -2.56, 2.567, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 4, "curve": [4.089, 0, 4.178, 0, 4.089, 0, 4.178, 5.92]}, {"time": 4.2667, "y": 5.92, "curve": "stepped"}, {"time": 4.5667, "y": 5.92, "curve": [4.778, 0, 4.989, 0, 4.778, 5.92, 4.989, 0]}, {"time": 5.2}]}, "Teeth_L": {"translate": [{"curve": "stepped"}, {"time": 0.1, "curve": [0.156, 0, 0.211, -3.11, 0.156, 0, 0.211, 0]}, {"time": 0.2667, "x": -3.11, "curve": [0.333, -3.11, 0.4, 0, 0.333, 0, 0.4, 0]}, {"time": 0.4667, "curve": "stepped"}, {"time": 2.2, "curve": [2.256, 0, 2.311, 3.63, 2.256, 0, 2.311, 0]}, {"time": 2.3667, "x": 3.63, "curve": [2.433, 3.63, 2.5, 0, 2.433, 0, 2.5, 0]}, {"time": 2.5667, "curve": "stepped"}, {"time": 4.1673, "curve": [4.223, 0, 4.278, -3.11, 4.223, 0, 4.278, 0]}, {"time": 4.334, "x": -3.11, "curve": [4.401, -3.11, 4.467, 0, 4.401, 0, 4.467, 0]}, {"time": 4.534}]}, "Teeth_R": {"translate": [{"curve": "stepped"}, {"time": 0.1, "curve": [0.156, 0, 0.211, 3.87, 0.156, 0, 0.211, 0]}, {"time": 0.2667, "x": 3.87, "curve": [0.333, 3.87, 0.4, 0, 0.333, 0, 0.4, 0]}, {"time": 0.4667, "curve": "stepped"}, {"time": 2.2, "curve": [2.256, 0, 2.311, -3.56, 2.256, 0, 2.311, 0]}, {"time": 2.3667, "x": -3.56, "curve": [2.433, -3.56, 2.5, 0, 2.433, 0, 2.5, 0]}, {"time": 2.5667, "curve": "stepped"}, {"time": 4.1673, "curve": [4.223, 0, 4.278, 3.87, 4.223, 0, 4.278, 0]}, {"time": 4.334, "x": 3.87, "curve": [4.401, 3.87, 4.467, 0, 4.401, 0, 4.467, 0]}, {"time": 4.534}]}, "mouth_2": {"translate": [{"y": 2.87}], "scale": [{"x": 2.041, "y": 0.543}]}, "body_top": {"translate": [{}], "scale": [{}]}, "body_bottom": {"translate": [{}], "scale": [{}]}, "Body_R": {"translate": [{}], "scale": [{}]}, "Body_L": {"translate": [{}], "scale": [{}]}, "blot": {"translate": [{}], "scale": [{}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}}, "attachments": {"default": {"body": {"Simpy_body": {"deform": [{}]}}, "body_outline": {"Simpy_body_outline": {"deform": [{}]}}}}}, "t1_IDLE2": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "body": {"attachment": [{"name": "Simpy_body"}]}, "body_outline": {"attachment": [{"name": "Simpy_body_outline"}]}, "eyebrows_l": {"attachment": [{"name": "Simpy_eyebrows_l"}]}, "eyebrows_r": {"attachment": [{"name": "Simpy_eyebrows_r"}]}, "eyelid_l_r": {"attachment": [{}]}, "eyelid_l_R": {"attachment": [{}]}, "eyelid_u_r": {"attachment": [{}]}, "eyelid_u_R": {"attachment": [{}]}, "eye_L": {"attachment": [{"name": "Simpy_eye_r"}]}, "eye_R": {"attachment": [{"name": "Simpy_eye_l"}]}, "mouth_1": {"attachment": [{}]}, "mouth_2": {"attachment": [{"name": "Simpy_mouth_2"}]}, "mouth_base": {"attachment": [{"name": "Simpy_mouth_base"}]}, "pupil_L": {"attachment": [{"name": "Simpy_pupil_r"}]}, "pupil_R": {"attachment": [{"name": "Simpy_pupil_l"}]}, "teeth": {"attachment": [{}]}}, "bones": {"EyeLiid_R_U": {"translate": [{"x": -3.48, "y": 26.04}]}, "Eyelid_R_B": {"translate": [{"x": 0.59, "y": -12.83}]}, "Eyelid_L_U": {"translate": [{"x": -2.09, "y": 24.95}]}, "Eyelid_L_B": {"translate": [{"x": 0.18, "y": -13.45}]}, "eye_L": {"translate": [{}], "scale": [{"x": 1.18, "y": 1.03}]}, "eye_R": {"translate": [{}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.178, 1.007, 0.189, 1.051, 0.178, 0.985, 0.189, 0.875]}, {"time": 0.2, "x": 1.059, "y": 0.861, "curve": [0.211, 1.067, 0.222, 1.095, 0.211, 0.847, 0.222, 0.832]}, {"time": 0.2333, "x": 1.095, "y": 0.832, "curve": [0.256, 1.095, 0.278, 1, 0.256, 0.832, 0.278, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 1.5, "curve": [1.511, 1, 1.522, 1.051, 1.511, 1, 1.522, 0.875]}, {"time": 1.5333, "x": 1.059, "y": 0.861, "curve": [1.544, 1.067, 1.556, 1.095, 1.544, 0.847, 1.556, 0.832]}, {"time": 1.5667, "x": 1.095, "y": 0.832, "curve": [1.589, 1.095, 1.611, 1, 1.589, 0.832, 1.611, 1]}, {"time": 1.6333, "curve": "stepped"}, {"time": 2.2333, "curve": [2.244, 1, 2.256, 1.051, 2.244, 1, 2.256, 0.875]}, {"time": 2.2667, "x": 1.059, "y": 0.861, "curve": [2.278, 1.067, 2.289, 1.095, 2.278, 0.847, 2.289, 0.832]}, {"time": 2.3, "x": 1.095, "y": 0.832, "curve": [2.322, 1.095, 2.344, 1, 2.322, 0.832, 2.344, 1]}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": [2.511, 1, 2.522, 1.051, 2.511, 1, 2.522, 0.875]}, {"time": 2.5333, "x": 1.059, "y": 0.861, "curve": [2.544, 1.067, 2.556, 1.095, 2.544, 0.847, 2.556, 0.832]}, {"time": 2.5667, "x": 1.095, "y": 0.832, "curve": [2.589, 1.095, 2.611, 1, 2.589, 0.832, 2.611, 1]}, {"time": 2.6333}]}, "Face": {"translate": [{"x": -6.82, "y": -6.34, "curve": "stepped"}, {"time": 0.7, "x": -6.82, "y": -6.34, "curve": [0.767, -6.82, 0.833, -6.82, 0.767, -6.34, 0.833, 0]}, {"time": 0.9, "x": -6.82, "curve": "stepped"}, {"time": 1.1, "x": -6.82, "curve": [1.167, -6.82, 1.233, -6.82, 1.167, 0, 1.233, -6.34]}, {"time": 1.3, "x": -6.82, "y": -6.34, "curve": "stepped"}, {"time": 1.8, "x": -6.82, "y": -6.34, "curve": [1.856, -6.82, 1.911, -6.82, 1.856, -6.34, 1.911, 0]}, {"time": 1.9667, "x": -6.82, "curve": "stepped"}, {"time": 2.8, "x": -6.82, "curve": [2.856, -6.82, 2.911, -6.82, 2.856, 0, 2.911, -6.34]}, {"time": 2.9667, "x": -6.82, "y": -6.34}]}, "cntr": {"rotate": [{"value": -1, "curve": [0.023, -1, 0.044, 0.78]}, {"time": 0.0667, "value": 0.78, "curve": [0.089, 0.78, 0.111, -1]}, {"time": 0.1333, "value": -1, "curve": [0.156, -1, 0.177, 0.78]}, {"time": 0.2, "value": 0.78, "curve": [0.223, 0.78, 0.244, -1]}, {"time": 0.2667, "value": -1, "curve": [0.289, -1, 0.311, 0.78]}, {"time": 0.3333, "value": 0.78, "curve": [0.356, 0.78, 0.377, -1]}, {"time": 0.4, "value": -1, "curve": [0.423, -1, 0.444, 0.78]}, {"time": 0.4667, "value": 0.78, "curve": [0.489, 0.78, 0.511, -1]}, {"time": 0.5333, "value": -1, "curve": [0.556, -1, 0.577, 0.78]}, {"time": 0.6, "value": 0.78, "curve": [0.623, 0.78, 0.644, -1]}, {"time": 0.6667, "value": -1, "curve": [0.689, -1, 0.711, 0.78]}, {"time": 0.7333, "value": 0.78, "curve": [0.756, 0.78, 0.811, -1]}, {"time": 0.8333, "value": -1, "curve": [0.856, -1, 0.877, 0.78]}, {"time": 0.9, "value": 0.78, "curve": [0.923, 0.78, 0.944, -1]}, {"time": 0.9667, "value": -1, "curve": [0.989, -1, 1.011, 0.78]}, {"time": 1.0333, "value": 0.78, "curve": [1.056, 0.78, 1.077, -1]}, {"time": 1.1, "value": -1, "curve": [1.123, -1, 1.144, 0.78]}, {"time": 1.1667, "value": 0.78, "curve": [1.189, 0.78, 1.211, -1]}, {"time": 1.2333, "value": -1, "curve": [1.256, -1, 1.277, 0.78]}, {"time": 1.3, "value": 0.78, "curve": [1.323, 0.78, 1.344, -1]}, {"time": 1.3667, "value": -1, "curve": [1.389, -1, 1.411, 0.78]}, {"time": 1.4333, "value": 0.78, "curve": [1.456, 0.78, 1.477, -1]}, {"time": 1.5, "value": -1, "curve": [1.523, -1, 1.544, 0.78]}, {"time": 1.5667, "value": 0.78, "curve": [1.589, 0.78, 1.611, -1]}, {"time": 1.6333, "value": -1, "curve": [1.656, -1, 1.677, 0.78]}, {"time": 1.7, "value": 0.78, "curve": [1.723, 0.78, 1.744, -1]}, {"time": 1.7667, "value": -1, "curve": [1.789, -1, 1.811, 0.78]}, {"time": 1.8333, "value": 0.78, "curve": [1.856, 0.78, 1.877, -1]}, {"time": 1.9, "value": -1, "curve": [1.923, -1, 1.944, 0.78]}, {"time": 1.9667, "value": 0.78, "curve": [1.989, 0.78, 2.011, -1]}, {"time": 2.0333, "value": -1, "curve": [2.056, -1, 2.077, 0.78]}, {"time": 2.1, "value": 0.78, "curve": [2.123, 0.78, 2.144, -1]}, {"time": 2.1667, "value": -1, "curve": [2.189, -1, 2.211, 0.78]}, {"time": 2.2333, "value": 0.78, "curve": [2.256, 0.78, 2.277, -1]}, {"time": 2.3, "value": -1, "curve": [2.323, -1, 2.377, 0.78]}, {"time": 2.4, "value": 0.78, "curve": [2.423, 0.78, 2.444, -1]}, {"time": 2.4667, "value": -1, "curve": [2.489, -1, 2.511, 0.78]}, {"time": 2.5333, "value": 0.78, "curve": [2.556, 0.78, 2.577, -1]}, {"time": 2.6, "value": -1, "curve": [2.623, -1, 2.644, 0.78]}, {"time": 2.6667, "value": 0.78, "curve": [2.689, 0.78, 2.711, -1]}, {"time": 2.7333, "value": -1, "curve": [2.756, -1, 2.777, 0.78]}, {"time": 2.8, "value": 0.78, "curve": [2.823, 0.78, 2.844, -1]}, {"time": 2.8667, "value": -1, "curve": [2.889, -1, 2.911, 0.78]}, {"time": 2.9333, "value": 0.78, "curve": [2.956, 0.78, 2.977, -1]}, {"time": 3, "value": -1}], "translate": [{"y": 2.43, "curve": [0.256, 0, 0.511, 0, 0.256, 2.43, 0.511, 16.12]}, {"time": 0.7667, "y": 16.12, "curve": [1.011, 0, 1.256, 0, 1.011, 16.12, 1.256, 2.43]}, {"time": 1.5, "y": 2.43, "curve": [1.756, 0, 2.011, 0, 1.756, 2.43, 2.011, 16.12]}, {"time": 2.2667, "y": 16.12, "curve": [2.511, 0, 2.756, 0, 2.511, 16.12, 2.756, 2.43]}, {"time": 3, "y": 2.43}], "scale": [{"x": 1.02, "y": 1.02, "curve": [0.11, 1.01, 0.22, 1, 0.11, 1.01, 0.22, 1]}, {"time": 0.3333, "curve": [0.583, 1, 0.85, 1.046, 0.583, 1, 0.85, 1.046]}, {"time": 1.1, "x": 1.046, "y": 1.046, "curve": [1.35, 1.046, 1.589, 1, 1.35, 1.046, 1.589, 1]}, {"time": 1.8333, "curve": [2.083, 1, 2.35, 1.046, 2.083, 1, 2.35, 1.046]}, {"time": 2.6, "x": 1.046, "y": 1.046, "curve": [2.737, 1.046, 2.868, 1.032, 2.737, 1.046, 2.868, 1.032]}, {"time": 3, "x": 1.02, "y": 1.02}]}, "Eyes_scale": {"scale": [{}]}, "eyebrows_l": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "eyebrows_r": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "pupil_R": {"translate": [{"x": -5.96, "y": -7.67, "curve": "stepped"}, {"time": 0.7333, "x": -5.96, "y": -7.67, "curve": [0.778, -5.96, 0.822, -5.96, 0.778, -7.67, 0.822, 1.11]}, {"time": 0.8667, "x": -5.96, "y": 1.11, "curve": "stepped"}, {"time": 1.1333, "x": -5.96, "y": 1.11, "curve": [1.178, -5.96, 1.222, -5.96, 1.178, 1.11, 1.222, -9.73]}, {"time": 1.2667, "x": -5.96, "y": -9.73, "curve": "stepped"}, {"time": 1.8333, "x": -5.96, "y": -9.73, "curve": [1.867, -5.96, 1.9, -5.96, 1.867, -9.73, 1.9, 0.85]}, {"time": 1.9333, "x": -5.96, "y": 0.85, "curve": "stepped"}, {"time": 2.1667, "x": -5.96, "y": 0.85, "curve": [2.2, -5.96, 2.233, -4.67, 2.2, 0.85, 2.233, -0.44]}, {"time": 2.2667, "x": -4.67, "y": -0.44, "curve": "stepped"}, {"time": 2.4, "x": -4.67, "y": -0.44, "curve": [2.444, -4.67, 2.489, -8.54, 2.444, -0.44, 2.489, 0.85]}, {"time": 2.5333, "x": -8.54, "y": 0.85, "curve": "stepped"}, {"time": 2.8333, "x": -8.54, "y": 0.85, "curve": [2.878, -8.54, 2.922, -5.96, 2.878, 0.85, 2.922, -7.67]}, {"time": 2.9667, "x": -5.96, "y": -7.67}]}, "pupil_L": {"translate": [{"x": 4.86, "y": -6.62, "curve": "stepped"}, {"time": 0.7333, "x": 4.86, "y": -6.62, "curve": [0.778, 4.86, 0.822, 4.86, 0.778, -6.62, 0.822, 2.16]}, {"time": 0.8667, "x": 4.86, "y": 2.16, "curve": "stepped"}, {"time": 1.1333, "x": 4.86, "y": 2.16, "curve": [1.178, 4.86, 1.222, 4.86, 1.178, 2.16, 1.222, -8.69]}, {"time": 1.2667, "x": 4.86, "y": -8.69, "curve": "stepped"}, {"time": 1.8333, "x": 4.86, "y": -8.69, "curve": [1.867, 4.86, 1.9, 4.86, 1.867, -8.69, 1.9, 1.9]}, {"time": 1.9333, "x": 4.86, "y": 1.9, "curve": "stepped"}, {"time": 2.1667, "x": 4.86, "y": 1.9, "curve": [2.2, 4.86, 2.233, 6.15, 2.2, 1.9, 2.233, 0.61]}, {"time": 2.2667, "x": 6.15, "y": 0.61, "curve": "stepped"}, {"time": 2.4, "x": 6.15, "y": 0.61, "curve": [2.444, 6.15, 2.489, 2.28, 2.444, 0.61, 2.489, 1.9]}, {"time": 2.5333, "x": 2.28, "y": 1.9, "curve": "stepped"}, {"time": 2.8333, "x": 2.28, "y": 1.9, "curve": [2.878, 2.28, 2.922, 4.86, 2.878, 1.9, 2.922, -6.62]}, {"time": 2.9667, "x": 4.86, "y": -6.62}]}, "mouth_base": {"translate": [{}], "scale": [{"x": 0.77}]}, "Mouth_low": {"translate": [{"y": 13.72}]}, "Teeth_L": {"translate": [{"y": 14.6}]}, "Teeth_R": {"translate": [{"y": 14.6}]}, "mouth_2": {"translate": [{}], "scale": [{"y": 1.297, "curve": "stepped"}, {"time": 0.5, "y": 1.297, "curve": [0.689, 1, 0.878, 1.083, 0.689, 1.297, 0.878, 0.84]}, {"time": 1.0667, "x": 1.083, "y": 0.84, "curve": "stepped"}, {"time": 1.1667, "x": 1.083, "y": 0.84, "curve": [1.222, 1.083, 1.278, 1, 1.222, 0.84, 1.278, 1.297]}, {"time": 1.3333, "y": 1.297, "curve": "stepped"}, {"time": 2.3, "y": 1.297, "curve": [2.4, 1, 2.5, 1, 2.4, 1.297, 2.5, 1]}, {"time": 2.6, "curve": [2.733, 1, 2.867, 1, 2.733, 1, 2.867, 1.297]}, {"time": 3, "y": 1.297}]}, "body_top": {"translate": [{}], "scale": [{}]}, "body_bottom": {"translate": [{}], "scale": [{}]}, "Body_R": {"translate": [{}], "scale": [{}]}, "Body_L": {"translate": [{}], "scale": [{}]}, "blot": {"translate": [{}], "scale": [{}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}}, "attachments": {"default": {"body": {"Simpy_body": {"deform": [{}]}}, "body_outline": {"Simpy_body_outline": {"deform": [{}]}}}}}, "t1_reaction": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "body": {"attachment": [{"name": "Simpy_body"}]}, "body_outline": {"attachment": [{"name": "Simpy_body_outline"}]}, "eyebrows_l": {"attachment": [{"name": "Simpy_eyebrows_l"}]}, "eyebrows_r": {"attachment": [{"name": "Simpy_eyebrows_r"}]}, "eyelid_l_r": {"attachment": [{"name": "Simpy_eyelid_l_r"}, {"time": 0.0667}]}, "eyelid_l_R": {"attachment": [{"name": "Simpy_eyelid_l_l"}, {"time": 0.0667}]}, "eyelid_u_r": {"attachment": [{}]}, "eyelid_u_R": {"attachment": [{}]}, "eye_L": {"attachment": [{"name": "Simpy_eye_r"}]}, "eye_R": {"attachment": [{"name": "Simpy_eye_l"}]}, "mouth_1": {"attachment": [{"name": "Simpy_mouth_1"}, {"time": 0.0667}]}, "mouth_2": {"attachment": [{}, {"time": 0.0667, "name": "Simpy_mouth_2"}]}, "mouth_base": {"attachment": [{"name": "Simpy_mouth_base"}]}, "pupil_L": {"attachment": [{"name": "Simpy_pupil_r"}]}, "pupil_R": {"attachment": [{"name": "Simpy_pupil_l"}]}, "teeth": {"attachment": [{"name": "Simpy_teeth"}, {"time": 0.0667}]}}, "bones": {"EyeLiid_R_U": {"translate": [{"x": -3.48, "y": 26.04}]}, "Eyelid_R_B": {"translate": [{"x": 0.59, "y": -7.66, "curve": [0.033, 0.59, 0.067, 0.59, 0.033, -7.66, 0.067, -12.83]}, {"time": 0.1, "x": 0.59, "y": -12.83}]}, "Eyelid_L_U": {"translate": [{"x": -2.09, "y": 24.95}]}, "Eyelid_L_B": {"translate": [{"x": 0.18, "y": -6.64, "curve": [0.033, 0.18, 0.067, 0.18, 0.033, -6.64, 0.067, -13.45]}, {"time": 0.1, "x": 0.18, "y": -13.45}]}, "eye_L": {"translate": [{}], "scale": [{"x": 1.18, "y": 1.03}]}, "eye_R": {"translate": [{}], "scale": [{}]}, "Face": {"translate": [{"x": -6.82, "curve": [0.033, -6.82, 0.067, -6.82, 0.033, 0, 0.067, 11.54]}, {"time": 0.1, "x": -6.82, "y": 11.54, "curve": [0.222, -6.82, 0.344, -6.82, 0.222, 11.54, 0.344, -4.91]}, {"time": 0.4667, "x": -6.82, "y": -4.91, "curve": [0.578, -6.82, 0.689, -6.82, 0.578, -4.91, 0.689, 0]}, {"time": 0.8, "x": -6.82}]}, "cntr": {"rotate": [{}], "translate": [{"y": 2.43, "curve": [0.033, 0, 0.067, 0, 0.033, 7.86, 0.067, 21.8]}, {"time": 0.1, "y": 21.8, "curve": [0.112, 0, 0.121, 0, 0.112, 21.8, 0.121, 21.33]}, {"time": 0.1333, "y": 21, "curve": [0.222, 0, 0.311, 0, 0.222, 18.7, 0.311, -3.61]}, {"time": 0.4, "y": -3.61, "curve": [0.5, 0, 0.6, 0, 0.5, -3.61, 0.6, 1.74]}, {"time": 0.7, "y": 2.43}], "scale": [{"curve": [0.022, 1, 0.044, 0.95, 0.022, 1, 0.044, 1.05]}, {"time": 0.0667, "x": 0.95, "y": 1.05, "curve": [0.1, 0.95, 0.133, 1.03, 0.1, 1.05, 0.133, 0.97]}, {"time": 0.1667, "x": 1.03, "y": 0.97, "curve": [0.211, 1.03, 0.256, 1, 0.211, 0.97, 0.256, 1]}, {"time": 0.3}]}, "Eyes_scale": {"scale": [{"curve": [0.033, 1, 0.067, 1.335, 0.033, 1, 0.067, 1.603]}, {"time": 0.1, "x": 1.335, "y": 1.603, "curve": [0.122, 1.335, 0.144, 1.335, 0.122, 1.603, 0.144, 1.335]}, {"time": 0.1667, "x": 1.335, "y": 1.335, "curve": [0.244, 1.335, 0.322, 1, 0.244, 1.335, 0.322, 1]}, {"time": 0.4}]}, "eyebrows_l": {"rotate": [{"curve": [0.04, 0, 0.06, 11.62]}, {"time": 0.1, "value": 11.62, "curve": "stepped"}, {"time": 0.1667, "value": 11.62, "curve": [0.267, 11.62, 0.367, 0]}, {"time": 0.4667}], "translate": [{"curve": [0.04, 0, 0.06, -15.78, 0.04, 0, 0.06, 16.16]}, {"time": 0.1, "x": -15.78, "y": 16.16, "curve": "stepped"}, {"time": 0.1667, "x": -15.78, "y": 16.16, "curve": [0.267, -15.78, 0.367, 0, 0.267, 16.16, 0.367, 0]}, {"time": 0.4667}], "scale": [{"curve": [0.04, 1, 0.06, 1.13, 0.04, 1, 0.06, 1.13]}, {"time": 0.1, "x": 1.13, "y": 1.13, "curve": "stepped"}, {"time": 0.1667, "x": 1.13, "y": 1.13, "curve": [0.267, 1.13, 0.367, 1, 0.267, 1.13, 0.367, 1]}, {"time": 0.4667}]}, "eyebrows_r": {"rotate": [{"curve": [0.04, 0, 0.06, -20.83]}, {"time": 0.1, "value": -20.83, "curve": "stepped"}, {"time": 0.1667, "value": -20.83, "curve": [0.267, -20.83, 0.367, 0]}, {"time": 0.4667}], "translate": [{"curve": [0.04, 0, 0.06, 18.23, 0.04, 0, 0.06, 16.16]}, {"time": 0.1, "x": 18.23, "y": 16.16, "curve": "stepped"}, {"time": 0.1667, "x": 18.23, "y": 16.16, "curve": [0.267, 18.23, 0.367, 0, 0.267, 16.16, 0.367, 0]}, {"time": 0.4667}], "scale": [{"curve": [0.04, 1, 0.06, 1.152, 0.04, 1, 0.06, 1.152]}, {"time": 0.1, "x": 1.152, "y": 1.152, "curve": "stepped"}, {"time": 0.1667, "x": 1.152, "y": 1.152, "curve": [0.267, 1.152, 0.367, 1, 0.267, 1.152, 0.367, 1]}, {"time": 0.4667}]}, "pupil_R": {"translate": [{"x": -5.96, "y": -7.67}]}, "pupil_L": {"translate": [{"x": 4.86, "y": -6.62}]}, "mouth_base": {"translate": [{}], "scale": [{"curve": [0.022, 1, 0.044, 0.77, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 0.77}]}, "Mouth_low": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.022, 0, 0.044, 13.72]}, {"time": 0.0667, "y": 13.72}]}, "Teeth_L": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.022, 0, 0.044, 14.6]}, {"time": 0.0667, "y": 14.6}]}, "Teeth_R": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.022, 0, 0.044, 14.6]}, {"time": 0.0667, "y": 14.6}]}, "mouth_2": {"translate": [{"y": 2.87, "curve": "stepped"}, {"time": 0.0667, "y": 2.87, "curve": [0.122, 0, 0.178, 0, 0.122, 2.87, 0.178, -3.87]}, {"time": 0.2333, "y": -3.87, "curve": [0.322, 0, 0.411, 0, 0.322, -3.87, 0.411, 0]}, {"time": 0.5}], "scale": [{"x": 2.041, "y": 0.543, "curve": "stepped"}, {"time": 0.0667, "x": 2.041, "y": 0.543, "curve": [0.122, 2.041, 0.178, 1, 0.122, 0.543, 0.178, 1.351]}, {"time": 0.2333, "y": 1.351, "curve": [0.322, 1, 0.411, 1, 0.322, 1.351, 0.411, 1]}, {"time": 0.5}]}, "body_top": {"translate": [{}], "scale": [{}]}, "body_bottom": {"translate": [{}], "scale": [{}]}, "Body_R": {"translate": [{}], "scale": [{}]}, "Body_L": {"translate": [{}], "scale": [{}]}, "blot": {"translate": [{}], "scale": [{}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}}, "attachments": {"default": {"body": {"Simpy_body": {"deform": [{}]}}, "body_outline": {"Simpy_body_outline": {"deform": [{}]}}}}}}}