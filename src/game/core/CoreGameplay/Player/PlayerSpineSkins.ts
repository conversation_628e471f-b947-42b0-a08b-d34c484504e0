import type { EventBus } from '@/shared/types'
import { SpineGameObject } from '@esotericsoftware/spine-phaser'
import { PlayerAnimations } from './PlayerStates/States/SpineAnimations'

export class SkinData {
  public index: number
  public name: string
  constructor(index: number, name: string) {
    this.index = index
    this.name = name
  }
}

export enum SkinNames {
  Agnes = 'Agnes',
  Anon = 'Anon',
  Astronaut = 'Astronaut',
  Balthazar = 'Balthazar',
  Base = 'Base',
  <PERSON> = 'Bear',
  Blue = 'Blue',
  Bottle = 'Bottle',
  Bull = 'Bull',
  Calcifer = 'Calcifer',
  Capibara = 'Capibara',
  Carrot = 'Carrot',
  Cat_mask = 'Cat_mask',
  Chainsaw = 'Chainsaw',
  Chick = 'Chick',
  Clown = 'Clown',
  Clown_Creepy = 'Clown_Creepy',
  Cloud = 'Cloud',
  Cosmos = 'Cosmos',
  Death = 'Death',
  <PERSON> = 'Devil',
  Dice = 'Dice',
  Diver = 'Diver',
  <PERSON><PERSON> = '<PERSON><PERSON>',
  <PERSON> = '<PERSON>',
  Dune = 'Dune',
  <PERSON>lingo = 'Duolingo',
  Easter_Basket = 'Easter_Basket',
  Easter_Egg_crack = 'Easter_Egg_Crack',
  <PERSON>_<PERSON> = 'Easter_Rabbit',
  <PERSON> = '<PERSON>',
  <PERSON><PERSON> = 'Emo',
  <PERSON> = 'Fairy',
  Genie = 'Genie',
  Gift = 'Gift',
  Gold_Pot = 'Gold_Pot',
  Gru = 'Gru',
  Grumpy_cat = 'Grumpy_cat',
  Headphones = 'Headphones',
  Inu = 'Inu',
  Kevin = 'Kevin',
  Lepricon = 'Lepricon',
  Lepricon_Messy = 'Lepricon_Messy',
  Maneki_Neko = 'Maneki_Neko',
  McDonalds = 'McDonalds',
  Monkey = 'Monkey',
  Monkey_meme = 'Monkey_meme',
  Mooncat = 'Mooncat',
  Mushroom = 'Mushroom',
  Ninja = 'Ninja',
  Orange_hat = 'Orange_hat',
  Pepe = 'Pepe',
  Penguin = 'Penguin',
  Pigeon = 'Pigeon',
  Poop = 'Poop',
  Popcat = 'Popcat',
  Potter = 'Potter',
  Princess = 'Princess',
  Princess_Lumpy = 'Princess_Lumpy',
  Purple_Minion = 'Purple_Minion',
  Rabbit = 'Rabbit',
  Resistance_Dog = 'Resistance_Dog',
  Rocket = 'Rocket',
  Rubik = 'Rubik',
  Skeleton = 'Skeleton',
  Spiral_Eyes = 'Spiral_Eyes',
  Spring = 'Spring',
  Super = 'Super',
  Swag = 'Swag',
  Trump = 'Trump',
  Tulip = 'Tulip',
  Vantus = 'Vantus',
  Vector = 'Vector',
  Witch = 'Witch',

  M_Captain = 'M_Captain',
  M_Hulk = 'M_Hulk',
  M_Iron = 'M_Iron',
  M_Loki = 'M_Loki',
  M_Spider = 'M_Spider',
  M_Thanos = 'M_Thanos',
  M_Thor = 'M_Thor',

  Minecraft_Creeper = 'Minecraft_Creeper',
  Minecraft_Diamond = 'Minecraft_Diamond',
  Minecraft_Enderman = 'Minecraft_Enderman',
  Minecraft_Mob = 'Minecraft_Mob',
  Minecraft_Steve = 'Minecraft_Steve',
  Minecraft_TNT = 'Minecraft_TNT',
  Minecraft_Zombie = 'Minecraft_Zombie',

  SW_Dart = 'SW_Dart',
  SW_Grogu = 'SW_Grogu',
  SW_Jedi = 'SW_Jedi',
  SW_Mando = 'SW_Mando',
  SW_Sith = 'SW_Sith',
  SW_Trooper = 'SW_Trooper',
  SW_Wookie = 'SW_Wookie',

  KP_Kai = 'KP_Kai',
  KP_Oogwei = 'KP_Oogwei',
  KP_Ping = 'KP_Ping',
  KP_Po = 'KP_Po',
  KP_Shifu = 'KP_Shifu',
  KP_Thai_Lung = 'KP_Thai_Lung',
  KP_Tigress = 'KP_Tigress',

  SB_Gary = 'SB_Gary',
  SB_Krabs = 'SB_Krabs',
  SB_Patrick = 'SB_Patrick',
  SB_Plankton = 'SB_Plankton',
  SB_Sandy = 'SB_Sandy',
  SB_Sponge = 'SB_Sponge',
  SB_Squid = 'SB_Squid',
  Sh_Shrek = 'Sh_Shrek',
  Sh_Dragon = 'Sh_Dragon',
  Sh_Fiona = 'Sh_Fiona',
  Sh_Gingy = 'Sh_Gingy',
  SH_Pinoccio = 'SH_Pinoccio',
  Sh_Donkey = 'Sh_Donkey',
  SH_InBoots = 'SH_InBoots',

  DC_Robin = 'DC_Robin',
  DC_GreenLantern = 'DC_GreenLantern',
  DC_Aquaman = 'DC_Aquaman',
  DC_Batman = 'DC_Batman',
  DC_Cyborg = 'DC_Cyborg',
  DC_Flash = 'DC_Flash',
  DC_Wonder = 'DC_Wonder',

  Scooby_Daphne = 'Scooby_Daphne',
  Scooby_Fred = 'Scooby_Fred',
  Scooby_Scooby = 'Scooby_Scooby',
  Scooby_Velma = 'Scooby_Velma',

  MK_SubZero = 'MK_SubZero',
  MK_Kitana = 'MK_Kitana',
  MK_Scorpion = 'MK_Scorpion',
  MK_Jade = 'MK_Jade',

  Dog = 'Dog',
  Cat = 'Cat',

  Durov = 'Durov',
  Plush = 'Plush',
  ScaredCat = 'ScaredCat',
  ToyBear = 'ToyBear',

  UniShitjumper = 'Shitjumper',
  UniBoinker = 'Boinker',

  UniMarty = 'Marti',
  UniGloria = 'Gloria',
  UniMelman = 'Melman',
  UniAlex = 'Alex',

  Solana = 'Solana',
  Ton = 'Ton',

  Hustle = 'Hustle',
  Bera = 'Bera',
  Brett = 'Brett',
  PepeHustle = 'PepeHustle',

  MrSoon = 'MrSoon',
  Tonjumper = 'Tonjumper',

  GOT_JohnSnow = 'GOT_JohnSnow',
  GOT_NigthKing = 'GOT_NigthKing',
  GOT_Daenerys = 'GOT_Daenerys',
  GOT_Dragon = 'GOT_Dragon',

  GOT_Water = 'GOT_Water',
  GOT_Fire = 'GOT_Fire',

  Dino = 'Dino',
  Yeti = 'Yeti',
  Koalin = 'Koalin',
  Alia = 'Alia',

  Match = 'Match',
  Money = 'Money',

  PandaFit = 'PandaFit',
  PandaInCap = 'PandaInCap',
  PandaMario = 'PandaMario',
  LuckyPanda = 'LuckyPanda',

  NicegramWhite = 'NicegramWhite',
  NicegramBlack = 'NicegramBlack',

  FightMe = 'FightMe',
  FightElf = 'FigthElf',
  FightHero = 'FigthHero',
  FightBear = 'FightBear',

  Labrador01 = 'Labrador01',
  Labrador02 = 'Labrador02',

  DuckYellow = 'DuckYellow',
  DuckKing = 'DuckKing',
  DuckDoctor = 'DuckDoctor',
  DuckCrocodile = 'DuckCrocodile',

  RichUni = 'RichUni',
  RichDog = 'RichDog'
}

export class PlayerSpineSkins {
  private playerSpine: SpineGameObject | null = null
  private availableSkins: Set<string> = new Set(Object.values(SkinNames))
  public currentSkin: SkinNames | undefined
  public currentSkinIndex: number = -1
  private skins: SkinData[]

  constructor(spine: SpineGameObject, uiEventBus: EventBus) {
    this.playerSpine = spine
    this.skins = this.createSkinsData()
  }

  private createSkinsData(): SkinData[] {
    return [
      new SkinData(-1, SkinNames.Base),
      new SkinData(0, SkinNames.Spiral_Eyes),
      new SkinData(1, SkinNames.Emo),
      new SkinData(2, SkinNames.Vantus),
      new SkinData(3, SkinNames.Cat_mask),
      new SkinData(4, SkinNames.Ninja),
      new SkinData(5, SkinNames.McDonalds),
      new SkinData(6, SkinNames.Potter),
      new SkinData(7, SkinNames.Devil),
      new SkinData(8, SkinNames.Super),
      new SkinData(9, SkinNames.Resistance_Dog),
      new SkinData(10, SkinNames.Diver),
      new SkinData(11, SkinNames.Duck),
      new SkinData(12, SkinNames.Capibara),
      new SkinData(13, SkinNames.Poop),
      new SkinData(14, SkinNames.Pepe),
      new SkinData(1000, SkinNames.Inu),
      new SkinData(1001, SkinNames.Trump),
      new SkinData(1002, SkinNames.Chainsaw),
      new SkinData(1003, SkinNames.Swag),
      new SkinData(1004, SkinNames.Dune),
      new SkinData(1005, SkinNames.Grumpy_cat),
      new SkinData(1006, SkinNames.Pigeon),
      new SkinData(1007, SkinNames.Duolingo),
      new SkinData(1008, SkinNames.Spring),
      new SkinData(1009, SkinNames.Lepricon_Messy),
      new SkinData(1010, SkinNames.Lepricon),

      new SkinData(2000, SkinNames.Orange_hat),
      new SkinData(2001, SkinNames.Witch),
      new SkinData(2002, SkinNames.Headphones),
      new SkinData(2003, SkinNames.Skeleton),
      new SkinData(2004, SkinNames.Death),
      new SkinData(2005, SkinNames.Calcifer),
      new SkinData(2006, SkinNames.Bear),
      new SkinData(2007, SkinNames.Monkey_meme),
      new SkinData(2008, SkinNames.Bull),
      new SkinData(2009, SkinNames.Doge),
      new SkinData(2010, SkinNames.Popcat),
      new SkinData(2011, SkinNames.Penguin),
      new SkinData(2012, SkinNames.Clown),
      new SkinData(2013, SkinNames.Clown_Creepy),
      new SkinData(2014, SkinNames.Anon),
      new SkinData(2015, SkinNames.Mushroom),
      new SkinData(2016, SkinNames.Princess),
      new SkinData(2017, SkinNames.Cloud),
      new SkinData(2018, SkinNames.Fairy),
      new SkinData(2019, SkinNames.Bottle),
      new SkinData(2020, SkinNames.Genie),
      new SkinData(2021, SkinNames.Rubik),
      new SkinData(2022, SkinNames.Gift),
      new SkinData(2023, SkinNames.Gold_Pot),
      new SkinData(2024, SkinNames.Maneki_Neko),
      new SkinData(2025, SkinNames.Rabbit),
      new SkinData(2026, SkinNames.Dice),
      new SkinData(2027, SkinNames.Cosmos),
      new SkinData(2028, SkinNames.Blue),
      new SkinData(2029, SkinNames.Princess_Lumpy),
      new SkinData(2030, SkinNames.Rocket),
      new SkinData(2031, SkinNames.Mooncat),
      new SkinData(2032, SkinNames.Astronaut),
      new SkinData(2033, SkinNames.Easter_Basket),
      new SkinData(2034, SkinNames.Chick),
      new SkinData(2035, SkinNames.Easter_Rabbit),
      new SkinData(2036, SkinNames.Carrot),
      new SkinData(2037, SkinNames.Easter_Egg_crack),
      new SkinData(2038, SkinNames.Tulip),
      new SkinData(2039, SkinNames.Minecraft_Mob),
      new SkinData(2040, SkinNames.Minecraft_TNT),
      new SkinData(2041, SkinNames.Minecraft_Zombie),
      new SkinData(2042, SkinNames.Minecraft_Diamond),
      new SkinData(2043, SkinNames.Minecraft_Steve),
      new SkinData(2044, SkinNames.Minecraft_Enderman),
      new SkinData(2045, SkinNames.Minecraft_Creeper),
      new SkinData(2046, SkinNames.Agnes),
      new SkinData(2047, SkinNames.Edith),
      new SkinData(2048, SkinNames.Balthazar),
      new SkinData(2049, SkinNames.Kevin),
      new SkinData(2050, SkinNames.Purple_Minion),
      new SkinData(2051, SkinNames.Gru),
      new SkinData(2052, SkinNames.Vector),
      new SkinData(2053, SkinNames.SW_Dart),
      new SkinData(2054, SkinNames.SW_Jedi),
      new SkinData(2055, SkinNames.SW_Sith),
      new SkinData(2056, SkinNames.SW_Trooper),
      new SkinData(2057, SkinNames.SW_Grogu),
      new SkinData(2058, SkinNames.SW_Wookie),
      new SkinData(2059, SkinNames.SW_Mando),
      new SkinData(2060, SkinNames.M_Captain),
      new SkinData(2061, SkinNames.M_Hulk),
      new SkinData(2062, SkinNames.M_Iron),
      new SkinData(2063, SkinNames.M_Loki),
      new SkinData(2064, SkinNames.M_Spider),
      new SkinData(2065, SkinNames.M_Thanos),
      new SkinData(2066, SkinNames.M_Thor),

      new SkinData(2067, SkinNames.KP_Po),
      new SkinData(2068, SkinNames.KP_Shifu),
      new SkinData(2069, SkinNames.KP_Ping),
      new SkinData(2070, SkinNames.KP_Kai),
      new SkinData(2071, SkinNames.KP_Thai_Lung),
      new SkinData(2072, SkinNames.KP_Tigress),
      new SkinData(2073, SkinNames.KP_Oogwei),

      new SkinData(2074, SkinNames.SB_Gary),
      new SkinData(2075, SkinNames.SB_Krabs),
      new SkinData(2076, SkinNames.SB_Patrick),
      new SkinData(2077, SkinNames.SB_Sponge),
      new SkinData(2078, SkinNames.SB_Plankton),
      new SkinData(2079, SkinNames.SB_Sandy),
      new SkinData(2080, SkinNames.SB_Squid),
      new SkinData(2081, SkinNames.Sh_Shrek),
      new SkinData(2082, SkinNames.Sh_Dragon),
      new SkinData(2083, SkinNames.Sh_Fiona),
      new SkinData(2084, SkinNames.Sh_Gingy),
      new SkinData(2085, SkinNames.SH_Pinoccio),
      new SkinData(2086, SkinNames.Sh_Donkey),
      new SkinData(2087, SkinNames.SH_InBoots),
      new SkinData(2088, SkinNames.DC_Robin),
      new SkinData(2089, SkinNames.DC_GreenLantern),
      new SkinData(2090, SkinNames.DC_Aquaman),
      new SkinData(2091, SkinNames.DC_Batman),
      new SkinData(2092, SkinNames.DC_Cyborg),
      new SkinData(2093, SkinNames.DC_Flash),
      new SkinData(2094, SkinNames.DC_Wonder),
      new SkinData(2095, SkinNames.Scooby_Fred),
      new SkinData(2096, SkinNames.Scooby_Velma),
      new SkinData(2097, SkinNames.Scooby_Daphne),
      new SkinData(2098, SkinNames.Scooby_Scooby),
      new SkinData(2099, SkinNames.MK_Jade),
      new SkinData(2100, SkinNames.MK_SubZero),
      new SkinData(2101, SkinNames.MK_Kitana),
      new SkinData(2102, SkinNames.MK_Scorpion),
      new SkinData(2103, SkinNames.Cat),
      new SkinData(2104, SkinNames.Dog),
      new SkinData(2105, SkinNames.ToyBear),
      new SkinData(2106, SkinNames.Durov),
      new SkinData(2107, SkinNames.Plush),
      new SkinData(2108, SkinNames.ScaredCat),
      new SkinData(2109, SkinNames.UniShitjumper),
      new SkinData(2110, SkinNames.UniBoinker),
      new SkinData(2111, SkinNames.UniMarty),
      new SkinData(2112, SkinNames.UniGloria),
      new SkinData(2113, SkinNames.UniMelman),
      new SkinData(2114, SkinNames.UniAlex),
      new SkinData(2115, SkinNames.Ton),
      new SkinData(2116, SkinNames.Solana),
      new SkinData(2117, SkinNames.Hustle),
      new SkinData(2118, SkinNames.Bera),
      new SkinData(2119, SkinNames.Brett),
      new SkinData(2120, SkinNames.PepeHustle),
      new SkinData(2121, SkinNames.Tonjumper),
      new SkinData(2122, SkinNames.MrSoon),
      new SkinData(2123, SkinNames.GOT_JohnSnow),
      new SkinData(2124, SkinNames.GOT_Daenerys),
      new SkinData(2125, SkinNames.GOT_Dragon),
      new SkinData(2126, SkinNames.GOT_NigthKing),
      new SkinData(2127, SkinNames.GOT_Water),
      new SkinData(2128, SkinNames.GOT_Fire),
      new SkinData(2129, SkinNames.Dino),
      new SkinData(2130, SkinNames.Yeti),
      new SkinData(2131, SkinNames.Koalin),
      new SkinData(2132, SkinNames.Alia),
      new SkinData(2133, SkinNames.Match),
      new SkinData(2134, SkinNames.Money),
      new SkinData(2135, SkinNames.PandaFit),
      new SkinData(2136, SkinNames.PandaInCap),
      new SkinData(2137, SkinNames.PandaMario),
      new SkinData(2138, SkinNames.LuckyPanda),
      new SkinData(2139, SkinNames.NicegramWhite),
      new SkinData(2140, SkinNames.NicegramBlack),
      new SkinData(2154, SkinNames.FightMe),
      new SkinData(2155, SkinNames.FightElf),
      new SkinData(2156, SkinNames.FightHero),
      new SkinData(2157, SkinNames.FightBear),

      new SkinData(2158, SkinNames.Labrador01),
      new SkinData(2159, SkinNames.Labrador02),

      new SkinData(2160, SkinNames.DuckYellow),
      new SkinData(2161, SkinNames.DuckDoctor),
      new SkinData(2162, SkinNames.DuckCrocodile),
      new SkinData(2163, SkinNames.DuckKing),

      new SkinData(2165, SkinNames.RichDog),
      new SkinData(2166, SkinNames.RichUni),

      new SkinData(3000, SkinNames.Monkey)
    ]
  }

  updateSpine(newSpine: SpineGameObject) {
    this.playerSpine = newSpine
  }

  public changeSkin(skinName: SkinNames): void {
    if (this.availableSkins.has(skinName)) {
      this.playerSpine!.skeleton.setSkinByName(skinName)
      this.playerSpine!.skeleton.setSlotsToSetupPose()
      this.playerSpine!.animationState.apply(this.playerSpine!.skeleton)
      this.triggerAdditionalAnimationForSkin()
    } else {
      console.warn(`Skin ${skinName} not found`)
    }
  }

  public triggerAdditionalAnimationForSkin(): void {
    if (this.currentSkin === SkinNames.Chainsaw && this.playerSpine) {
      this.playerSpine.animationState.setAnimation(5, PlayerAnimations.CHAINSAW_IDLE, true)
    }
  }

  public setDefaultSkin(): void {
    this.currentSkinIndex = -1
    this.currentSkin = SkinNames.Base
    this.changeSkin(SkinNames.Base)
  }

  public setSkin(index: number): void {
    const skinData = this.skins.find(s => s.index === index)
    if (!skinData) {
      console.warn(`Skin with index ${index} not found`)
      this.setDefaultSkin()
      return
    }
    this.currentSkinIndex = index
    this.currentSkin = skinData.name as SkinNames
    this.changeSkin(skinData.name as SkinNames)
  }

  public moveNextSkin(): void {
    let currentIndex = this.skins.findIndex(s => s.index === this.currentSkinIndex)
    if (currentIndex === -1) {
      currentIndex = 0
    }
    const nextIndex = (currentIndex + 1) % this.skins.length
    const nextSkin = this.skins[nextIndex]
    this.currentSkinIndex = nextSkin.index
    this.currentSkin = nextSkin.name as SkinNames
    this.changeSkin(this.currentSkin)
  }
}
