<script setup lang="ts">
import { useLeagueLeaders, useLeagueUserInfo } from '@/services/client/useLeagues'
import { computed } from 'vue'
import LeaderboardList, { type LeaderboardCurrency } from '../events/LeaderboardList.vue'
import LoaderText from '../LoaderText.vue'

import { LEAGUE_TO_UNI } from '@/constants/leagues'
import { formatNumberToShortString } from '@/utils/number'
import ProgressBar from '../UI/ProgressBar.vue'
import LeagueBadge from './LeagueBadge.vue'

import { REWARD_TO_IMAGE } from '@/composables/useIconImage'
import checkIcon from '@/assets/images/temp/check-icon.png'
import { LEAGUES_INSTRUCTION } from '@/constants/instructions.ts'
import InstructionButton from '../UI/InstructionButton.vue'
import TextDivider from '../UI/TextDivider.vue'
import { isTimeRewardType } from '@/types'
import type { RewardType } from '@/services/openapi'

const props = defineProps<{
  league: number
  leagueName: string
  ticketsRange: [number, number]
  rewards: Array<{ type: RewardType; value: number }>
  active: boolean
  rendered: boolean
  userLeague: number
  userTickets: number
  isLast?: boolean
}>()

const ticketsStart = props.ticketsRange[0]
const ticketsEnd = props.ticketsRange[1]
const ticketsDelta = ticketsEnd - ticketsStart

const isLeagueActive = computed(() => props.active)
const isUserInLeague = computed(() => isLeagueActive.value && props.league === props.userLeague)
const userTicketsProgress = computed(() =>
  props.isLast ? ticketsDelta : Math.max(props.userTickets - ticketsStart, 0)
)

const { leaderboard: leaderboardRaw, isLoading: isLoadingLeaders } = useLeagueLeaders(
  props.league,
  isLeagueActive
)

const { userInfo, isLoading: isLoadingUserInfo } = useLeagueUserInfo(isUserInLeague)

const leaderboard = computed(() => {
  return leaderboardRaw.value.map(leader => ({
    ...leader,
    balance: leader.value,
    league: props.league,
    value: undefined,
    currency: 'tickets' as LeaderboardCurrency
  }))
})

const user = computed(() => {
  return {
    rank: userInfo.value?.rank ?? undefined,
    balance: userInfo.value?.tickets ?? 0,
    currency: 'tickets' as LeaderboardCurrency,
    league: userInfo.value?.leagueLevel ?? 0
  }
})
</script>

<template>
  <div class="league" v-if="rendered">
    <div class="league-info">
      <div class="league-name z-10">
        <LeagueBadge :league="league" />
        <div class="text-[18px] text-shadow text-shadow_black">
          {{ leagueName }}
        </div>
      </div>

      <InstructionButton
        class="absolute top-[5px] right-[5px]"
        :instruction-type="LEAGUES_INSTRUCTION"
      />

      <div class="league-progress z-10" v-if="userLeague === league">
        <div
          class="icon-bg ticket-bg absolute -top-[10px] -left-[18px] z-10 !w-[40px] !h-[40px]"
        ></div>
        <LeagueBadge
          v-if="!isLast"
          class="absolute -top-[14px] -right-[25px] z-10"
          :league="league + 1"
        />
        <ProgressBar
          class="h-[26px] w-[184px]"
          inner-wrapper-class="p-[2px]"
          inner-class="league-progress-bar__inner"
          :progress="userTicketsProgress"
          :goal="ticketsDelta"
          :transition-speed="0"
        >
          <div
            class="flex items-center justify-center gap-[3px]"
          >
            <p class="text-[16px] leading-[23px] text-shadow text-shadow_black">
              {{ formatNumberToShortString(userTickets)
              }}{{ !isLast ? '/' + formatNumberToShortString(ticketsRange[1]) : '' }}
            </p>
          </div>
        </ProgressBar>
      </div>

      <div class="league-info__top">
        <div class="league-image__wrapper">
          <img class="league-image" :src="LEAGUE_TO_UNI[league]" alt="league" />
        </div>
      </div>
      <div class="league-info__bottom">
        <TextDivider
          v-if="userLeague !== league && rewards.length"
          class="absolute top-[3px] left-1/2 -translate-x-1/2 w-full px-[21px] z-10"
          color="#007BB4"
        >
          Rewards
        </TextDivider>
        <div class="league-info__rewards">
          <div v-for="(reward, index) in rewards" :key="index" class="league-info__reward-wrapper">
            <div
              class="league-info__reward"
              :class="{
                'league-info__reward_collected': league <= userLeague
              }"
            >
              <img
                :src="REWARD_TO_IMAGE[reward.type!]"
                class="league-info__reward-image"
                alt="league reward"
              />
              <div class="league-info__reward-amount">
                {{
                  reward.value === null || reward.value === 0
                    ? 'Full'
                    : isTimeRewardType(reward.type)
                      ? reward.value / 60 + ' min'
                      : formatNumberToShortString(reward.value)
                }}
              </div>
            </div>
            <img
              v-if="league <= userLeague"
              class="absolute left-1/2 -translate-x-1/2 bottom-0 translate-y-1/2 w-[21px]"
              :src="checkIcon"
              alt="check"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="league__leaderboard px-[13px]">
      <div
        v-if="isLoadingLeaders || isLoadingUserInfo"
        class="flex items-center justify-center h-full"
      >
        <LoaderText is-loading />
      </div>
      <div v-else-if="leaderboard.length">
        <div class="league__shadow-gradient"></div>
        <LeaderboardList
          class="league-leaderboard"
          :leaderboard="leaderboard"
          :user="league === userLeague ? user : undefined"
        />
        <div class="league__shadow-gradient"></div>
      </div>
      <div v-else class="flex items-center justify-center h-full">
        <p class="text-center mt-[12px] mb-[10px]">Crystal Path {{ league }} League is empty</p>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.league {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 29px 12px 0;

  &_left {
    .league-info {
      transform: translateX(23%);
    }
  }

  &_right {
    .league-info {
      transform: translateX(-23%);
    }
  }

  &-info {
    flex: 0 0 50%;
    width: 78%;
    margin: 0 auto;
    position: relative;

    display: flex;
    flex-direction: column;
    align-items: center;

    border-radius: 10px;
    transition: transform 0.1s;
    background: radial-gradient(80% 70% at 50% 50%, #10d4ff 17.5%, rgba(16, 212, 255, 0) 100%);

    &__top {
      position: relative;
      flex: 0 0 60%;
      width: 100%;
      background: #43f2ff38;
      border-radius: 10px 10px 0 0;
    }

    &__bottom {
      position: relative;
      flex: 0 0 40%;
      width: 100%;
      background: #80f7ff85;
      border-radius: 0 0 10px 10px;
      padding: 26px 0 18px;
      display: flex;
      flex-direction: column;
    }

    &__rewards {
      display: flex;
      justify-content: center;
      align-items: end;
      gap: 11px;
      flex: 1;
    }

    &__reward-wrapper {
      position: relative;
      flex: 0 0 25%;
      height: 100%;
    }

    &__reward {
      position: relative;
      height: 100%;
      background-color: #007bb445;
      border-radius: 9px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      &-image {
        position: absolute;
        width: 40px;
        transform: translateY(-7px);
      }

      &-amount {
        white-space: nowrap;
        width: 100%;
        text-align: center;
        position: absolute;
        font-size: 12px;
        line-height: 16px;
        font-weight: 800;
        transform: translateY(20px);
      }

      &_collected {
        opacity: 0.7;

        .league-info__reward {
          &-image {
            transform: translateY(-12px);
          }

          &-amount {
            transform: translateY(14px);
          }
        }
      }
    }
  }

  &__tickets-range {
    display: flex;
    align-items: center;
    gap: 4px;
    border-radius: 5px;
    padding: 2px 10px 2px 7px;
    background: linear-gradient(360deg, #015098 0%, #002b6b 100%);
    color: #ffe657;
    font-size: 18px;
    line-height: 24px;
  }

  &__leaderboard {
    flex: 1;
    overflow-x: visible;
    overflow-y: auto;
  }

  &__shadow-gradient {
    position: sticky;
    z-index: 1;
    left: 0;
    width: 100%;
    height: 28px;

    &:first-child {
      top: 0;
      background: linear-gradient(180deg, var(--menu-color) 40%, rgba(41, 87, 154, 0) 100%);
      transform: translateY(-1px);
    }

    &:last-child {
      bottom: 0;
      background: linear-gradient(360deg, var(--menu-color) 40%, rgba(41, 87, 154, 0) 100%);
      transform: translateY(1px);
    }
  }
}

.league-name {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, -50%);

  display: flex;
  align-items: center;
  column-gap: 4px;
}

.league-progress {
  top: 50%;
  left: 50%;
  transform: translate(-50%, 50%);
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: end;
}

.league-image {
  position: absolute;
  object-fit: contain;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;

  &__wrapper {
    --shadow-color-1: #2c548b;
    --shadow-color-2: rgba(11, 101, 164, 0);
    position: absolute;
    top: 50%;
    transform: translateY(-50%);

    height: 80%;
    width: 100%;

    &::after {
      content: '';
      position: absolute;
      bottom: 4px;
      left: 50%;
      transform: translate(-50%, 40%);
      width: 50%;
      height: 20%;
      border-radius: 50%;
      background: radial-gradient(
        50% 50% at 50% 50%,
        var(--shadow-color-1) 17.5%,
        var(--shadow-color-2) 100%
      );
      transition: transform 0.3s;
      z-index: -1;
    }
  }
}

.league-progress-bar__inner {
  background: #FFE02F !important;
  box-shadow: 0 1px #00000040, inset #FFC636 0 -11px, inset 0 2px #FFF08E;
}
</style>
