import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import { addToPlayerState } from '@/services/client/usePlayerState'
import {
  getFortuneWheelConfigOptions,
  getPlayerStateQueryKey,
  spinWheelMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import type { NumeralRewardType } from '@/types'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { computed } from 'vue'

export function useWheelSpinConfig() {
  const { data, isLoading } = useQuery({
    ...getFortuneWheelConfigOptions()
  })

  const sectors = computed(() => data.value?.sectors ?? [])

  return {
    sectors,
    isLoading
  }
}

export function useGetWheelSpin(rewardDelay: number = 0) {
  const queryClient = useQueryClient()
  const { updatePlayerState } = addToPlayerState()
  let isFreeSpin = false

  const { mutateAsync, isPending } = useMutation({
    ...spinWheelMutation(),
    retry: 3,
    retryDelay: 1000,
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: data => {
      if (isFreeSpin) {
        queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
      } else {
        updatePlayerState('wheelSpins', -1)
        setTimeout(() => {
          const reward = data.reward
          updatePlayerState(reward.type as NumeralRewardType, reward.value)
        }, rewardDelay)
      }
    }
  })

  const getWheelSpin = async (isFree: boolean = false) => {
    isFreeSpin = isFree
    return mutateAsync({
      body: {}
    }).then(data => {
      return data
    })
  }

  return { getWheelSpin, isPending }
}
