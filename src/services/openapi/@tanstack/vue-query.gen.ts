// This file is auto-generated by @hey-api/openapi-ts

import type { Options } from '@hey-api/client-fetch'
import { queryOptions, type UseMutationOptions } from '@tanstack/vue-query'
import {
  cancelWithdraw,
  checkGlobalTask,
  checkTonInvoice,
  claimAchievement,
  claimBattleEventBooster,
  claimDailyReward,
  claimDailyTasks,
  claimDailyTasksLootbox,
  claimFarming,
  claimGlobalTask,
  claimLeagueReward,
  claimLockedBalance,
  claimReferralReward,
  claimSkin,
  claimWithdrawTask,
  clansRating,
  client,
  completeGlobalTask,
  connectWallet,
  convertCustomCoins,
  createGameplaySessionOctet,
  createInvoice,
  createReferralLink,
  createReviveInvoiceByStars,
  createWithdraw,
  denyWriteOffRansom,
  disconnectWallet,
  getAchievementsList,
  getAchievementsState,
  getAssets,
  getBattleEventBoosters,
  getBattleEventLeaders,
  getBattleEventUserInfo,
  getClanEventLeaders,
  getClanEventUserInfo,
  getContestInfo,
  getCustomCoinEventUserInfo,
  getCustomCoinsLeaders,
  getDailyTasks,
  getFortuneWheelConfig,
  getFreeLootbox,
  getGlobalTasks,
  getHotrecordLeaders,
  getHotrecordUserInfo,
  getLeagueList,
  getLeaguesLeaders,
  getLeaguesUserInfo,
  getOnepercentLeaders,
  getOnepercentUserInfo,
  getPlayerProfile,
  getPlayerState,
  getReferralsList,
  getReviveInfo,
  getShopItems,
  getSkins,
  getTransactionsHistory,
  getUserProfile,
  getUtc,
  getWithdrawHistory,
  login,
  openLootbox,
  purchase,
  purchaseEventBooster,
  purchaseJackpotCoupons,
  purchaseProgressive,
  purchasePuzzle,
  purchaseRansom,
  purchaseRevive,
  purchaseSkin,
  removePlayerFlag,
  selectSkin,
  spinWheel,
  startClanEvent,
  startFarming,
  updateGameplaySessionOctet,
  usersInClanRating
} from '../services.gen'
import type {
  CancelWithdrawData,
  CancelWithdrawError,
  CancelWithdrawResponse2,
  CheckGlobalTaskData,
  CheckGlobalTaskError,
  CheckGlobalTaskResponse2,
  CheckTonInvoiceData,
  CheckTonInvoiceError,
  CheckTonInvoiceResponse2,
  ClaimAchievementData,
  ClaimAchievementError,
  ClaimAchievementResponse2,
  ClaimBattleEventBoosterData,
  ClaimBattleEventBoosterError,
  ClaimBattleEventBoosterResponse2,
  ClaimDailyRewardData,
  ClaimDailyRewardError,
  ClaimDailyRewardResponse2,
  ClaimDailyTasksData,
  ClaimDailyTasksError,
  ClaimDailyTasksLootboxError,
  ClaimDailyTasksLootboxResponse,
  ClaimDailyTasksResponse,
  ClaimFarmingData,
  ClaimFarmingError,
  ClaimFarmingResponse2,
  ClaimGlobalTaskData,
  ClaimGlobalTaskError,
  ClaimGlobalTaskResponse2,
  ClaimLeagueRewardData,
  ClaimLeagueRewardError,
  ClaimLeagueRewardResponse2,
  ClaimLockedBalanceError,
  ClaimLockedBalanceResponse,
  ClaimReferralRewardData,
  ClaimReferralRewardError,
  ClaimReferralRewardResponse2,
  ClaimSkinData,
  ClaimSkinError,
  ClaimSkinResponse2,
  ClaimWithdrawTaskData,
  ClaimWithdrawTaskError,
  ClaimWithdrawTaskResponse,
  CompleteGlobalTaskData,
  CompleteGlobalTaskError,
  CompleteGlobalTaskResponse2,
  ConnectWalletData,
  ConnectWalletError,
  ConnectWalletResponse2,
  ConvertCustomCoinsData,
  ConvertCustomCoinsError,
  ConvertCustomCoinsResponse2,
  CreateGameplaySessionOctetData,
  CreateGameplaySessionOctetError,
  CreateGameplaySessionOctetResponse,
  CreateInvoiceData,
  CreateInvoiceError,
  CreateInvoiceResponse2,
  CreateReferralLinkData,
  CreateReferralLinkError,
  CreateReferralLinkResponse2,
  CreateReviveInvoiceByStarsData,
  CreateReviveInvoiceByStarsError,
  CreateReviveInvoiceByStarsResponse2,
  CreateWithdrawData,
  CreateWithdrawError,
  CreateWithdrawResponse2,
  DenyWriteOffRansomError,
  DenyWriteOffRansomResponse,
  DisconnectWalletData,
  DisconnectWalletError,
  DisconnectWalletResponse2,
  GetContestInfoData,
  GetFreeLootboxError,
  GetFreeLootboxResponse,
  GetLeaguesLeadersData,
  GetReferralsListData,
  GetReviveInfoData,
  GetReviveInfoError,
  GetReviveInfoResponse,
  GetUserProfileData,
  LoginData,
  OpenLootboxData,
  OpenLootboxError,
  OpenLootboxResponse,
  PurchaseData,
  PurchaseError,
  PurchaseEventBoosterData,
  PurchaseEventBoosterError,
  PurchaseEventBoosterResponse2,
  PurchaseJackpotCouponsData,
  PurchaseJackpotCouponsError,
  PurchaseJackpotCouponsResponse2,
  PurchaseProgressiveData,
  PurchaseProgressiveError,
  PurchaseProgressiveResponse,
  PurchasePuzzleData,
  PurchasePuzzleError,
  PurchasePuzzleResponse,
  PurchaseRansomError,
  PurchaseRansomResponse,
  PurchaseResponse2,
  PurchaseReviveData,
  PurchaseReviveError,
  PurchaseReviveResponse2,
  PurchaseSkinData,
  PurchaseSkinError,
  PurchaseSkinResponse2,
  RemovePlayerFlagData,
  RemovePlayerFlagError,
  RemovePlayerFlagResponse2,
  SelectSkinData,
  SelectSkinError,
  SelectSkinResponse2,
  SpinWheelError,
  SpinWheelResponse,
  StartClanEventError,
  StartClanEventResponse2,
  StartFarmingData,
  StartFarmingError,
  StartFarmingResponse2,
  UpdateGameplaySessionOctetData,
  UpdateGameplaySessionOctetError,
  UpdateGameplaySessionOctetResponse,
  UsersInClanRatingData
} from '../types.gen'

type QueryKey<TOptions extends Options> = [
  Pick<TOptions, 'baseUrl' | 'body' | 'headers' | 'path' | 'query'> & {
    _id: string
    _infinite?: boolean
  }
]

const createQueryKey = <TOptions extends Options>(
  id: string,
  options?: TOptions,
  infinite?: boolean
): QueryKey<TOptions>[0] => {
  const params: QueryKey<TOptions>[0] = {
    _id: id,
    baseUrl: (options?.client ?? client).getConfig().baseUrl
  } as QueryKey<TOptions>[0]
  if (infinite) {
    params._infinite = infinite
  }
  if (options?.body) {
    params.body = options.body
  }
  if (options?.headers) {
    params.headers = options.headers
  }
  if (options?.path) {
    params.path = options.path
  }
  if (options?.query) {
    params.query = options.query
  }
  return params
}

export const claimAchievementQueryKey = (options: Options<ClaimAchievementData>) => [
  createQueryKey('claimAchievement', options)
]

export const claimAchievementOptions = (options: Options<ClaimAchievementData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimAchievement({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimAchievementQueryKey(options)
  })
}

export const claimAchievementMutation = (options?: Partial<Options<ClaimAchievementData>>) => {
  const mutationOptions: UseMutationOptions<
    ClaimAchievementResponse2,
    ClaimAchievementError,
    Options<ClaimAchievementData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimAchievement({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getAchievementsListQueryKey = (options?: Options) => [
  createQueryKey('getAchievementsList', options)
]

export const getAchievementsListOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getAchievementsList({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getAchievementsListQueryKey(options)
  })
}

export const getAchievementsStateQueryKey = (options?: Options) => [
  createQueryKey('getAchievementsState', options)
]

export const getAchievementsStateOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getAchievementsState({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getAchievementsStateQueryKey(options)
  })
}

export const loginQueryKey = (options?: Options<LoginData>) => [createQueryKey('login', options)]

export const loginOptions = (options?: Options<LoginData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await login({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: loginQueryKey(options)
  })
}

export const clansRatingQueryKey = (options?: Options) => [createQueryKey('clansRating', options)]

export const clansRatingOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await clansRating({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: clansRatingQueryKey(options)
  })
}

export const usersInClanRatingQueryKey = (options: Options<UsersInClanRatingData>) => [
  createQueryKey('usersInClanRating', options)
]

export const usersInClanRatingOptions = (options: Options<UsersInClanRatingData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await usersInClanRating({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: usersInClanRatingQueryKey(options)
  })
}

export const getContestInfoQueryKey = (options: Options<GetContestInfoData>) => [
  createQueryKey('getContestInfo', options)
]

export const getContestInfoOptions = (options: Options<GetContestInfoData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getContestInfo({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getContestInfoQueryKey(options)
  })
}

export const getBattleEventBoostersQueryKey = (options?: Options) => [
  createQueryKey('getBattleEventBoosters', options)
]

export const getBattleEventBoostersOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getBattleEventBoosters({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getBattleEventBoostersQueryKey(options)
  })
}

export const claimBattleEventBoosterQueryKey = (options: Options<ClaimBattleEventBoosterData>) => [
  createQueryKey('claimBattleEventBooster', options)
]

export const claimBattleEventBoosterOptions = (options: Options<ClaimBattleEventBoosterData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimBattleEventBooster({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimBattleEventBoosterQueryKey(options)
  })
}

export const claimBattleEventBoosterMutation = (
  options?: Partial<Options<ClaimBattleEventBoosterData>>
) => {
  const mutationOptions: UseMutationOptions<
    ClaimBattleEventBoosterResponse2,
    ClaimBattleEventBoosterError,
    Options<ClaimBattleEventBoosterData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimBattleEventBooster({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getBattleEventLeadersQueryKey = (options?: Options) => [
  createQueryKey('getBattleEventLeaders', options)
]

export const getBattleEventLeadersOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getBattleEventLeaders({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getBattleEventLeadersQueryKey(options)
  })
}

export const getBattleEventUserInfoQueryKey = (options?: Options) => [
  createQueryKey('getBattleEventUserInfo', options)
]

export const getBattleEventUserInfoOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getBattleEventUserInfo({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getBattleEventUserInfoQueryKey(options)
  })
}

export const getClanEventLeadersQueryKey = (options?: Options) => [
  createQueryKey('getClanEventLeaders', options)
]

export const getClanEventLeadersOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getClanEventLeaders({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getClanEventLeadersQueryKey(options)
  })
}

export const getClanEventUserInfoQueryKey = (options?: Options) => [
  createQueryKey('getClanEventUserInfo', options)
]

export const getClanEventUserInfoOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getClanEventUserInfo({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getClanEventUserInfoQueryKey(options)
  })
}

export const startClanEventQueryKey = (options?: Options) => [
  createQueryKey('startClanEvent', options)
]

export const startClanEventOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await startClanEvent({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: startClanEventQueryKey(options)
  })
}

export const startClanEventMutation = (options?: Partial<Options>) => {
  const mutationOptions: UseMutationOptions<StartClanEventResponse2, StartClanEventError, Options> =
    {
      mutationFn: async localOptions => {
        const { data } = await startClanEvent({
          ...options,
          ...localOptions,
          throwOnError: true
        })
        return data
      }
    }
  return mutationOptions
}

export const convertCustomCoinsQueryKey = (options: Options<ConvertCustomCoinsData>) => [
  createQueryKey('convertCustomCoins', options)
]

export const convertCustomCoinsOptions = (options: Options<ConvertCustomCoinsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await convertCustomCoins({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: convertCustomCoinsQueryKey(options)
  })
}

export const convertCustomCoinsMutation = (options?: Partial<Options<ConvertCustomCoinsData>>) => {
  const mutationOptions: UseMutationOptions<
    ConvertCustomCoinsResponse2,
    ConvertCustomCoinsError,
    Options<ConvertCustomCoinsData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await convertCustomCoins({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getCustomCoinsLeadersQueryKey = (options?: Options) => [
  createQueryKey('getCustomCoinsLeaders', options)
]

export const getCustomCoinsLeadersOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getCustomCoinsLeaders({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getCustomCoinsLeadersQueryKey(options)
  })
}

export const getCustomCoinEventUserInfoQueryKey = (options?: Options) => [
  createQueryKey('getCustomCoinEventUserInfo', options)
]

export const getCustomCoinEventUserInfoOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getCustomCoinEventUserInfo({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getCustomCoinEventUserInfoQueryKey(options)
  })
}

export const getHotrecordLeadersQueryKey = (options?: Options) => [
  createQueryKey('getHotrecordLeaders', options)
]

export const getHotrecordLeadersOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getHotrecordLeaders({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getHotrecordLeadersQueryKey(options)
  })
}

export const getHotrecordUserInfoQueryKey = (options?: Options) => [
  createQueryKey('getHotrecordUserInfo', options)
]

export const getHotrecordUserInfoOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getHotrecordUserInfo({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getHotrecordUserInfoQueryKey(options)
  })
}

export const getOnepercentLeadersQueryKey = (options?: Options) => [
  createQueryKey('getOnepercentLeaders', options)
]

export const getOnepercentLeadersOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getOnepercentLeaders({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getOnepercentLeadersQueryKey(options)
  })
}

export const getOnepercentUserInfoQueryKey = (options?: Options) => [
  createQueryKey('getOnepercentUserInfo', options)
]

export const getOnepercentUserInfoOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getOnepercentUserInfo({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getOnepercentUserInfoQueryKey(options)
  })
}

export const claimFarmingQueryKey = (options: Options<ClaimFarmingData>) => [
  createQueryKey('claimFarming', options)
]

export const claimFarmingOptions = (options: Options<ClaimFarmingData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimFarming({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimFarmingQueryKey(options)
  })
}

export const claimFarmingMutation = (options?: Partial<Options<ClaimFarmingData>>) => {
  const mutationOptions: UseMutationOptions<
    ClaimFarmingResponse2,
    ClaimFarmingError,
    Options<ClaimFarmingData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimFarming({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const startFarmingQueryKey = (options: Options<StartFarmingData>) => [
  createQueryKey('startFarming', options)
]

export const startFarmingOptions = (options: Options<StartFarmingData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await startFarming({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: startFarmingQueryKey(options)
  })
}

export const startFarmingMutation = (options?: Partial<Options<StartFarmingData>>) => {
  const mutationOptions: UseMutationOptions<
    StartFarmingResponse2,
    StartFarmingError,
    Options<StartFarmingData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await startFarming({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getFortuneWheelConfigQueryKey = (options?: Options) => [
  createQueryKey('getFortuneWheelConfig', options)
]

export const getFortuneWheelConfigOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getFortuneWheelConfig({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getFortuneWheelConfigQueryKey(options)
  })
}

export const spinWheelQueryKey = (options?: Options) => [createQueryKey('spinWheel', options)]

export const spinWheelOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await spinWheel({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: spinWheelQueryKey(options)
  })
}

export const spinWheelMutation = (options?: Partial<Options>) => {
  const mutationOptions: UseMutationOptions<SpinWheelResponse, SpinWheelError, Options> = {
    mutationFn: async localOptions => {
      const { data } = await spinWheel({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const claimLockedBalanceQueryKey = (options?: Options) => [
  createQueryKey('claimLockedBalance', options)
]

export const claimLockedBalanceOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimLockedBalance({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimLockedBalanceQueryKey(options)
  })
}

export const claimLockedBalanceMutation = (options?: Partial<Options>) => {
  const mutationOptions: UseMutationOptions<
    ClaimLockedBalanceResponse,
    ClaimLockedBalanceError,
    Options
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimLockedBalance({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const denyWriteOffRansomQueryKey = (options?: Options) => [
  createQueryKey('denyWriteOffRansom', options)
]

export const denyWriteOffRansomOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await denyWriteOffRansom({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: denyWriteOffRansomQueryKey(options)
  })
}

export const denyWriteOffRansomMutation = (options?: Partial<Options>) => {
  const mutationOptions: UseMutationOptions<
    DenyWriteOffRansomResponse,
    DenyWriteOffRansomError,
    Options
  > = {
    mutationFn: async localOptions => {
      const { data } = await denyWriteOffRansom({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const createReviveInvoiceByStarsQueryKey = (
  options: Options<CreateReviveInvoiceByStarsData>
) => [createQueryKey('createReviveInvoiceByStars', options)]

export const createReviveInvoiceByStarsOptions = (
  options: Options<CreateReviveInvoiceByStarsData>
) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createReviveInvoiceByStars({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: createReviveInvoiceByStarsQueryKey(options)
  })
}

export const createReviveInvoiceByStarsMutation = (
  options?: Partial<Options<CreateReviveInvoiceByStarsData>>
) => {
  const mutationOptions: UseMutationOptions<
    CreateReviveInvoiceByStarsResponse2,
    CreateReviveInvoiceByStarsError,
    Options<CreateReviveInvoiceByStarsData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await createReviveInvoiceByStars({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getReviveInfoQueryKey = (options: Options<GetReviveInfoData>) => [
  createQueryKey('getReviveInfo', options)
]

export const getReviveInfoOptions = (options: Options<GetReviveInfoData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getReviveInfo({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getReviveInfoQueryKey(options)
  })
}

export const getReviveInfoMutation = (options?: Partial<Options<GetReviveInfoData>>) => {
  const mutationOptions: UseMutationOptions<
    GetReviveInfoResponse,
    GetReviveInfoError,
    Options<GetReviveInfoData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await getReviveInfo({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const purchaseReviveQueryKey = (options: Options<PurchaseReviveData>) => [
  createQueryKey('purchaseRevive', options)
]

export const purchaseReviveOptions = (options: Options<PurchaseReviveData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await purchaseRevive({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: purchaseReviveQueryKey(options)
  })
}

export const purchaseReviveMutation = (options?: Partial<Options<PurchaseReviveData>>) => {
  const mutationOptions: UseMutationOptions<
    PurchaseReviveResponse2,
    PurchaseReviveError,
    Options<PurchaseReviveData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await purchaseRevive({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const createGameplaySessionOctetQueryKey = (
  options: Options<CreateGameplaySessionOctetData>
) => [createQueryKey('createGameplaySessionOctet', options)]

export const createGameplaySessionOctetOptions = (
  options: Options<CreateGameplaySessionOctetData>
) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createGameplaySessionOctet({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: createGameplaySessionOctetQueryKey(options)
  })
}

export const createGameplaySessionOctetMutation = (
  options?: Partial<Options<CreateGameplaySessionOctetData>>
) => {
  const mutationOptions: UseMutationOptions<
    CreateGameplaySessionOctetResponse,
    CreateGameplaySessionOctetError,
    Options<CreateGameplaySessionOctetData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await createGameplaySessionOctet({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const updateGameplaySessionOctetQueryKey = (
  options: Options<UpdateGameplaySessionOctetData>
) => [createQueryKey('updateGameplaySessionOctet', options)]

export const updateGameplaySessionOctetOptions = (
  options: Options<UpdateGameplaySessionOctetData>
) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await updateGameplaySessionOctet({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: updateGameplaySessionOctetQueryKey(options)
  })
}

export const updateGameplaySessionOctetMutation = (
  options?: Partial<Options<UpdateGameplaySessionOctetData>>
) => {
  const mutationOptions: UseMutationOptions<
    UpdateGameplaySessionOctetResponse,
    UpdateGameplaySessionOctetError,
    Options<UpdateGameplaySessionOctetData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await updateGameplaySessionOctet({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getLeagueListQueryKey = (options?: Options) => [
  createQueryKey('getLeagueList', options)
]

export const getLeagueListOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getLeagueList({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getLeagueListQueryKey(options)
  })
}

export const getLeaguesUserInfoQueryKey = (options?: Options) => [
  createQueryKey('getLeaguesUserInfo', options)
]

export const getLeaguesUserInfoOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getLeaguesUserInfo({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getLeaguesUserInfoQueryKey(options)
  })
}

export const claimLeagueRewardQueryKey = (options: Options<ClaimLeagueRewardData>) => [
  createQueryKey('claimLeagueReward', options)
]

export const claimLeagueRewardOptions = (options: Options<ClaimLeagueRewardData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimLeagueReward({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimLeagueRewardQueryKey(options)
  })
}

export const claimLeagueRewardMutation = (options?: Partial<Options<ClaimLeagueRewardData>>) => {
  const mutationOptions: UseMutationOptions<
    ClaimLeagueRewardResponse2,
    ClaimLeagueRewardError,
    Options<ClaimLeagueRewardData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimLeagueReward({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getLeaguesLeadersQueryKey = (options: Options<GetLeaguesLeadersData>) => [
  createQueryKey('getLeaguesLeaders', options)
]

export const getLeaguesLeadersOptions = (options: Options<GetLeaguesLeadersData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getLeaguesLeaders({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getLeaguesLeadersQueryKey(options)
  })
}

export const getFreeLootboxQueryKey = (options?: Options) => [
  createQueryKey('getFreeLootbox', options)
]

export const getFreeLootboxOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getFreeLootbox({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getFreeLootboxQueryKey(options)
  })
}

export const getFreeLootboxMutation = (options?: Partial<Options>) => {
  const mutationOptions: UseMutationOptions<GetFreeLootboxResponse, GetFreeLootboxError, Options> =
    {
      mutationFn: async localOptions => {
        const { data } = await getFreeLootbox({
          ...options,
          ...localOptions,
          throwOnError: true
        })
        return data
      }
    }
  return mutationOptions
}

export const openLootboxQueryKey = (options: Options<OpenLootboxData>) => [
  createQueryKey('openLootbox', options)
]

export const openLootboxOptions = (options: Options<OpenLootboxData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await openLootbox({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: openLootboxQueryKey(options)
  })
}

export const openLootboxMutation = (options?: Partial<Options<OpenLootboxData>>) => {
  const mutationOptions: UseMutationOptions<
    OpenLootboxResponse,
    OpenLootboxError,
    Options<OpenLootboxData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await openLootbox({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const claimDailyRewardQueryKey = (options: Options<ClaimDailyRewardData>) => [
  createQueryKey('claimDailyReward', options)
]

export const claimDailyRewardOptions = (options: Options<ClaimDailyRewardData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimDailyReward({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimDailyRewardQueryKey(options)
  })
}

export const claimDailyRewardMutation = (options?: Partial<Options<ClaimDailyRewardData>>) => {
  const mutationOptions: UseMutationOptions<
    ClaimDailyRewardResponse2,
    ClaimDailyRewardError,
    Options<ClaimDailyRewardData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimDailyReward({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const claimDailyTasksQueryKey = (options: Options<ClaimDailyTasksData>) => [
  createQueryKey('claimDailyTasks', options)
]

export const claimDailyTasksOptions = (options: Options<ClaimDailyTasksData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimDailyTasks({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimDailyTasksQueryKey(options)
  })
}

export const claimDailyTasksMutation = (options?: Partial<Options<ClaimDailyTasksData>>) => {
  const mutationOptions: UseMutationOptions<
    ClaimDailyTasksResponse,
    ClaimDailyTasksError,
    Options<ClaimDailyTasksData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimDailyTasks({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const claimDailyTasksLootboxQueryKey = (options?: Options) => [
  createQueryKey('claimDailyTasksLootbox', options)
]

export const claimDailyTasksLootboxOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimDailyTasksLootbox({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimDailyTasksLootboxQueryKey(options)
  })
}

export const claimDailyTasksLootboxMutation = (options?: Partial<Options>) => {
  const mutationOptions: UseMutationOptions<
    ClaimDailyTasksLootboxResponse,
    ClaimDailyTasksLootboxError,
    Options
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimDailyTasksLootbox({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getDailyTasksQueryKey = (options?: Options) => [
  createQueryKey('getDailyTasks', options)
]

export const getDailyTasksOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getDailyTasks({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getDailyTasksQueryKey(options)
  })
}

export const removePlayerFlagQueryKey = (options: Options<RemovePlayerFlagData>) => [
  createQueryKey('removePlayerFlag', options)
]

export const removePlayerFlagOptions = (options: Options<RemovePlayerFlagData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await removePlayerFlag({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: removePlayerFlagQueryKey(options)
  })
}

export const removePlayerFlagMutation = (options?: Partial<Options<RemovePlayerFlagData>>) => {
  const mutationOptions: UseMutationOptions<
    RemovePlayerFlagResponse2,
    RemovePlayerFlagError,
    Options<RemovePlayerFlagData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await removePlayerFlag({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getPlayerProfileQueryKey = (options?: Options) => [
  createQueryKey('getPlayerProfile', options)
]

export const getPlayerProfileOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getPlayerProfile({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getPlayerProfileQueryKey(options)
  })
}

export const getPlayerStateQueryKey = (options?: Options) => [
  createQueryKey('getPlayerState', options)
]

export const getPlayerStateOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getPlayerState({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getPlayerStateQueryKey(options)
  })
}

export const checkGlobalTaskQueryKey = (options: Options<CheckGlobalTaskData>) => [
  createQueryKey('checkGlobalTask', options)
]

export const checkGlobalTaskOptions = (options: Options<CheckGlobalTaskData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await checkGlobalTask({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: checkGlobalTaskQueryKey(options)
  })
}

export const checkGlobalTaskMutation = (options?: Partial<Options<CheckGlobalTaskData>>) => {
  const mutationOptions: UseMutationOptions<
    CheckGlobalTaskResponse2,
    CheckGlobalTaskError,
    Options<CheckGlobalTaskData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await checkGlobalTask({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const claimGlobalTaskQueryKey = (options: Options<ClaimGlobalTaskData>) => [
  createQueryKey('claimGlobalTask', options)
]

export const claimGlobalTaskOptions = (options: Options<ClaimGlobalTaskData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimGlobalTask({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimGlobalTaskQueryKey(options)
  })
}

export const claimGlobalTaskMutation = (options?: Partial<Options<ClaimGlobalTaskData>>) => {
  const mutationOptions: UseMutationOptions<
    ClaimGlobalTaskResponse2,
    ClaimGlobalTaskError,
    Options<ClaimGlobalTaskData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimGlobalTask({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const completeGlobalTaskQueryKey = (options: Options<CompleteGlobalTaskData>) => [
  createQueryKey('completeGlobalTask', options)
]

export const completeGlobalTaskOptions = (options: Options<CompleteGlobalTaskData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await completeGlobalTask({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: completeGlobalTaskQueryKey(options)
  })
}

export const completeGlobalTaskMutation = (options?: Partial<Options<CompleteGlobalTaskData>>) => {
  const mutationOptions: UseMutationOptions<
    CompleteGlobalTaskResponse2,
    CompleteGlobalTaskError,
    Options<CompleteGlobalTaskData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await completeGlobalTask({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getGlobalTasksQueryKey = (options?: Options) => [
  createQueryKey('getGlobalTasks', options)
]

export const getGlobalTasksOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getGlobalTasks({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getGlobalTasksQueryKey(options)
  })
}

export const getUtcQueryKey = (options?: Options) => [createQueryKey('getUtc', options)]

export const getUtcOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getUtc({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getUtcQueryKey(options)
  })
}

export const createReferralLinkQueryKey = (options: Options<CreateReferralLinkData>) => [
  createQueryKey('createReferralLink', options)
]

export const createReferralLinkOptions = (options: Options<CreateReferralLinkData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createReferralLink({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: createReferralLinkQueryKey(options)
  })
}

export const createReferralLinkMutation = (options?: Partial<Options<CreateReferralLinkData>>) => {
  const mutationOptions: UseMutationOptions<
    CreateReferralLinkResponse2,
    CreateReferralLinkError,
    Options<CreateReferralLinkData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await createReferralLink({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getReferralsListQueryKey = (options?: Options<GetReferralsListData>) => [
  createQueryKey('getReferralsList', options)
]

export const getReferralsListOptions = (options?: Options<GetReferralsListData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getReferralsList({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getReferralsListQueryKey(options)
  })
}

export const getReferralsListInfiniteQueryKey = (
  options?: Options<GetReferralsListData>
): QueryKey<Options<GetReferralsListData>> => [createQueryKey('getReferralsList', options, true)]

// export const getReferralsListInfiniteOptions = (options?: Options<GetReferralsListData>) => {
//   return infiniteQueryOptions<
//     GetReferralsListResponse,
//     GetReferralsListError,
//     InfiniteData<GetReferralsListResponse>,
//     QueryKey<Options<GetReferralsListData>>,
//     string | Pick<QueryKey<Options<GetReferralsListData>>[0], 'body' | 'headers' | 'path' | 'query'>
//   >(
//     // @ts-ignore
//     {
//       queryFn: async ({ pageParam, queryKey, signal }) => {
//         // @ts-ignore
//         const page: Pick<
//           QueryKey<Options<GetReferralsListData>>[0],
//           'body' | 'headers' | 'path' | 'query'
//         > =
//           typeof pageParam === 'object'
//             ? pageParam
//             : {
//                 query: {
//                   cursor: pageParam
//                 }
//               }
//         const params = createInfiniteParams(queryKey, page)
//         const { data } = await getReferralsList({
//           ...options,
//           ...params,
//           signal,
//           throwOnError: true
//         })
//         return data
//       },
//       queryKey: getReferralsListInfiniteQueryKey(options)
//     }
//   )
// }

export const claimReferralRewardQueryKey = (options: Options<ClaimReferralRewardData>) => [
  createQueryKey('claimReferralReward', options)
]

export const claimReferralRewardOptions = (options: Options<ClaimReferralRewardData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimReferralReward({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimReferralRewardQueryKey(options)
  })
}

export const claimReferralRewardMutation = (
  options?: Partial<Options<ClaimReferralRewardData>>
) => {
  const mutationOptions: UseMutationOptions<
    ClaimReferralRewardResponse2,
    ClaimReferralRewardError,
    Options<ClaimReferralRewardData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimReferralReward({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const createInvoiceQueryKey = (options: Options<CreateInvoiceData>) => [
  createQueryKey('createInvoice', options)
]

export const createInvoiceOptions = (options: Options<CreateInvoiceData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createInvoice({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: createInvoiceQueryKey(options)
  })
}

export const createInvoiceMutation = (options?: Partial<Options<CreateInvoiceData>>) => {
  const mutationOptions: UseMutationOptions<
    CreateInvoiceResponse2,
    CreateInvoiceError,
    Options<CreateInvoiceData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await createInvoice({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getShopItemsQueryKey = (options?: Options) => [createQueryKey('getShopItems', options)]

export const getShopItemsOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getShopItems({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getShopItemsQueryKey(options)
  })
}

export const purchaseQueryKey = (options: Options<PurchaseData>) => [
  createQueryKey('purchase', options)
]

export const purchaseOptions = (options: Options<PurchaseData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await purchase({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: purchaseQueryKey(options)
  })
}

export const purchaseMutation = (options?: Partial<Options<PurchaseData>>) => {
  const mutationOptions: UseMutationOptions<
    PurchaseResponse2,
    PurchaseError,
    Options<PurchaseData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await purchase({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const purchaseEventBoosterQueryKey = (options: Options<PurchaseEventBoosterData>) => [
  createQueryKey('purchaseEventBooster', options)
]

export const purchaseEventBoosterOptions = (options: Options<PurchaseEventBoosterData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await purchaseEventBooster({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: purchaseEventBoosterQueryKey(options)
  })
}

export const purchaseEventBoosterMutation = (
  options?: Partial<Options<PurchaseEventBoosterData>>
) => {
  const mutationOptions: UseMutationOptions<
    PurchaseEventBoosterResponse2,
    PurchaseEventBoosterError,
    Options<PurchaseEventBoosterData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await purchaseEventBooster({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const purchaseJackpotCouponsQueryKey = (options: Options<PurchaseJackpotCouponsData>) => [
  createQueryKey('purchaseJackpotCoupons', options)
]

export const purchaseJackpotCouponsOptions = (options: Options<PurchaseJackpotCouponsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await purchaseJackpotCoupons({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: purchaseJackpotCouponsQueryKey(options)
  })
}

export const purchaseJackpotCouponsMutation = (
  options?: Partial<Options<PurchaseJackpotCouponsData>>
) => {
  const mutationOptions: UseMutationOptions<
    PurchaseJackpotCouponsResponse2,
    PurchaseJackpotCouponsError,
    Options<PurchaseJackpotCouponsData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await purchaseJackpotCoupons({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const purchaseProgressiveQueryKey = (options: Options<PurchaseProgressiveData>) => [
  createQueryKey('purchaseProgressive', options)
]

export const purchaseProgressiveOptions = (options: Options<PurchaseProgressiveData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await purchaseProgressive({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: purchaseProgressiveQueryKey(options)
  })
}

export const purchaseProgressiveMutation = (
  options?: Partial<Options<PurchaseProgressiveData>>
) => {
  const mutationOptions: UseMutationOptions<
    PurchaseProgressiveResponse,
    PurchaseProgressiveError,
    Options<PurchaseProgressiveData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await purchaseProgressive({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const purchasePuzzleQueryKey = (options: Options<PurchasePuzzleData>) => [
  createQueryKey('purchasePuzzle', options)
]

export const purchasePuzzleOptions = (options: Options<PurchasePuzzleData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await purchasePuzzle({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: purchasePuzzleQueryKey(options)
  })
}

export const purchasePuzzleMutation = (options?: Partial<Options<PurchasePuzzleData>>) => {
  const mutationOptions: UseMutationOptions<
    PurchasePuzzleResponse,
    PurchasePuzzleError,
    Options<PurchasePuzzleData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await purchasePuzzle({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const purchaseRansomQueryKey = (options?: Options) => [
  createQueryKey('purchaseRansom', options)
]

export const purchaseRansomOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await purchaseRansom({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: purchaseRansomQueryKey(options)
  })
}

export const purchaseRansomMutation = (options?: Partial<Options>) => {
  const mutationOptions: UseMutationOptions<PurchaseRansomResponse, PurchaseRansomError, Options> =
    {
      mutationFn: async localOptions => {
        const { data } = await purchaseRansom({
          ...options,
          ...localOptions,
          throwOnError: true
        })
        return data
      }
    }
  return mutationOptions
}

export const checkTonInvoiceQueryKey = (options: Options<CheckTonInvoiceData>) => [
  createQueryKey('checkTonInvoice', options)
]

export const checkTonInvoiceOptions = (options: Options<CheckTonInvoiceData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await checkTonInvoice({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: checkTonInvoiceQueryKey(options)
  })
}

export const checkTonInvoiceMutation = (options?: Partial<Options<CheckTonInvoiceData>>) => {
  const mutationOptions: UseMutationOptions<
    CheckTonInvoiceResponse2,
    CheckTonInvoiceError,
    Options<CheckTonInvoiceData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await checkTonInvoice({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const claimSkinQueryKey = (options: Options<ClaimSkinData>) => [
  createQueryKey('claimSkin', options)
]

export const claimSkinOptions = (options: Options<ClaimSkinData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimSkin({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimSkinQueryKey(options)
  })
}

export const claimSkinMutation = (options?: Partial<Options<ClaimSkinData>>) => {
  const mutationOptions: UseMutationOptions<
    ClaimSkinResponse2,
    ClaimSkinError,
    Options<ClaimSkinData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimSkin({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getSkinsQueryKey = (options?: Options) => [createQueryKey('getSkins', options)]

export const getSkinsOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getSkins({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getSkinsQueryKey(options)
  })
}

export const purchaseSkinQueryKey = (options: Options<PurchaseSkinData>) => [
  createQueryKey('purchaseSkin', options)
]

export const purchaseSkinOptions = (options: Options<PurchaseSkinData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await purchaseSkin({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: purchaseSkinQueryKey(options)
  })
}

export const purchaseSkinMutation = (options?: Partial<Options<PurchaseSkinData>>) => {
  const mutationOptions: UseMutationOptions<
    PurchaseSkinResponse2,
    PurchaseSkinError,
    Options<PurchaseSkinData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await purchaseSkin({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const selectSkinQueryKey = (options: Options<SelectSkinData>) => [
  createQueryKey('selectSkin', options)
]

export const selectSkinOptions = (options: Options<SelectSkinData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await selectSkin({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: selectSkinQueryKey(options)
  })
}

export const selectSkinMutation = (options?: Partial<Options<SelectSkinData>>) => {
  const mutationOptions: UseMutationOptions<
    SelectSkinResponse2,
    SelectSkinError,
    Options<SelectSkinData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await selectSkin({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getUserProfileQueryKey = (options: Options<GetUserProfileData>) => [
  createQueryKey('getUserProfile', options)
]

export const getUserProfileOptions = (options: Options<GetUserProfileData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getUserProfile({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getUserProfileQueryKey(options)
  })
}

export const getAssetsQueryKey = (options?: Options) => [createQueryKey('getAssets', options)]

export const getAssetsOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getAssets({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getAssetsQueryKey(options)
  })
}

export const connectWalletQueryKey = (options: Options<ConnectWalletData>) => [
  createQueryKey('connectWallet', options)
]

export const connectWalletOptions = (options: Options<ConnectWalletData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await connectWallet({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: connectWalletQueryKey(options)
  })
}

export const connectWalletMutation = (options?: Partial<Options<ConnectWalletData>>) => {
  const mutationOptions: UseMutationOptions<
    ConnectWalletResponse2,
    ConnectWalletError,
    Options<ConnectWalletData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await connectWallet({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const disconnectWalletQueryKey = (options: Options<DisconnectWalletData>) => [
  createQueryKey('disconnectWallet', options)
]

export const disconnectWalletOptions = (options: Options<DisconnectWalletData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await disconnectWallet({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: disconnectWalletQueryKey(options)
  })
}

export const disconnectWalletMutation = (options?: Partial<Options<DisconnectWalletData>>) => {
  const mutationOptions: UseMutationOptions<
    DisconnectWalletResponse2,
    DisconnectWalletError,
    Options<DisconnectWalletData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await disconnectWallet({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getTransactionsHistoryQueryKey = (options?: Options) => [
  createQueryKey('getTransactionsHistory', options)
]

export const getTransactionsHistoryOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getTransactionsHistory({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getTransactionsHistoryQueryKey(options)
  })
}

export const cancelWithdrawQueryKey = (options: Options<CancelWithdrawData>) => [
  createQueryKey('cancelWithdraw', options)
]

export const cancelWithdrawOptions = (options: Options<CancelWithdrawData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await cancelWithdraw({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: cancelWithdrawQueryKey(options)
  })
}

export const cancelWithdrawMutation = (options?: Partial<Options<CancelWithdrawData>>) => {
  const mutationOptions: UseMutationOptions<
    CancelWithdrawResponse2,
    CancelWithdrawError,
    Options<CancelWithdrawData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await cancelWithdraw({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const claimWithdrawTaskQueryKey = (options: Options<ClaimWithdrawTaskData>) => [
  createQueryKey('claimWithdrawTask', options)
]

export const claimWithdrawTaskOptions = (options: Options<ClaimWithdrawTaskData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await claimWithdrawTask({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: claimWithdrawTaskQueryKey(options)
  })
}

export const claimWithdrawTaskMutation = (options?: Partial<Options<ClaimWithdrawTaskData>>) => {
  const mutationOptions: UseMutationOptions<
    ClaimWithdrawTaskResponse,
    ClaimWithdrawTaskError,
    Options<ClaimWithdrawTaskData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await claimWithdrawTask({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const createWithdrawQueryKey = (options: Options<CreateWithdrawData>) => [
  createQueryKey('createWithdraw', options)
]

export const createWithdrawOptions = (options: Options<CreateWithdrawData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createWithdraw({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: createWithdrawQueryKey(options)
  })
}

export const createWithdrawMutation = (options?: Partial<Options<CreateWithdrawData>>) => {
  const mutationOptions: UseMutationOptions<
    CreateWithdrawResponse2,
    CreateWithdrawError,
    Options<CreateWithdrawData>
  > = {
    mutationFn: async localOptions => {
      const { data } = await createWithdraw({
        ...options,
        ...localOptions,
        throwOnError: true
      })
      return data
    }
  }
  return mutationOptions
}

export const getWithdrawHistoryQueryKey = (options?: Options) => [
  createQueryKey('getWithdrawHistory', options)
]

export const getWithdrawHistoryOptions = (options?: Options) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getWithdrawHistory({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true
      })
      return data
    },
    queryKey: getWithdrawHistoryQueryKey(options)
  })
}
